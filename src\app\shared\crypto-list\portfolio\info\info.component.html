<select
  [ngModel]="searchCoin().name"
  class="selectCoin"
  (ngModelChange)="onChangeCrypo($event)"
  >
  @for (coin of portfolio.sortRank; track coin) {
    <option
      [value]="coin.name"
      [selected]="searchCoin().name == coin.name"
      >
      {{ coin.name + " (" + coin.ticker + ")" }}
    </option>
  }
</select>

<div class="crypto-list-table">
  <div class="crypto-list-table-logo">
    <img src="{{ searchCoin()?.logo }}" />
  </div>
  <div class="crypto-list-table-name">
    {{ searchCoin()?.name + " " }}
    @if(searchCoin()?.rating?.level){
      <div
        class="crypto-list-table-name-rating"
        [ngStyle]="{ background: ratingBackground(searchCoin()?.rating?.level) }"
        >
        {{ searchCoin()?.rating?.level }}
      </div>
    }
  </div>

  <div class="crypto-list-table-ticker">
    {{ searchCoin()?.ticker }}
  </div>

  <div class="crypto-list-table-info">
    @if(searchCoin()?.rating?.level){
      <div
        class="crypto-list-table-info-rating"
        [ngStyle]="{ color: ratingBackground(searchCoin()?.rating?.level) }"
        >
        <div class="security">
          <div class="icon"><i class="fa-solid fa-lock"></i></div>
          <b> Security: {{ searchCoin()?.rating?.security | ratingLevel }}</b>
        </div>
        <div class="progress">
          <div class="icon"><i class="fa-solid fa-list-ul"></i></div>
          <b> Progress: {{ searchCoin()?.rating?.progress | ratingLevel }}</b>
        </div>
        <div class="economics">
          <div class="icon">
            <i class="fa-solid fa-magnifying-glass-dollar"></i>
          </div>
          <b> Economics: {{ searchCoin()?.rating?.economics | ratingLevel }}</b>
        </div>
        <div class="performance">
          <div class="icon"><i class="fa-solid fa-chart-line"></i></div>
          <b>
            Performance: {{ searchCoin()?.rating?.performance | ratingLevel }}</b
            >
          </div>

          <div class="ratingDescription">
            {{ findDescription(searchCoin()?.rating?.level) }}
          </div>
        </div>
        } @if(searchCoin().description) {
        <div [innerHTML]="searchCoin()?.description"></div>
      }
    </div>

    <div class="crypto-list-table-category">
      {{ searchCoin()?.category }}
    </div>
    <div class="crypto-list-table-marketCap">
      {{ searchCoin()?.marketCap | shortNumber }}
    </div>
  </div>
  <!--
  <app-crypto-info
    [coin]="coin"
    [showInfo]="showInfo"
    [currentCoin]="currentCoin"
  ></app-crypto-info> -->
