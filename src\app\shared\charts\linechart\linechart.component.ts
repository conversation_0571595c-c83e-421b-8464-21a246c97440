import { Component, Input, ViewChild, computed, effect } from '@angular/core';
import { ApexOptions } from 'apexcharts';
import { ChartComponent } from 'ng-apexcharts';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-linechart',
  templateUrl: './linechart.component.html',
  styleUrls: ['./linechart.component.scss'],
})
export class LinechartComponent {
  @Input('minY') minY: number;
  @Input('maxY') maxY: number;
  @ViewChild('chart', { static: false }) chart: ChartComponent;
  public chartOptions: Partial<ApexOptions>;
  protected currentExchange = this.currentAccountService.currentAccount;

  protected portfolioHistory = computed(() => {
    return (
      this.currentAccountService
        .currentPortfolioHistory()
        .portfolioMonthly.sort((a, b) => {
          return new Date(a.date).getTime() - new Date(b.date).getTime();
        }) || []
    );
  });

  constructor(private currentAccountService: CurrentAccountService) {
    this.createLineChart();
    effect(() =>
      this.currentAccountService.currentPortfolioHistory()
        ? this.createLineChart()
        : null,
    );
  }

  createLineChart() {
    const generateColors = (data) => {
      return data.map((d, idx) => {
        let color = d > 0 ? '#22c55f' : '#ef4544';

        return {
          offset: (idx / (data.length - 1)) * 100,
          color,
          opacity: 1,
        };
      });
    };

    let portfolioHistory = this.portfolioHistory;
    let currentExchange = this.currentExchange;
    this.chartOptions = {
      chart: {
        type: 'line',
        // background: 'rgb(10, 10, 10)',
        width: '100%',
        height: '300px',
        foreColor: '#fff',
        fontFamily: 'Roboto, Arial, sans-serif',
        zoom: { autoScaleYaxis: true },
        toolbar: {
          show: false,
          offsetX: 0,
          offsetY: 0,
          tools: {
            download: false,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: false,
            customIcons: [],
          },
          export: {
            svg: {
              filename: undefined,
            },
            png: {
              filename: undefined,
            },
          },
          autoSelected: 'zoom',
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          opacity: 0.2,
        },
      },

      stroke: {
        width: 2,
        curve: 'smooth',
      },
      fill: {
        // colors: ['', '', '#fff'],
        // type: 'gradient',
        // gradient: {
        //   type: 'vertical',
        //   shadeIntensity: 1,
        //   opacityFrom: 1,
        //   opacityTo: 1,
        //   // gradientToColors: ['', '', ''], // green
        //   stops: [
        //     generateColors(
        //       this.portfolioHistory.portfolioMonthly.map(
        //         (history) => history.profit
        //       )
        //     ),
        //   ],
        // },
      },

      markers: {
        // size: 3,
        // strokeWidth: 1,
        hover: {
          size: 6,
        },
      },

      grid: {
        show: true,
        borderColor: '#424754',
        padding: {
          bottom: 40,
          right: 32,
          left: -5,
        },
        xaxis: {
          lines: {
            show: false,
          },
        },
      },

      colors: [
        '#1e38fc',
        '#2196F3',
        function () {
          return portfolioHistory()[portfolioHistory().length - 1].profit > 0
            ? 'var(--green-profit)'
            : '#ed451f';
        },

        // console.log('VALUE', value);
        // // return portfolioHistory.portfolioMonthly[w.globals.seriesLog[2]]
        // //   .profit > 0
        // //   ? 'var(--green-profit)'
        // //   : '#ed451f';
      ],
      series: [
        {
          name: 'Deposit',
          data: this.portfolioHistory().map((history) => history.deposits),
        },
        {
          name: 'Current',
          data: this.portfolioHistory().map((history) => history.current),
        },
        {
          name: 'Profit',
          data: this.portfolioHistory().map((history) => history.profit),
        },
      ],
      legend: {
        show: true,
        offsetY: -10,
        markers: {
          fillColors: [
            '#1e38fc',
            '#2196F3',
            portfolioHistory()[portfolioHistory().length - 1].profit > 0
              ? 'var(--green-profit)'
              : '#ed451f',
            // function () {
            //   return portfolioHistory().portfolioMonthly[
            //     portfolioHistory().portfolioMonthly.length - 1
            //   ].profit > 0
            //     ? 'var(--green-profit)'
            //     : '#ed451f';
            // },
          ],
          shape: 'circle',
          strokeWidth: 0,
        },
      },
      xaxis: {
        type: 'datetime',
        categories: this.portfolioHistory().map((history) => {
          let newDate = new Date(history.date);
          const year = newDate.getFullYear();
          const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
          const day = newDate.getDate().toString().padStart(2, '0');

          return `${year}/${month}/${day}`;
        }),
        tickAmount: 1,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Roboto, Arial, sans-serif',
          },
          offsetY: 2,
          rotateAlways: false,
          hideOverlappingLabels: true,
          datetimeUTC: true,
          datetimeFormatter: {
            year: 'yyyy',
            month: '',
            // month: "MMM 'yy",
            // day: 'dd MMM',
            // hour: 'HH:mm',
          },
        },
      },
      yaxis: {
        decimalsInFloat: 0,
        max: this.maxY,
        min: currentExchange() === 'binance-elisa' ? -1000 : this.minY,
        showAlways: false,
        forceNiceScale: true,
        labels: {
          offsetX: -15,
          style: {
            fontSize: '11px',
            fontFamily: 'Roboto, Arial, sans-serif',
          },
          formatter: (value) => {
            return this.shortNumbers(value) + ' €';
          },
        },
      },
      tooltip: {
        // x: {
        //   format: 'dd/MM/yy',
        // },
        theme: 'dark',
      },
      annotations: {
        xaxis: [],
        points: [
          {
            x: this.portfolioHistory()[this.portfolioHistory().length - 1].DATE,
            y: this.portfolioHistory()[this.portfolioHistory().length - 1]
              .current,
            marker: {
              size: 4,
              fillColor: '#2196F3',
              strokeColor: '#fff',
              cssClass: 'apexcharts-custom-class',
            },
            label: {
              borderColor: 'transparent',
              offsetY: 15,
              offsetX: 20,
              style: {
                color: '#fff',
                background: 'transparent',
                fontSize: '12px',
              },
              text: `${this.shortNumbers(
                this.portfolioHistory()[
                  this.portfolioHistory().length - 1
                ].current.toFixed(0),
              )}`,
            },
          },
          {
            x: this.portfolioHistory()[this.portfolioHistory().length - 1].DATE,
            y: this.portfolioHistory()[
              this.portfolioHistory().length - 1
            ].deposits.toFixed(0),
            marker: {
              size: 4,
              fillColor: '#3F51B5',
              strokeColor: '#fff',
              cssClass: 'apexcharts-custom-class',
            },
            label: {
              borderColor: 'transparent',
              offsetY: 15,
              offsetX: 20,
              style: {
                color: '#fff',
                background: 'transparent',
                fontSize: '12px',
              },
              text: `${this.shortNumbers(
                this.portfolioHistory()[
                  this.portfolioHistory().length - 1
                ].deposits.toFixed(0),
              )}`,
            },
          },
          {
            x: this.portfolioHistory()[this.portfolioHistory().length - 1].DATE,
            y: this.portfolioHistory()[this.portfolioHistory().length - 1]
              .profit,
            marker: {
              size: 4,
              fillColor:
                portfolioHistory()[portfolioHistory().length - 1].profit > 0
                  ? 'var(--green-profit)'
                  : '#ed451f',
              strokeColor: '#fff',
              cssClass: 'apexcharts-custom-class',
            },
            label: {
              borderColor: 'transparent',
              offsetY: 15,
              offsetX: 20,
              style: {
                color: '#fff',
                background: 'transparent',
                fontSize: '12px',
                cssClass: 'apexcharts-yaxis-annotation-label',
              },

              text: `${this.shortNumbers(
                this.portfolioHistory()[
                  this.portfolioHistory().length - 1
                ].profit.toFixed(0),
              )} `,
            },
          },
          // {
          //   x: new Date('08 Dec 2017').getTime(),
          //   y: 9340.85,
          //   marker: {
          //     size: 0,
          //   },
          //   image: {
          //     path: '../../assets/images/ico-instagram.png',
          //   },
          // },
        ],
      },
      dataLabels: {
        enabled: false,
        enabledOnSeries: undefined,
      },
    };
  }

  ngAfterViewInit() {}

  shortNumbers(number: number) {
    if (isNaN(number)) return null; // will only work value is a number
    if (number === null) return null;
    if (number === 0) return 0;
    let abs = Math.abs(number);
    const rounder = Math.pow(10, 1);
    const isNegative = number < 0; // will also work for Negative numbers
    let key = '';

    const powers = [
      { key: ' Q', value: Math.pow(10, 15) },
      { key: ' T', value: Math.pow(10, 12) },
      { key: ' B', value: Math.pow(10, 9) },
      { key: ' M', value: Math.pow(10, 6) },
      { key: 'k', value: 1000 },
    ];

    for (let i = 0; i < powers.length; i++) {
      let reduced = abs / powers[i].value;
      reduced = Math.round(reduced * rounder) / rounder;
      if (reduced >= 1) {
        abs = reduced;
        key = powers[i].key;
        break;
      }
    }
    return (isNegative ? '-' : '') + Math.round(abs) + key;
  }

  finalDate() {
    let finalDate =
      this.portfolioHistory()[this.portfolioHistory().length - 2].date;

    let newDate = new Date(finalDate);
    const year = newDate.getFullYear();
    const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
    const day = newDate.getDate().toString().padStart(2, '0');

    // return `${year}/${month}/${day}`;
    return newDate.getTime();
  }

  shouldFetchData() {
    const storedDate = localStorage.getItem('date');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
