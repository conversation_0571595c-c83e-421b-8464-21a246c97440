import { formatCurrency } from '@angular/common';
import {
  Component,
  effect,
  ElementRef,
  input,
  Input,
  signal,
  viewChild,
} from '@angular/core';
import { ICoin } from 'src/app/core/interfaces/coins';

@Component({
  selector: 'app-crypto-info',
  templateUrl: './crypto-info.component.html',
  styleUrls: ['./crypto-info.component.scss'],
})
export class CryptoInfoComponent {
  coin = input.required<ICoin>();
  showInfo = input.required<boolean>();
  @Input() currentCoin: any;

  protected priceInput: string;
  protected priceInputNumber: number;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected profits: string;
  protected script: HTMLScriptElement;
  protected selectedTab = signal<'chart' | 'info'>('chart');
  protected container = viewChild<ElementRef>('container');

  protected showInfoEffect = effect(() => {
    if (this.showInfo()) {
      if (!!this.container()) {
        this.script = document.createElement('script');
        this.script.src =
          'https://s3.tradingview.com/external-embedding/embed-widget-symbol-overview.js';
        this.script.type = 'text/javascript';
        this.script.async = true;
        this.script.innerHTML = `
         {
          "symbols": [
              [
              "COINBASE:${this.coin().ticker.toUpperCase()}USD|60"
              ],
              [
              "BINANCE:${this.coin().ticker.toUpperCase()}USD|60"
              ], 
              ["BITFINEX:${this.coin().ticker.toUpperCase()}USD|60"]
             ],
          "chartOnly": true,
          "width": "100%",
          "height": "100%",
          "locale": "en",
          "colorTheme": "dark",
          "autosize": true,
          "showVolume": false,
          "showMA": false,
          "hideDateRanges": false,
          "hideMarketStatus": false,
          "hideSymbolLogo": true,
          "scalePosition": "right",
          "scaleMode": "Normal",
          "fontFamily": "-apple-system, BlinkMacSystemFont, Trebuchet MS, Roboto, Ubuntu, sans-serif",
          "fontSize": "12",
          "noTimeScale": false,
          "valuesTracking": "1",
          "changeMode": "price-and-percent",
          "chartType": "area",
          "maLineColor": "#2962FF",
          "maLineWidth": 1,
          "maLength": 9,
          "headerFontSize": "medium",
          "lineWidth": 2,
          "lineType": 0,
          "dateRanges": [
            "1d|1",
            "1m|30",
            "3m|60",
            "12m|1D",
            "60m|1W",
            "all|1M"
          ], 
          "upColor": "#22ab94",
          "downColor": "#f7525f",
          "borderUpColor": "#22ab94",
          "borderDownColor": "#f7525f",
          "wickUpColor": "#22ab94",
          "wickDownColor": "#f7525f"

        }`;

        console.log('container', this.container());
        this.container().nativeElement.appendChild(this.script);
      }
    }
  });

  onInputChange(element) {
    let elementNumber: any;
    if (element.includes(',')) {
      this.priceInputNumber = element.replaceAll('.', '').replace(',', '.');

      return;
    }

    elementNumber = element.replaceAll('.', '');
    this.priceInputNumber = elementNumber;

    let formattedValue = elementNumber.replaceAll('.', '');

    this.priceInput = formatCurrency(
      formattedValue,
      'it-IT',
      '',
      'EUR',
      '0.0-2',
    );
  }

  onInputCompleted(element) {
    this.priceInput = '€ ' + element.target.value;
  }

  coinPrice(price) {
    if (price === 0) return '';
    let part1 = price.toString().split('.')[0];
    let part2 = price.toString().split('.')[1];

    if (part1 == 0 && part2[0] == 0)
      return formatCurrency(price, 'it-IT', '', 'EUR', '0.2-5') + ' €';
    else if (part1.length > 3) {
      return formatCurrency(price, 'it-IT', '', 'EUR', '0.0-0') + ' €';
    } else {
      return formatCurrency(price, 'it-IT', '', 'EUR', '0.2-2') + ' €';
    }
  }
}
