.container {
  padding: 1rem;

  .info {
    display: flex;
    width: 100%;
    margin-bottom: 1rem;
    // background-color: #005382;
    border-radius: 10px;
    border: 1px solid gray;

    &-text {
      display: flex;
      justify-content: space-around;
      font-size: 1.6rem;
      width: 100%;

      & .deposits {
        display: flex;
        justify-content: start;
        align-items: center;
        padding: 1rem 0.5rem;
        border-radius: 10px;
        width: auto;
        letter-spacing: 0.2px;
      }
    }

    & select {
      font-size: 1.6rem;
      padding: 1rem 0.5rem;
      border-radius: 10px;
      border: none;
      background-color: black;
      color: #fff;
      width: 122px;
      text-align: center;
      margin-right: 1rem;
      cursor: pointer;
    }
  }

  .search {
    select {
      width: 100%;
      padding: 1rem;
      background-color: lightgray;
      color: black;
      border: 1px solid lightgray;
      margin-bottom: 1rem;
      border-radius: 10px;
      cursor: pointer;

      &:focus {
        outline: none;
      }
    }
  }

  & .card {
    margin-bottom: 4rem;
    &-date {
      font-size: 1.6rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      margin-left: 0.5rem;
      color: #c7c7c7;
      font-size: 1.6rem;
    }

    &-grid {
      padding: 1rem;
      display: grid;
      grid-template-columns: 38px 32% auto 27%;
      grid-template-rows: 50% 50%;
      width: 100%;
      background-color: #1f1f1f;
      border-radius: 10px;
      font-size: 1.6rem;
      margin-bottom: 1rem;

      & .logo {
        grid-column: 1/2;
        grid-row: 1/3;
        justify-self: center;
        align-self: center;
        width: 100%;

        & img {
          width: 100%;
          height: 100%;
          margin-top: 0.3rem;
          // margin-left: 0.2rem;
        }
      }

      & .name {
        grid-row: 1/2;
        grid-column: 2/3;
        justify-self: start;
        align-self: end;
        padding-left: 0.7rem;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 100%;
      }

      & .ticker {
        grid-row: 2/3;
        grid-column: 2/3;
        justify-self: start;
        align-self: start;
        padding-left: 0.7rem;
        color: #8c8c8c;
        font-size: 1.4rem;
      }

      & .deposit {
        grid-column: 4/5;
        grid-row: 1/2;
        color: rgb(0, 188, 0);
        font-weight: 500;
        align-self: end;
        justify-self: end;
        // font-size: 1.8rem;
      }

      & .fees {
        color: darkorange;
        grid-column: 4/5;
        grid-row: 2/3;
        justify-self: end;
        align-self: start;
        font-size: 1.4rem;
      }

      & .quantity {
        grid-row: 1/2;
        grid-column: 3/4;
        justify-self: end;
        align-self: end;
        color: #b1b1b1;
        // font-size: 1.4rem;
      }

      & .avgPrice {
        grid-column: 3/4;
        grid-row: 2/3;
        justify-self: end;
        align-self: start;
        color: #008fe4;
        font-size: 1.4rem;
      }
    }
  }
}

@media (min-width: 900px) {
  .container {
    // margin: 0% 20%;
    margin-top: 2rem;

    & .info-text {
      justify-content: start;
    }
  }
}

@media (min-width: 1200px) {
  .container {
    // margin: 0% 30%;

    & .info-text {
      justify-content: start;
    }
  }
}
