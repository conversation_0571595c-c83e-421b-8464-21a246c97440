<div class="home-desktop">
  <!-- Summary card -->
  <div class="home">
    <div class="home-desktop-1">
      <div
        class="card"
        #card
        [@flipCard]="flipState"
        (touchstart)="onTouchStart($event)"
        (touchmove)="onTouchMove($event)"
        [ngClass]="{ 'is-flipped': flipState == 'back' }"
        >
        <div class="card-inner">
          <div class="card-front">
            <div class="card-logo">
              <div class="card-logo-img1">
                @if (currentExchange() == 'pac') {
                  <img
                    src="/assets/img/logo/btc.png"
                    alt="PAC"
                  [ngStyle]="{
                    width: '40px',
                    marginRight: '-0.7rem'
                  }"
                    />
                }
                @if (currentExchange() == 'pac') {
                  <img
                    src="/assets/img/logo/eth.png"
                    alt="PAC"
                    [ngStyle]="{ width: '40px', marginRight: '-0.7rem' }"
                    />
                }
                @if (currentExchange() == 'pac') {
                  <img
                    src="/assets/img/logo/sol.png"
                    alt="PAC"
                    [ngStyle]="{ width: '40px' }"
                    />
                }
              </div>
              <div class="card-logo-img2">
                @if (currentExchange() == 'coinbase-fra') {
                  <img
                    src="/assets/img/logo/coinbase-logo.png"
                    alt="Coinbase"
                    [ngStyle]="{ width: '150px' }"
                    />
                }
              </div>
              <div class="card-logo-img2">
                @if (
                  currentExchange() == 'binance-fra' ||
                  currentExchange() == 'binance-andrea' ||
                  currentExchange() == 'binance-elisa'
                  ) {
                  <img
                    src="/assets/img/logo/binance-logo.png"
                    alt="Binance"
                    [ngStyle]="{ width: '150px' }"
                    />
                }
              </div>

              @if (
                currentUser()?.username !== "elisa" &&
                currentPortfolio()?.portfolioAverageScore.average != 30
                ) {
                <div
                  class="ratingAccount"
                  [ngStyle]="{
                    background: ratingBackground(
                      currentPortfolio()?.portfolioAverageScore?.rating
                    )
                  }"
                  [ngClass]="{ exchange: currentAccount() != 'pac' }"
                  >
                  {{ currentPortfolio()?.portfolioAverageScore?.rating }}
                </div>
              }

              <!-- <img
              src="/assets/img/logo/crypto-com-logo.png"
              alt="Crypto.com"
              [ngStyle]="{ width: '160px' }"
              *ngIf="currentExchange == 'crypto-fra'"
              /> -->
            </div>
            <div class="home-balance">
              <div class="home-balance-title">Balance</div>
              <div class="home-balance-current">
                {{
                currentPortfolio()?.portfolioStats?.current
                | depositsNoDecimal
                }}
                <div
                  class="home-balance-current-gain"
                  [ngStyle]="{
                    backgroundColor:
                      currentPortfolio()?.portfolioStats?.profits > 0
                        ? 'rgba(0, 100, 0, 0.5)'
                        : 'rgba(100, 0, 0, 0.5)'
                  }"
                  >
                  <div class="home-balance-current-gain-icon">
                    @if (currentPortfolio()?.portfolioStats?.profits > 0) {
                      <i
                        class="fa-solid fa-caret-up"
                        [ngStyle]="{ color: '#04dc00' }"
                      ></i>
                    }
                    <div
                      class="home-balance-current-gain-icon-down"
                      [ngStyle]="{ marginBottom: '1.2rem' }"
                      >
                      @if (currentPortfolio()?.portfolioStats?.profits < 0) {
                        <i
                          class="fa-solid fa-caret-down"
                        [ngStyle]="{
                          color: 'red'
                        }"
                        ></i>
                      }
                    </div>
                  </div>
                  <div
                    class="home-balance-current-gain-number"
                    [ngStyle]="{
                      color:
                        currentPortfolio()?.portfolioStats?.profits > 0
                          ? '#04dc00'
                          : 'red'
                    }"
                    >
                    {{
                    currentPortfolio()?.portfolioStats?.profitsPerc
                    | profitsPerc
                    }}
                  </div>
                </div>
              </div>
              <div class="home-balance-profit-title">Profit</div>
              <div
                class="home-balance-profit"
                [ngStyle]="{
                  color:
                    currentPortfolio()?.portfolioStats?.profits > 0
                      ? '#04dc00'
                      : 'red'
                }"
                >
                {{
                currentPortfolio()?.portfolioStats?.profits | profitsNoDecimal
                }}
              </div>
            </div>
            <div class="home-deposits">
              <div class="home-deposits-title">Deposits</div>
              <div class="home-deposits-current">
                {{
                currentPortfolio()?.portfolioStats?.totalDeposits
                | depositsNoDecimal
                }}
              </div>
              <div class="home-deposits-fees-title">Fees</div>
              <div class="home-deposits-fees-number">
                -
                {{
                currentPortfolio()?.portfolioStats?.totalFees
                | depositsNoDecimal
                }}
              </div>
            </div>
            <div class="home-extra-card">
              <span>Start: {{ getStartDate() | date: "MM/yyyy" }} </span>
              <span>
                Inflation:
                {{
                currentPortfolioHistory()?.getTotalInflation().toFixed(2)
                | formatNumberIt
                }}% (-
                {{
                (currentPortfolio()?.portfolioStats?.totalDeposits *
                currentPortfolioHistory()?.getTotalInflation()) /
                100 | depositsNoDecimal
                }})
              </span>
              <!-- <h3>
              Real profit:
              {{
              currentPortfolio()?.portfolioStats?.profitsPerc -
              currentPortfolioHistory()?.getTotalInflation() | profitsPerc
              }}
            </h3> -->
          </div>
        </div>

        <div class="card-back">
          <div class="card-logo">
            <div class="card-logo-img1">
              @if (currentExchange() == 'pac') {
                <img
                  src="/assets/img/logo/btc.png"
                  alt="PAC"
                  [ngStyle]="{
                    width: '40px',
                    marginRight: '-0.7rem'
                  }"
                  />
              }
              @if (currentExchange() == 'pac') {
                <img
                  src="/assets/img/logo/eth.png"
                  alt="PAC"
                  [ngStyle]="{ width: '40px', marginRight: '-0.7rem' }"
                  />
              }
              @if (currentExchange() == 'pac') {
                <img
                  src="/assets/img/logo/sol.png"
                  alt="PAC"
                  [ngStyle]="{ width: '40px' }"
                  />
              }
            </div>
            <div class="card-logo-img2">
              @if (currentExchange() == 'coinbase-fra') {
                <img
                  src="/assets/img/logo/coinbase-logo.png"
                  alt="Coinbase"
                  [ngStyle]="{ width: '150px' }"
                  />
              }
            </div>
            <div class="card-logo-img2">
              @if (
                currentExchange() == 'binance-fra' ||
                currentExchange() == 'binance-andrea' ||
                currentExchange() == 'binance-elisa'
                ) {
                <img
                  src="/assets/img/logo/binance-logo.png"
                  alt="Binance"
                  [ngStyle]="{ width: '150px' }"
                  />
              }
            </div>

            @if (currentPortfolio()?.portfolioAverageScore?.average != 30) {
              <div
                class="ratingAccount"
                  [ngStyle]="{
                    background: ratingBackground(
                      currentPortfolio()?.portfolioAverageScore?.rating
                    )
                  }"
                [ngClass]="{ exchange: currentAccount() != 'pac' }"
                >
                {{ currentPortfolio()?.portfolioAverageScore?.rating }}
              </div>
            }

            <!-- <img
            src="/assets/img/logo/crypto-com-logo.png"
            alt="Crypto.com"
            [ngStyle]="{ width: '160px' }"
            *ngIf="currentExchange == 'crypto-fra'"
            /> -->
          </div>
          <div class="home-balance">
            <div class="home-balance-title">Realized Profit</div>
            <div
              class="home-balance-current"
                [ngStyle]="{
                  color:
                    currentPortfolio()?.portfolioStats?.realizedProfit > 0
                      ? '#04dc00'
                      : 'red'
                }"
              >
              {{
              currentPortfolio()?.portfolioStats?.realizedProfit
              | depositsNoDecimal
              }}
              <!-- <div
              class="home-balance-current-gain"
                  [ngStyle]="{
                    backgroundColor:
                      currentPortfolio()?.portfolioStats?.realizedProfit > 0
                        ? 'rgba(0, 100, 0, 0.5)'
                        : 'rgba(100, 0, 0, 0.5)'
                  }"
              >
              <div class="home-balance-current-gain-icon">
                <i
                  class="fa-solid fa-caret-up"
                  [ngStyle]="{ color: '#04dc00' }"
                      *ngIf="
                        currentPortfolio()?.portfolioStats?.realizedProfit > 0
                      "
                ></i>
                <div
                  class="home-balance-current-gain-icon-down"
                  [ngStyle]="{ marginBottom: '1.2rem' }"
                  >
                  <i
                    class="fa-solid fa-chevron-down"
                        *ngIf="
                          currentPortfolio()?.portfolioStats?.realizedProfit < 0
                        "
                        [ngStyle]="{
                          color: 'red'
                        }"
                  ></i>
                </div>
              </div>
              <div
                class="home-balance-current-gain-number"
                    [ngStyle]="{
                      color:
                        currentPortfolio()?.portfolioStats?.realizedProfit > 0
                          ? '#04dc00'
                          : 'white'
                    }"
                >
                {{
                currentPortfolio()?.portfolioStats?.realizedProfit
                | profitsPerc
                }}
              </div>
            </div> -->
          </div>
          <div class="home-balance-profit-title">Unrealized Profit</div>
          <div
            class="home-balance-profit"
                [ngStyle]="{
                  color:
                    currentPortfolio()?.portfolioStats?.profits > 0
                      ? '#04dc00'
                      : 'red'
                }"
            >
            {{
            currentPortfolio()?.portfolioStats?.profits | profitsNoDecimal
            }}
          </div>

          <div class="home-balance-profit-total-title">Total Profit</div>
          <div
            class="home-balance-profit-total"
                [ngStyle]="{
                  color:
                    currentPortfolio()?.portfolioStats?.profits > 0
                      ? '#04dc00'
                      : 'red'
                }"
            >
            {{
            currentPortfolio()?.portfolioStats?.totalProfits
            | profitsNoDecimal
            }}
          </div>
        </div>

        <!-- <div class="home-extra-back">
        <div class="home-extra-earn">Total earn</div>
        <div class="home-extra-earn-number">
          {{ currentPortfolio()?.earnTotalProfit | depositsNoDecimal }}
        </div>
        <div class="home-extra-gift">Total gifts</div>
        <div class="home-extra-gift-number">
          {{
          currentPortfolio()?.portfolioStats?.gifts | depositsNoDecimal
          }}
        </div>
        <div class="home-extra-fees-title">Taxes</div>
        <div class="home-extra-fees-number">
          -
          {{
          currentPortfolio()?.portfolioStats?.taxes | depositsNoDecimal
          }}
        </div>
      </div> -->
    </div>
  </div>
</div>

<div class="dots">
  <div class="dot">
    <i
      class="fa-solid fa-circle"
      [ngStyle]="{ color: flipState == 'front' ? '#d8d6d6' : null }"
      (click)="flipState = 'front'"
    ></i>
  </div>
  <div class="dot">
    <i
      class="fa-solid fa-circle"
      [ngStyle]="{ color: flipState == 'back' ? '#d8d6d6' : null }"
      (click)="flipState = 'back'"
    ></i>
  </div>
</div>

<div class="home-extra-profits">
  <div class="home-extra-profits-ytd-title">This year</div>
  <div
    class="home-extra-profits-ytd-number"
    [ngStyle]="{ color: ytd() > 0 ? '#04dc00' : 'red' }"
    >
    {{ ytd() < 0 ? "- " : null }}{{ ytd() | depositsNoDecimal }}
  </div>
  <div class="home-extra-profits-3m-title">3 Months</div>
  <div
    class="home-extra-profits-3m-number"
    [ngStyle]="{ color: last3Months() > 0 ? '#04dc00' : 'red' }"
    >
    {{ last3Months() < 0 ? "- " : null
    }}{{ last3Months() | depositsNoDecimal }}
  </div>
  <div class="home-extra-profits-1m-title">This Month</div>
  <div
    class="home-extra-profits-1m-number"
    [ngStyle]="{ color: last1Month() > 0 ? '#04dc00' : 'red' }"
    >
    {{ last1Month() < 0 ? "- " : null
    }}{{ last1Month() | depositsNoDecimal }}
  </div>
</div>

<div class="home-extra" #extra>
  <div class="home-extra-earn">Total earn</div>
  <div class="home-extra-earn-number">
    @switch (currentAccount()) {
      @case ("binance-fra") {
        {{ 229 | depositsNoDecimal }}
      }
      @case ("binance-andrea") {
        {{ 265.75 | depositsNoDecimal }}
      }
      @case ("coinbase-fra") {
        {{ 71.14 | depositsNoDecimal }}
      }
      @case ("pac") {
        {{ 336.89 | depositsNoDecimal }}
      }
      @default {
        {{ 0 | depositsNoDecimal }}
      }
    }
  </div>
  <div class="home-extra-gift">Total gifts</div>
  <div class="home-extra-gift-number">
    {{ currentPortfolio()?.portfolioStats?.gifts | depositsNoDecimal }}
  </div>
  <div class="home-extra-fees-title">Taxes</div>
  <div class="home-extra-fees-number">
    -
    {{ currentPortfolio()?.portfolioStats?.taxes | depositsNoDecimal }}
  </div>
</div>

<!-- Summary Card END -->
</div>

<!-- Stats (Charts)  -->
@defer (on immediate; prefetch on idle) {
<app-charts #charts></app-charts>
} @placeholder {
<div></div>
}
<!-- Stats (Charts) END -->
</div>

<!-- Portfolio (list) -->
@defer (on immediate; prefetch on idle) {
<app-crypto-list
  [portfolio]="currentPortfolio()"
  class="crypto-list"
></app-crypto-list>
} @placeholder {
<div></div>
}
<!-- Portfolio (list) END -->
</div>
