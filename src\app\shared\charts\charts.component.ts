import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-charts',
  templateUrl: './charts.component.html',
  styleUrls: ['./charts.component.scss'],
})
export class ChartsComponent {
  @Input('loading') loading: boolean;
  @Input('maxY') maxY: any;
  @Input('minX') minX: any;

  protected currentChartFilter = 'balance';
  // protected portfolioMonthly = computed(() => {
  //   const history = this.currentAccountService.currentPortfolioHistory();

  //   return history.portfolioMonthly
  //     .sort((a, b) => {
  //       return new Date(b.date).getTime() - new Date(a.date).getTime();
  //     })
  //     .map((history, i) => {
  //       if (
  //         i == 0 ||
  //         i ==
  //           this.currentAccountService.currentPortfolioHistory()
  //             .portfolioMonthly.length -
  //             1
  //       ) {
  //         return {
  //           ...history,
  //           profit: 0,
  //           profitPerc: (history.profit / history.deposits) * 100,
  //         };
  //       } else {
  //         return {
  //           ...history,
  //           profit:
  //             +history.profit -
  //             this.currentAccountService
  //               .currentPortfolioHistory()
  //               .portfolioMonthly[i - 1].profit.toFixed(0),
  //           profitPerc: (history.profit / history.deposits) * 100,
  //         };
  //       }
  //     })
  //     .map((history, i, arr) => {
  //       let profitPercCurrent = history.profit;
  //       console.log('array', arr);
  //       console.log('current', profitPercCurrent);
  //       console.log('next', arr[i - 1]?.profitPerc || 0);

  //       return {
  //         ...history,
  //         profitPerc:
  //           (profitPercCurrent - arr[i - 1]?.profit / arr[i - 1]?.current) /
  //             100 || 0,
  //       };
  //     });
  // });

  constructor(
    private router: Router,
    protected currentAccountService: CurrentAccountService,
  ) {}

  onBalanceClick() {
    this.currentChartFilter = 'balance';
  }

  onHoldingsClick() {
    this.currentChartFilter = 'holdings';
  }

  onMonthlyClick() {
    this.currentChartFilter = 'monthly';
  }

  onYearlyClick() {
    this.currentChartFilter = 'yearly';
    this.router.navigateByUrl('/account-detail');
  }
}
