import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { PipesModule } from '../../../../core/utils/pipes.module';
import { CryptoInfoModule } from '../crypto-info/crypto-info.module';
import { CurrentComponent } from './current.component';

@NgModule({
  declarations: [CurrentComponent],
  imports: [CommonModule, PipesModule, CryptoInfoModule],
  exports: [CurrentComponent],
})
export class CurrentModule {}
