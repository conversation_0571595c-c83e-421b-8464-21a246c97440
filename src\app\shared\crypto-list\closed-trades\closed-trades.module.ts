import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { InfiniteScrollDirective } from 'ngx-infinite-scroll';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { ClosedTradesComponent } from './closed-trades.component';

@NgModule({
  declarations: [ClosedTradesComponent],
  imports: [CommonModule, PipesModule, DatePipe, InfiniteScrollDirective],
  exports: [ClosedTradesComponent],
})
export class ClosedTradesModule {}
