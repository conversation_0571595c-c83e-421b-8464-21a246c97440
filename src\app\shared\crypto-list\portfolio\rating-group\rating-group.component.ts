import { Component, Input } from '@angular/core';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-rating-group',
  templateUrl: './rating-group.component.html',
  styleUrl: './rating-group.component.scss',
})
export class RatingGroupComponent {
  @Input('portfolio') portfolio: any;
  protected showInfo: boolean = false;
  protected currentCoin: string = '';
  protected priceInput: string;
  protected priceInputNumber: number | string;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected expand: boolean = false;
  protected currentCategories = [];
  protected currentAccount =
    this.currentAccountService.currentPortfolio().portfolioRatings;

  constructor(private currentAccountService: CurrentAccountService) {}

  toggleRatingGroup(categoryName: string): void {
    const index = this.currentCategories.indexOf(categoryName);

    if (index !== -1) {
      // Se l'elemento è presente, lo rimuoviamo
      this.currentCategories.splice(index, 1);
    } else {
      // Se l'elemento non è presente, lo aggiungiamo
      this.currentCategories.push(categoryName);
    }
  }

  onHeaderCategoriesClick() {
    if (this.currentCategories.length == 0) {
      let newCoins = this.portfolio.portfolioRatings.slice();

      this.currentCategories = newCoins.map((item) => item.rating);
    } else {
      this.currentCategories = [];
    }
  }
}
