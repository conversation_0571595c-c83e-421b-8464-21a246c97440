import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { PipesModule } from 'src/app/core/utils/pipes.module';
import { AthModule } from './ath/ath.module';
import { CategoryModule } from './category/category.module';
import { CurrentModule } from './current/current.module';
import { DepositsModule } from './deposits/deposits.module';
import { EarnModule } from './earn/earn.module';
import { H24Module } from './h24/h24.module';
import { InfoModule } from './info/info.module';
import { PortfolioComponent } from './portfolio.component';
import { ProfitsModule } from './profits/profits.module';
import { QuantityModule } from './quantity/quantity.module';
import { RankModule } from './rank/rank.module';
import { RatingGroupModule } from './rating-group/rating-group.module';
import { RatingModule } from './rating/rating.module';

@NgModule({
  declarations: [PortfolioComponent],
  imports: [
    CommonModule,
    DepositsModule,
    ProfitsModule,
    CurrentModule,
    AthModule,
    H24Module,
    RankModule,
    PipesModule,
    QuantityModule,
    RatingModule,
    CategoryModule,
    InfoModule,
    EarnModule,
    RatingGroupModule,
  ],
  exports: [PortfolioComponent],
})
export class PortfolioModule {}
