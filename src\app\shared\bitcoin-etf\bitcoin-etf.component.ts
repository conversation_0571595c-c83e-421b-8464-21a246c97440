import { Component, computed, effect, signal } from '@angular/core';
import { CoingeckoService } from 'src/app/core/services/http/coingecko.service';
import { GoogleSheetService } from 'src/app/core/services/http/google-sheet.service';

@Component({
  selector: 'app-bitcoin-etf',
  templateUrl: './bitcoin-etf.component.html',
  styleUrl: './bitcoin-etf.component.scss',
})
export class BitcoinEtfComponent {
  protected selectedEtf = signal<'btc$' | 'btc' | 'eth$' | 'eth' | undefined>(
    undefined,
  );
  protected etfBtcStats = computed(
    () =>
      this.googleSheetService
        .etfBtcHistory()
        ?.sort((a, b) => +new Date(b.date) - +new Date(a.date))[0],
  );

  eff = effect(() => {
    console.log('BTC', this.etfBtcStats());
  });

  protected etfEthStats = computed(
    () =>
      this.googleSheetService
        .etfEthHistory()
        ?.sort((a, b) => +new Date(b.date) - +new Date(a.date))[0],
  );

  protected eurusd = this.coingeckoService.eurusd;
  protected btcPrice = this.coingeckoService.btcPrice;
  protected btc24hChange = this.coingeckoService.btc24hChange;
  protected ethPrice = this.coingeckoService.ethPrice;
  protected eth24hChange = this.coingeckoService.eth24hChange;
  protected btcMarketD = this.coingeckoService.btcMarketD;
  protected ethMarketD = this.coingeckoService.ethMarketD;
  // protected etfStatsNewDate = this.firebaseService.etfStatsNewDate;
  // protected etfMoreInfoFetchData = effect(() => {
  //   if (!!this.etfMoreInfo())
  //     this.googleSheetService.fetchEtfBtcHistoryData().subscribe((res) => {
  //       console.log('ETF BTC', res);
  //     });
  // });

  constructor(
    private googleSheetService: GoogleSheetService,
    private coingeckoService: CoingeckoService,
  ) {}
}
