import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CryptorankService {
  constructor(private http: HttpClient) {}

  getTokens() {
    const API_KEY =
      '9e2c8fbc9e6e591441277916c499b572a75fcd62d443d21774b57d61f145';
    let params = new HttpParams();
    let limit = 300;
    params = params.append('limit', limit.toString());
    params = params.append('optionalFields', 'images');

    return this.http
      .get(`https://api.cryptorank.io/v1/currencies?api_key=${API_KEY}`, {
        params: params,
      })
      .pipe(
        tap((data: any) => {
          // console.log(data.data);
          localStorage.setItem('tokens', JSON.stringify(data.data));
          localStorage.setItem('tokensDate', new Date().toString());
        })
      );
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('tokensDate');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 60) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
