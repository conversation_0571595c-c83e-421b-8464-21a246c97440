<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto (01/12/24)</div>
  <div class="crypto-list-header-quantity">Quantity</div>
  <div class="crypto-list-header-profit">
    Profit
    <i class="fa-solid fa-caret-down"></i>
    <br />
    <div class="totalProfit">
      {{ portfolio?.earnTotalProfit | profits }}
    </div>
  </div>
</div>
@for (coin of portfolio.sortEarn; track coin) {
  <div class="crypto">
    <div class="crypto-list-table">
      <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
        <img src="{{ coin.logo }}" />
      </div>
      <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
        {{ coin.name }}
      </div>
      <div class="crypto-list-table-name" (click)="onInfoClick($event)">
        {{ coin.ticker }}
      </div>
      <div class="crypto-list-table-perc">
        {{ coin.earnQuantity | quantity }}
      </div>
      <div
        class="crypto-list-table-gain"
        [ngStyle]="{ color: coin.earn > 0 ? '#04dc00' : 'red' }"
        >
        {{ coin.earn | profits }}
      </div>
    </div>
    <app-crypto-info
      [coin]="coin"
      [showInfo]="showInfo"
      [currentCoin]="currentCoin"
    ></app-crypto-info>
  </div>
}
