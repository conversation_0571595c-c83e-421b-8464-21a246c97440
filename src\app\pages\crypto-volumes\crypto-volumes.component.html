<app-loader-spinner [loading]="loading()"></app-loader-spinner>

@if (!loading()) {
  <div class="crypto-list" style="margin-top: 0; margin-bottom: 0">
    <div class="crypto-list-header">
      <div class="crypto-list-header-name">Crypto</div>
      <div class="crypto-list-header-tvl" (click)="onSortClick('24h')">
        24H
        @if (currentFilter == '24h') {
          <i
            class="fa-solid fa-caret-down"
            style="margin-left: 0.5rem"
          ></i>
        }
      </div>
      <div class="crypto-list-header-price" (click)="onSortClick('7d')">
        7D
        @if (currentFilter == '7d') {
          <i
            class="fa-solid fa-caret-down"
            style="margin-left: 0.5rem"
          ></i>
        }
      </div>
      <div class="crypto-list-header-30d" (click)="onSortClick('30d')">
        30D
        @if (currentFilter == '30d') {
          <i
            class="fa-solid fa-caret-down"
            style="margin-left: 0.5rem"
          ></i>
        }
      </div>
    </div>

    @for (crypto of cryptoList()?.slice(0, 51); track crypto) {
      <div class="crypto">
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="crypto[12]" />
          </div>
          <div class="crypto-list-table-name">
            {{ crypto[1] }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ crypto[2] }}
          </div>
          <div class="crypto-list-table-gainPercent24h">
            <div class="crypto-list-table-gainPercent24h-number">
              {{ crypto[8].replace("$", "").replaceAll(".", "") | shortNumber }}
            </div>
          </div>
          <div class="crypto-list-table-gainPercent7d">
            <div class="crypto-list-table-gainPercent7d-number">
              {{ crypto[9].replace("$", "").replaceAll(".", "") | shortNumber }}
            </div>
          </div>
          <div class="crypto-list-table-gainPercent30d">
            <div class="crypto-list-table-gainPercent30d-number">
              {{ crypto[10].replace("$", "").replaceAll(".", "") | shortNumber }}
            </div>
          </div>
        </div>
      </div>
    }
  </div>
}
