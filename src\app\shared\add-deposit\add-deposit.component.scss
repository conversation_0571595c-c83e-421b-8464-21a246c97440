.addDeposits {
  display: flex;
  justify-content: space-between;
  font-size: 1.6rem;
  width: 100%;
  background: #4b4b4b;
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  cursor: pointer;
}

.depositsForm {
  display: grid;
  grid-template-columns: 52% 50.6%;
  font-size: 1.6rem;
  width: 100%;
  background: #212121;
  padding: 1.5rem;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 2rem;

  & .input-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 1rem;

    & input {
      margin-bottom: 1rem;
      background-color: #8f8f8f;
      border: none;
      height: 25px;
      width: 95%;
      padding: 0.5rem;
      border-radius: 5px;
      text-align: start;

      &[disabled] {
        background-color: #545454;
        color: black;
      }
    }

    & label {
      margin-bottom: 0.5rem;
    }
  }

  & .buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 0.7rem;
    grid-column: 1/-1;

    & button {
      height: 30px;
      width: 100px;
      font-size: 1.8rem;
      padding: 0.5rem;

      color: white;
      border: none;
      border-radius: 5px;
      text-align: center;
      cursor: pointer;
    }

    & .confirm {
      background-color: green;
    }

    & .cancel {
      background-color: rgb(168, 1, 1);
    }
  }
}
