import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { PipesModule } from '../../../../core/utils/pipes.module';
import { CryptoInfoModule } from '../crypto-info/crypto-info.module';
import { DepositsComponent } from './deposits.component';

@NgModule({
  declarations: [DepositsComponent],
  imports: [CommonModule, PipesModule, CryptoInfoModule],
  exports: [DepositsComponent],
})
export class DepositsModule {}
