import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from '../../../loader-spinner/loader-spinner.module';
import { InfoComponent } from './info.component';

@NgModule({
  declarations: [InfoComponent],
  exports: [InfoComponent],
  imports: [CommonModule, PipesModule, FormsModule, LoaderSpinnerModule],
})
export class InfoModule {}
