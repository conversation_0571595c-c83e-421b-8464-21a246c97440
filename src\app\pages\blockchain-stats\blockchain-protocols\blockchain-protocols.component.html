<div class="crypto-list-header">
  <div class="crypto-list-header-name">Blockchain</div>
  <div class="crypto-list-header-tvl">Protocols</div>
  <div class="crypto-list-header-price">Protocols %</div>
</div>

@for (chain of chainsProtocols.slice(0, 21); track chain) {
  <div class="crypto">
    @if(!!chain.tokenSymbol && chain.protocols > 99){
      <div class="crypto-list-table">
        <div class="crypto-list-table-logo">
          <img [src]="chain.img" />
        </div>
        <div class="crypto-list-table-name">
          {{ chain.name == "Binance" ? "BSC" : chain.name }}
        </div>
        <div class="crypto-list-table-ticker">
          {{ chain.tokenSymbol }}
        </div>
        <div class="crypto-list-table-tvl">
          {{ (chain.protocols | number : "1.0-0").replaceAll(",", ".") }}
        </div>
        <div class="crypto-list-table-price">
          <div>
            {{ (chain?.protocols / chainsTotalProtocols) * 100 | profitsPerc }}
          </div>
        </div>
      </div>
    }
  </div>
}
