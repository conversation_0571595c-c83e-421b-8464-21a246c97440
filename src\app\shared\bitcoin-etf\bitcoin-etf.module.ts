import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgApexchartsModule } from 'ng-apexcharts';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from '../loader-spinner/loader-spinner.module';
import { BitcoinEtfChartComponent } from './bitcoin-etf-chart/bitcoin-etf-chart.component';
import { BitcoinEtfComponent } from './bitcoin-etf.component';
import { EthereumEtfChartComponent } from './ethereum-etf-chart/ethereum-etf-chart.component';

@NgModule({
  declarations: [
    BitcoinEtfComponent,
    BitcoinEtfChartComponent,
    EthereumEtfChartComponent,
  ],
  imports: [CommonModule, NgApexchartsModule, PipesModule, LoaderSpinnerModule],
  exports: [
    BitcoinEtfComponent,
    BitcoinEtfChartComponent,
    EthereumEtfChartComponent,
  ],
})
export class BitcoinEtfModule {}
