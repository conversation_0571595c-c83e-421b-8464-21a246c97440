@if (!loading()) {
  <div class="container">
    <div class="phase">
      Phase:
      <span style="color: #f7931a; margin-left: 0.5rem"> 1 BITCOIN</span>
    </div>
    <div class="crypto-list-buttons">
      <div
        class="crypto-list-buttons-tvl"
        (click)="currentSection = 'stats'"
        [ngClass]="{ selected: currentSection == 'stats' }"
        >
        Stats
      </div>
      <div
        class="crypto-list-buttons-tvl"
        (click)="currentSection = 'topAltcoins'"
        [ngClass]="{ selected: currentSection == 'topAltcoins' }"
        >
        Top Altcoins
      </div>

      <div class="extra"></div>
    </div>

    @if (currentSection == "stats") {
      <div class="grid eth">
        <div class="header-logo">
          <img src="../../../assets/img/logo/eth.png" alt="" />
        </div>
        <div class="header-name">ETH</div>
        <div class="header-7d">7d</div>
        <div class="header-30d">30d</div>
        <div class="subheader">BTC</div>
        <div class="subheader">ETH</div>
        <div class="subheader">ETH vs BTC</div>

        <div
          class="grid-7d"
          [ngStyle]="{
            color:
              +cryptoStats()[2][1]?.replace(',', '.') > 0 ? '#04dc00' : 'red'
          }"
          >
          {{
          +cryptoStats()[2][1]?.replace(",", ".") * 100 | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d"
          [ngStyle]="{
            color:
              +cryptoStats()[2][2]?.replace(',', '.') > 0 ? '#04dc00' : 'red'
          }"
          >
          {{
          +cryptoStats()[2][2]?.replace(",", ".") * 100 | profitsPercNodecimal
          }}
        </div>

        <div
          class="grid-7d-btc"
          [ngStyle]="{
            color:
              +cryptoStats()[18][1]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[18][1]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d-btc"
          [ngStyle]="{
            color:
              +cryptoStats()[18][2]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[18][2]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>

        <div
          class="grid-7d-eth"
          [ngStyle]="{
            color:
              +cryptoStats()[22][1]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[22][1]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d-eth"
          [ngStyle]="{
            color:
              +cryptoStats()[22][2]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[22][2]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
      </div>

      <div class="grid top">
        <div class="header-name">TOP 50</div>
        <div class="header-7d">7d</div>
        <div class="header-30d">30d</div>
        <div class="subheader">BTC</div>
        <div class="subheader">Avg Top 50</div>
        <div class="subheader">Top 50 vs BTC</div>
        <div class="subheader">Outperform BTC</div>
        <div
          class="grid-7d"
          [ngStyle]="{
            color:
              +cryptoStats()[12][1]?.replace(',', '.') > 0 ? '#04dc00' : 'red'
          }"
          >
          {{
          +cryptoStats()[12][1]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d"
          [ngStyle]="{
            color:
              +cryptoStats()[12][2]?.replace(',', '.') > 0 ? '#04dc00' : 'red'
          }"
          >
          {{
          +cryptoStats()[12][2]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-7d-btc"
          [ngStyle]="{
            color:
              +cryptoStats()[18][1]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[18][1]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d-btc"
          [ngStyle]="{
            color:
              +cryptoStats()[18][2]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[18][2]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-7d-top"
          [ngStyle]="{
            color:
              +cryptoStats()[15][1]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{ +cryptoStats()[15][1]?.replace(",", ".") | profitsPercNodecimal }}
        </div>
        <div
          class="grid-30d-top"
          [ngStyle]="{
            color:
              +cryptoStats()[15][2]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{ +cryptoStats()[15][2]?.replace(",", ".") | profitsPercNodecimal }}
        </div>
        <div
          class="grid-7d-number"
          [ngStyle]="{
            color:
              +cryptoStats()[13][1].replace(',', '.') > 25 ? '#04dc00' : 'red'
          }"
          >
          {{ +cryptoStats()[13][1].replace(",", ".") + " / 50" }}
        </div>
        <div
          class="grid-30d-number"
          [ngStyle]="{
            color:
              +cryptoStats()[13][2]?.replace(',', '.') > 25 ? '#04dc00' : 'red'
          }"
          >
          {{ +cryptoStats()[13][2].replace(",", ".") + " / 50" }}
        </div>
      </div>

      <div class="grid top">
        <div class="header-name">TOP 100</div>
        <div class="header-7d">7d</div>
        <div class="header-30d">30d</div>
        <div class="subheader">BTC</div>
        <div class="subheader">Avg Top 100</div>
        <div class="subheader">Top 100 vs BTC</div>
        <div class="subheader">Outperform BTC</div>
        <div
          class="grid-7d"
          [ngStyle]="{
            color:
              +cryptoStats()[6][1].replace(',', '.') > 0 ? '#04dc00' : 'red'
          }"
          >
          {{
          +cryptoStats()[6][1].replace(",", ".") * 100 | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d"
          [ngStyle]="{
            color:
              +cryptoStats()[6][2].replace(',', '.') > 0 ? '#04dc00' : 'red'
          }"
          >
          {{
          +cryptoStats()[6][2].replace(",", ".") * 100 | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-7d-btc"
          [ngStyle]="{
            color:
              +cryptoStats()[18][1]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[18][1]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-30d-btc"
          [ngStyle]="{
            color:
              +cryptoStats()[18][2]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{
          +cryptoStats()[18][2]?.replace(",", ".") * 100
          | profitsPercNodecimal
          }}
        </div>
        <div
          class="grid-7d-top"
          [ngStyle]="{
            color:
              +cryptoStats()[9][1]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{ +cryptoStats()[9][1]?.replace(",", ".") | profitsPercNodecimal }}
        </div>
        <div
          class="grid-30d-top"
          [ngStyle]="{
            color:
              +cryptoStats()[9][2]?.replace(',', '.') > 0
                ? 'darkgreen'
                : 'darkred'
          }"
          >
          {{ +cryptoStats()[9][2]?.replace(",", ".") | profitsPercNodecimal }}
        </div>
        <div
          class="grid-7d-number"
          [ngStyle]="{
            color:
              +cryptoStats()[7][1]?.replace(',', '.') > 50 ? '#04dc00' : 'red'
          }"
          >
          {{ +cryptoStats()[7][1]?.replace(",", ".") + " / 100" }}
        </div>
        <div
          class="grid-30d-number"
          [ngStyle]="{
            color:
              +cryptoStats()[7][2]?.replace(',', '.') > 50 ? '#04dc00' : 'red'
          }"
          >
          {{ +cryptoStats()[7][2]?.replace(",", ".") + " / 100" }}
        </div>
      </div>
    }
    @if (currentSection == "topAltcoins") {
      <div class="crypto-list" style="margin-top: 0; margin-bottom: 0">
        <div class="crypto-list-header">
          <div class="crypto-list-header-name">Crypto</div>
          <div class="crypto-list-header-tvl" (click)="onSortClick('7d')">
            7D
            @if (currentFilter == '7d') {
              <i
                class="fa-solid fa-caret-down"
                style="margin-left: 0.3rem"
              ></i>
            }
          </div>
          <div class="crypto-list-header-price" (click)="onSortClick('30d')">
            30D
            @if (currentFilter == '30d') {
              <i
                class="fa-solid fa-caret-down"
                style="margin-left: 0.3rem"
              ></i>
            }
          </div>
        </div>

        @for (crypto of cryptoTop.slice(0, 32); track crypto[0]) {
          @if (crypto[2] !== "BTC") {
            <div class="crypto">
              <div class="crypto-list-table">
                <div class="crypto-list-table-logo">
                  <img [src]="crypto?.[12]" />
                </div>
                <div class="crypto-list-table-name">
                  {{ crypto?.[1] }}
                </div>
                <div class="crypto-list-table-ticker">
                  {{ crypto?.[2] }}
                </div>
                <div
                  class="crypto-list-table-gainPercent7d"
                  [ngStyle]="{
                    backgroundColor:
                      +crypto?.[6] > 0
                        ? 'rgba(0, 100, 0, 0.4)'
                        : 'rgba(100, 0, 0, 0.4)'
                  }"
                  >
                  <div class="crypto-list-table-gainPercent7d-icon">
                    @if (crypto?.[6] > 0) {
                      <i class="fa-solid fa-caret-up"></i>
                    }
                    @if (+crypto?.[6] < 0) {
                      <i
                        class="fa-solid fa-caret-down"
                      [ngStyle]="{
                        color: 'red'
                      }"
                      ></i>
                    }
                  </div>
                  <div
                    class="crypto-list-table-gainPercent7d-number"
                    [ngStyle]="{
                      color: +crypto?.[6] > 0 ? '#04dc00' : 'red'
                    }"
                    >
                    {{ +crypto?.[6] | profitsPerc }}
                  </div>
                </div>
                <div
                  class="crypto-list-table-gainPercent30d"
                  [ngStyle]="{
                    backgroundColor:
                      +crypto?.[7] > 0
                        ? 'rgba(0, 100, 0, 0.4)'
                        : 'rgba(100, 0, 0, 0.4)'
                  }"
                  >
                  <div class="crypto-list-table-gainPercent30d-icon">
                    @if (+crypto?.[7] > 0) {
                      <i
                        class="fa-solid fa-caret-up"
                      ></i>
                    }
                    @if (+crypto?.[7] < 0) {
                      <i
                        class="fa-solid fa-caret-down"
                      [ngStyle]="{
                        color: 'red'
                      }"
                      ></i>
                    }
                  </div>
                  <div
                    class="crypto-list-table-gainPercent30d-number"
                    [ngStyle]="{
                      color: +crypto?.[7] > 0 ? '#04dc00' : 'red'
                    }"
                    >
                    {{ +crypto?.[7] | profitsPerc }}
                  </div>
                </div>
              </div>
            </div>
          }
        }
      </div>
    }
  </div>
} @else {
  <app-loader-spinner></app-loader-spinner>
}
