import {
  ChangeDetectionStrategy,
  Component,
  input,
  signal,
} from '@angular/core';
import { PortfolioCoinbaseFra } from 'src/app/core/services/data/coinbase-fra.service';

@Component({
  selector: 'app-crypto-list',
  templateUrl: './crypto-list.component.html',
  styleUrls: ['./crypto-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CryptoListComponent {
  portfolio = input<PortfolioCoinbaseFra>();

  protected currentheaderFilter = signal<'portfolio' | 'closedTrades'>(
    'portfolio',
  );
}
