import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from 'src/app/shared/loader-spinner/loader-spinner.module';
import { AltcoinSeasonRoutingModule } from './altcoin-season-routing.module';
import { AltcoinSeasonComponent } from './altcoin-season.component';

@NgModule({
  declarations: [AltcoinSeasonComponent],
  imports: [
    CommonModule,
    AltcoinSeasonRoutingModule,
    PipesModule,
    LoaderSpinnerModule,
  ],
  exports: [AltcoinSeasonComponent],
})
export class AltcoinSeasonModule {}
