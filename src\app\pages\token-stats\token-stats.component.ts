import { Component, signal } from '@angular/core';
import { CryptorankService } from 'src/app/core/services/http/cryptorank.service';

@Component({
  selector: 'app-token-stats',
  templateUrl: './token-stats.component.html',
  styleUrl: './token-stats.component.scss',
})
export class TokenStatsComponent {
  protected tokenList = [];
  protected tokenFilter = [];
  protected loading = signal<boolean>(true);
  protected tableFilter = signal('general');
  protected currentSorting;

  constructor(private cryptorank: CryptorankService) {
    console.log('FetchData Tokens: ', this.cryptorank.shouldFetchData());
    if (
      !this.cryptorank.shouldFetchData() &&
      JSON.parse(localStorage.getItem('tokens'))
    ) {
      this.tokenList = JSON.parse(localStorage.getItem('tokens')).filter(
        (item) =>
          item.type == 'token' &&
          item.category != 'Stablecoin' &&
          item.category != 'Chain' &&
          item.values.USD.marketCap > 100000000,
      );

      this.tokenFilter = this.tokenList;

      this.loading.set(false);
    }

    if (
      !this.cryptorank.shouldFetchData() &&
      !JSON.parse(localStorage.getItem('tokens'))
    ) {
      this.cryptorank.getTokens().subscribe((data: any) => {
        let tokens = data.data;

        this.tokenList = tokens.filter(
          (item) =>
            item.type == 'token' &&
            item.category != 'Stablecoin' &&
            item.category != 'Chain' &&
            item.values.USD.marketCap > 100000000,
        );

        this.tokenFilter = this.tokenList;

        this.loading.set(false);
      });
    }

    if (this.cryptorank.shouldFetchData()) {
      this.cryptorank.getTokens().subscribe((data: any) => {
        let tokens = data.data;

        // console.log(
        //   tokens.filter(
        //     (item) =>
        //       item.type == 'token' &&
        //       item.category != 'Stablecoin' &&
        //       item.category != 'Chain'
        //   )
        // );

        this.tokenList = tokens.filter(
          (item) =>
            item.type == 'token' &&
            item.category != 'Stablecoin' &&
            item.category != 'Chain' &&
            item.values.USD.marketCap > 100000000,
        );

        this.tokenFilter = this.tokenList;

        this.loading.set(false);
      });
    }
  }

  onTableFilterChange(chain: string) {
    this.tableFilter.set(chain);

    // if (chain == 'all') {
    //   this.tokenFilter = this.tokenList;
    // } else {
    //   this.tokenFilter = this.tokenList.slice().filter((item) => {
    //     return item.tokens.some((token) => token.platform.name === 'BNB');
    //   });
    // }

    // console.log(this.tokenList);
  }

  onSortChainsClick() {
    this.tokenFilter = this.tokenFilter.sort(
      (a, b) => b.tokens.length - a.tokens.length,
    );

    this.currentSorting = 'chains';
  }

  onSortCategoryClick() {
    this.tokenFilter = this.tokenFilter.sort((a, b) => {
      if (a.category === null || a.category === undefined) return 1;
      if (b.category === null || b.category === undefined) return -1;
      return a.category.localeCompare(b.category);
    });

    this.currentSorting = 'category';
  }

  onSortRankCapClick() {
    this.tokenFilter = this.tokenFilter.sort(
      (a, b) => b.values.USD.marketCap - a.values.USD.marketCap,
    );

    this.currentSorting = 'rank';
  }
}
