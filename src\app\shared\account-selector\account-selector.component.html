<div
  class="bg"
  [ngStyle]="{ display: menu() ? 'block' : 'none' }"
  (click)="onCLoseBtnClick()"
></div>

<div
  class="account-selector"
  [ngClass]="{ 'title-other-pages': currentPage() !== '' }"
  >
  @switch (currentPage()) {
    @case ("news") {
      <div class="header">News</div>
    }
    @case ("blockchain-stats") {
      <div class="header">Blockchain Stats</div>
    }
    @case ("token-stats") {
      <div class="header">Token Stats</div>
    }
    @case ("crypto-volumes") {
      <div class="header">Crypto Volumes</div>
    }
    @case ("altcoin-season") {
      <div class="header">Altcoin Season</div>
    }
    @case ("account-deposits") {
      <div class="header">Deposit History</div>
    }
    @case ("account-detail") {
      <div class="header">Annual Report</div>
      <!-- <ng-container *ngTemplateOutlet="tplSelector"></ng-container> -->
    }
    @default {
      <ng-container *ngTemplateOutlet="tplSelector"></ng-container>
    }
  }
  <!-- Buttons END -->
  <!-- <div class="menu">
  @if(addedNews().length > 0 && !menu){
    <div class="notification" [ngStyle]="{ opacity: menu ? 0.6 : 1 }">
      {{ addedNews().length }}
    </div>
  }
  <input id="menu-toggle" type="checkbox" [(ngModel)]="menu" />
  <label class="menu-button-container" for="menu-toggle">
    <div class="menu-button"></div>
  </label>
  <ul class="menu">
    <li (click)="onHomeClick()">
      HOME
      <div class="icon">
        <i class="fa-regular fa-house" style="margin-left: 1rem"></i>
      </div>
    </li>
    <li class="news" (click)="onNewsClick()">
      @if(menu && addedNews().length > 0){
        <div class="notification">{{ addedNews().length }}</div>
        } NEWS
        <div class="icon">
          <i class="fa-regular fa-bullhorn" style="margin-left: 1rem"></i>
        </div>
      </li>
      <li (click)="onReportClick()">
        ANNUAL REPORT
        <div class="icon">
          <i class="fa-regular fa-clipboard" style="margin-left: 1rem"></i>
        </div>
      </li>
      <li
        (click)="onLogoutClick()"
        [ngStyle]="{ color: 'red', fontWeight: '500' }"
        >
        LOGOUT
        <div class="icon">
          <i
            class="fa-solid fa-right-from-bracket"
            style="margin-left: 1rem"
          ></i>
        </div>
      </li>
    </ul>
  </div> -->

  <!-- TEST MENU -->
  <div class="menu-container">
    @if (addedNews().length > 0 && !currentPage()) {
      <div class="notification">
        {{ addedNews().length > 9 ? "!" : addedNews().length }}
      </div>
    }
    <div class="menu-btn" (click)="onMenuBtnClick()" #menuBtn>
      <svg
        width="35"
        height="35"
        viewBox="0 0 24 24"
        fill="none"
        stroke="#fff"
        stroke-width="1.5"
        stroke-linecap="butt"
        stroke-linejoin="arcs"
        >
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg>
      <!-- <i class="fas fa-bars"></i> -->
    </div>

    <div class="side-bar" #sidebar>
      <div class="menu-header">
        <div class="close-btn" (click)="onCLoseBtnClick()" #closeBtn>
          <i class="fas fa-times"></i>
        </div>
        <img src="/assets/img/logo/btc.png" alt="logo" />
        <div>Cripto Tracker</div>
      </div>
      <div class="menu">
        @if (currentUser()?.username !== "elisa") {
          <div class="item stock-tracker">
            <a href="https://stocktracker24.netlify.app/"
              ><div class="icon">
              <img src="/assets/img/logo/tsla.svg" alt="Stock Tracker" />
            </div>
            Stock Tracker
            <i class="fa-solid fa-arrow-up-right-from-square fa-xs"></i>
          </a>
        </div>
      }
      <div class="item" (click)="onHomeClick()">
        <a
          ><div class="icon"><i class="fa-solid fa-house"></i></div>
          Dashboard</a
          >
        </div>
        <div class="item" (click)="onNewsClick()">
          <a>
            <div class="icon"><i class="fa-regular fa-bullhorn"></i></div>
            News
            @if (menu() && addedNews().length > 0) {
              <div class="notificationNews">
                {{ addedNews().length > 9 ? "!" : addedNews().length }}
              </div>
            }
          </a>
        </div>
        <div class="item" (click)="onReportClick()">
          <a>
            <div class="icon"><i class="fa-solid fa-chart-line"></i></div>
            Annual Report
            <!-- <i class="fas fa-angle-right dropdown"></i> -->
          </a>
          <!-- <div class="sub-menu" #subMenu>
          <a href="#" class="sub-item">Sub Item 01</a>
          <a href="#" class="sub-item">Sub Item 02</a>
        </div> -->
      </div>
      <div class="item" (click)="onDepositHistoryClick()">
        <a>
          <div class="icon">
            <i class="fa-solid fa-money-bill-transfer"></i>
          </div>
          Deposit History
        </a>
      </div>
      @if (currentUser()?.username !== "elisa") {
        <div class="item" (click)="onCryptoVolumesClick()">
          <a>
            <div class="icon">
              <i class="fa-solid fa-magnifying-glass-chart"></i>
            </div>
            Crypto Volumes
          </a>
        </div>

        <div class="item" (click)="onAltcoinSeasonClick()">
          <a>
            <div class="icon">
              <i class="fa-solid fa-rocket"></i>
            </div>
            Altcoin Season
          </a>
        </div>
        <div class="item" (click)="onBlockchainStatsClick()">
          <a>
            <div class="icon">
              <i class="fa-solid fa-chart-pie"></i>
            </div>
            Blockchain Stats
          </a>
        </div>
        <div class="item" (click)="onTokenStatsCLick()">
          <a>
            <div class="icon">
              <i class="fa-solid fa-magnifying-glass-dollar"></i>
            </div>
            Token Stats
          </a>
        </div>
      }
      <div class="item" (click)="onLogoutClick()">
        <a [routerLink]="'/login'" style="color: red">
          <div class="icon">
            <i class="fa-solid fa-right-from-bracket"></i>
          </div>
          Logout</a
          >
        </div>
      </div>
    </div>
    <!-- <section class="main">
    <h1>Sidebar Menu With<br />Sub-Menus</h1>
  </section> -->
</div>
</div>
@if (
  currentPage() === "account-detail" || currentPage() == "account-deposits"
  ) {
  <div class="account-selector" style="margin-top: 24px; margin-bottom: -10px">
    <ng-container *ngTemplateOutlet="tplSelector"></ng-container>
  </div>
}

<ng-template #tplSelector>
  <div class="selector">
    <div class="account-selector-title">Account</div>

    <!-- Buttons -->
    <div
      class="account-selector-buttons"
      [ngClass]="{
        hidden: menu()
      }"
      >
      <div class="select select-after">
        <form [formGroup]="accountForm">
          <select formControlName="account" class="selected">
            @for (key of portfoliosKeys(); track key) {
              <option [selected]="selectedAccount() === key">
                {{ key }}
              </option>
            }
          </select>
          <i class="fa-solid fa-chevron-down"></i>
        </form>
      </div>
      @if (currentAccount() != 'pac') {
        <div
          class="select select2"
        [ngClass]="{
          'select-after':
            currentAccount() != 'pac' && currentAccount() != 'binance-andrea'
        }"
          >
          <form [formGroup]="exchangeForm">
            <select formControlName="exchange">
              <i class="fa-solid fa-chevron-down">ciao</i>
              @if (
                currentAccount() == 'coinbase-fra' ||
                currentAccount() == 'binance-fra'
                ) {
                <option
                  value="binance-fra"
                  [selected]="currentAccount() == 'binance-fra'"
                  >
                  Binance
                </option>
              }
              @if (
                currentAccount() == 'coinbase-fra' ||
                currentAccount() == 'binance-fra'
                ) {
                <option
                  value="coinbase-fra"
                  [selected]="currentAccount() == 'coinbase-fra'"
                  >
                  Coinbase
                </option>
              }
              @if (currentAccount() == 'binance-andrea') {
                <option
                  value="binance-andrea"
                  >
                  Binance
                </option>
              }
              @if (currentAccount() == 'binance-elisa') {
                <option
                  value="binance-elisa"
                  >
                  Binance
                </option>
              }
            </select>
            <i class="fa-solid fa-chevron-down"></i>
          </form>
        </div>
      }
      <!-- @if(showHomeButton()){
      <div class="buttons" (click)="onHomeClick()">
        HOME
        <i class="fa-solid fa-house" style="margin-left: 0.5rem"></i>
      </div>
      } -->
    </div>
  </div>
</ng-template>
