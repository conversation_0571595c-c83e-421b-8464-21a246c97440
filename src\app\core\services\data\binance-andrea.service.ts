import { Injectable } from '@angular/core';
import { Coin, ICoin, IPortfolioStats, ITrade } from '../../interfaces/coins';

@Injectable({
  providedIn: 'root',
})
export class PortfolioBinanceAndrea extends Coin {
  constructor() {
    super();
  }

  // BINANCE
  public coins: ICoin[] = [
    // Profitto ETH = 685€
    {
      name: 'Ethereum',
      nameApi: 'ethereum',
      ticker: 'ETH',
      logo: '../../../assets/img/logo/eth.png',
      category: 'Layer 1',
      quantity: 0.47,
      deposits: 1012.75,
      averagePrice: 2250,
      logoColor: '#627eea',
      ecosystem: ['Ethereum'],
    },
    {
      name: 'Bitcoin',
      nameApi: 'bitcoin',
      ticker: 'BTC',
      logo: '../../../assets/img/logo/btc.png',
      quantity: 0.00155059,
      deposits: 135.57,
      averagePrice: 41117,
      logoColor: '#F7931A',
      category: 'Layer 1',
    },
    // Profitto XRP = 175€
    {
      name: 'Ripple',
      nameApi: 'ripple',
      ticker: 'XRP',
      logo: '../../../assets/img/logo/xrp.png',
      category: 'Payment',
      quantity: 221.18,
      deposits: 348.37,
      averagePrice: 0.55,
      logoColor: '#23292F',
      ecosystem: ['Xrp'],
      description: `Lanciato nel 2021, XRP Ledger (XRPL) è una tecnologia open source, senza autorizzazione e decentralizzata. I vantaggi di XRP Ledger includono il basso costo ($ 0,0002 per effettuare transazioni), la velocità (regolamento delle transazioni in 3-5 secondi), la scalabilità (1.500 transazioni al secondo) e attributi intrinsecamente ecologici (a zero emissioni di carbonio ed efficiente dal punto di vista energetico). XRP Ledger presenta anche il primo scambio decentralizzato (DEX) e funzionalità di tokenizzazione personalizzata integrate nel protocollo. Dal 2012, XRP Ledger funziona in modo affidabile, avendo chiuso 70 milioni di registri.`,
    },
    // Profitto FET = 261€
    {
      name: 'Fetch.ai',
      nameApi: 'fetch-ai',
      ticker: 'FET',
      logo: '../../../assets/img/logo/fet.png',
      category: 'AI',
      quantity: 721.32,
      deposits: 507.14,
      averagePrice: 1.07,
      logoColor: '#1E2943',
      ecosystem: ['Ethereum'],
      description: `Fondato nel 2017 e lanciato tramite IEO su Binance nel marzo 2019, Fetch.AI è un laboratorio di 
      <span style='color: #4f4fff'><strong>intelligenza artificiale (AI)</strong></span> che costruisce una rete di apprendimento automatico aperta, senza autorizzazione e decentralizzata con un'economia crittografica. 
      Fetch.ai democratizza l'accesso alla tecnologia IA con una rete senza autorizzazione su cui chiunque può connettersi e accedere a set di dati sicuri utilizzando l'intelligenza artificiale autonoma per eseguire attività che sfruttano la sua rete globale di dati. 
      Il modello Fetch.AI affonda le sue radici in casi d'uso come l'ottimizzazione dei servizi di trading DeFi, delle reti di trasporto (parcheggi, micromobilità), delle reti energetiche intelligenti, dei viaggi - essenzialmente qualsiasi sistema digitale complesso che si basa su set di dati su larga scala.`,
    },
    // Profitto SOL = 653€ + 1.716.60 (13/09/2025)
    //     {
    //       name: 'Solana',
    //       nameApi: 'solana',
    //       ticker: 'SOL',
    //       logo: '../../../assets/img/logo/sol.png',
    //       quantity: 14.718,
    //       deposits: 1327.3,
    //       averagePrice: 77.98,
    //       logoColor: '#0a0b0d',
    //       ecosystem: ['Solana'],
    //       category: 'Layer 1',
    //       description: `Solana è un progetto open source altamente funzionale che sfrutta la natura senza autorizzazione della tecnologia blockchain per fornire soluzioni di finanza decentralizzata (DeFi). Sebbene l'idea e il lavoro iniziale sul progetto siano iniziati nel 2017, Solana è stata lanciata ufficialmente nel marzo 2020 dalla Fondazione Solana con sede a Ginevra, Svizzera.

    // Il protocollo Solana è progettato per facilitare la creazione di app decentralizzate (DApp). Mira a migliorare la scalabilità introducendo un consenso proof-of-history (PoH) combinato con il consenso sottostante proof-of-stake (PoS) della blockchain.

    // Grazie all'innovativo modello di consenso ibrido, Solana gode dell'interesse sia dei piccoli trader che dei trader istituzionali. Un obiettivo significativo per la Fondazione Solana è rendere la finanza decentralizzata accessibile su scala più ampia.`,
    //     },
    {
      name: 'Euro',
      nameApi: 'euro-coin',
      ticker: 'EUR',
      logo: '../../../assets/img/logo/euro.png',
      category: 'Fiat',
      quantity: 3044,
      deposits: 1327.3,
      averagePrice: 1,
      logoColor: '#000000',
      ecosystem: ['Fiat'],
    },
    {
      name: 'Polygon',
      nameApi: 'matic-network',
      ticker: 'MATIC',
      logo: '../../../assets/img/logo/matic.png',
      category: 'Layer 2',
      quantity: 84.19,
      deposits: 65.2,
      averagePrice: 0.809,
      logoColor: '#8247e5',
      ecosystem: ['Ethereum'],
    },
    {
      name: 'Cardano',
      nameApi: 'cardano',
      ticker: 'ADA',
      logo: '../../../assets/img/logo/ada.png',
      category: 'Layer 1',
      quantity: 471.75,
      deposits: 253.08,
      averagePrice: 0.519,
      logoColor: '#0033ad',
      ecosystem: ['Cardano'],
    },
    {
      name: 'The Graph',
      nameApi: 'the-graph',
      ticker: 'GRT',
      logo: '../../../assets/img/logo/graph.webp',
      category: 'AI',
      quantity: 469.82,
      deposits: 102.01,
      averagePrice: 0.17,
      logoColor: '#4827A9',
      ecosystem: ['Ethereum'],
    },
    {
      name: 'BNB',
      nameApi: 'binancecoin',
      ticker: 'BNB',
      logo: '../../../assets/img/logo/bnb.webp',
      category: 'Layer 1',
      quantity: 1.062,
      deposits: 305.8,
      averagePrice: 294.1,
      logoColor: '#F0B90B',
    },
    {
      name: 'Arbitrum',
      nameApi: 'arbitrum',
      ticker: 'ARB',
      logo: '../../../assets/img/logo/arb.png',
      category: 'Layer 2',
      quantity: 29.29,
      deposits: 50.01,
      averagePrice: 1.94,
      logoColor: '#2D6E8D',
    },
    {
      name: 'Sleepless AI',
      nameApi: 'sleepless-ai',
      ticker: 'AI',
      logo: '../../../assets/img/logo/ai.webp',
      category: 'AI',
      quantity: 30.99,
      deposits: 51,
      logoColor: '#ED4243',
    },
    {
      name: 'Cosmos',
      nameApi: 'cosmos',
      ticker: 'ATOM',
      logo: '../../../assets/img/logo/atom.png',
      quantity: 4.729,
      deposits: 51,
      averagePrice: 10.86,
      logoColor: '#2e3148',
      category: 'Layer 1',
    },
    {
      name: 'Toncoin',
      nameApi: 'the-open-network',
      ticker: 'TON',
      logo: '../../../assets/img/logo/ton.webp',
      category: 'Layer 1',
      quantity: 64.47,
      deposits: 412,
      averagePrice: 7.2,
      logoColor: '#72BCE2',
    },
    {
      name: 'Bonk',
      nameApi: 'bonk',
      ticker: 'BONK',
      logo: '../../../assets/img/logo/bonk.png',
      category: 'Memecoin',
      quantity: 4017020.81,
      deposits: 100.68,
      averagePrice: 0.00002456,
      logoColor: '#fccb08',
    },

    {
      name: 'Official Trump',
      nameApi: 'official-trump',
      ticker: 'TRUMP',
      logo: '../../../assets/img/logo/trump.webp',
      category: 'Memecoin',
      quantity: 16.97,
      deposits: 346.18,
      averagePrice: 62,
      logoColor: 'yellow',
      ecosystem: ['Solana'],
    },
  ];

  public portfolioStats: IPortfolioStats = {
    gifts: 92,
    totalFees: -70.22,
    taxes: 18,
    realizedProfit: 0,
  };

  public closedTrades: ITrade[] = [];
}
