@if (showUpdateModal()) {
  <app-modal
    [modalContentTitle]="updateModalConfig().modalContentTitle"
    [modalContentText]="updateModalConfig().modalContentText"
    [modalCounter]="modalCounterSeconds()"
  ></app-modal>
}
<!-- DESKTOP -->
@if (currentDevice() === "desktop") {
  @if (!pageLoading()) {
    @if (currentUrl() !== "/login" && loading()) {
      <app-loader-spinner [loading]="loading()"></app-loader-spinner>
    }
    <!-- AUTH PAGES -->
    @if (currentUrl() !== "/login" && !loading()) {
      <div class="main-content">
        <!-- Side menu show everywhere -->
        <div class="side-menu">
          <app-side-menu-desktop></app-side-menu-desktop>
        </div>

        <!-- Only show in home -->
        <div class="inner-content">
          @if (currentUrl() === "/") {
            <!-- Cripto top 24h -->
            <div class="price-tab">
              <app-price-tab [portfolio]="currentPortfolio()"></app-price-tab>
              <!--  Cripto top 24h END-->
            </div>

            <!-- Cripto widgets -->
            <app-widgets [portfolio]="currentPortfolio()"></app-widgets>
            <!--  Cripto widgets END-->
          }

          <!-- Show everywhere -->
          <div
            class="exchange"
            [style]="{ padding: currentUrl() !== '/' ? '1rem' : 0 }"
          >
            <app-account-selector></app-account-selector>
          </div>

          <router-outlet></router-outlet>
        </div>
      </div>
    }

    <!-- LOGIN PAGE -->
    @if (currentUrl() === "/login") {
      <router-outlet></router-outlet>
    }
  }
}

<!-- MOBILE -->
@if (currentDevice() === "mobile") {
  <div [class]="{ container: menuOpen }">
    @if (!pageLoading()) {
      @if (currentUrl() !== "/login" && loading()) {
        <app-loader-spinner [loading]="loading()"></app-loader-spinner>
      }
      <!-- AUTH PAGES -->
      @if (currentUrl() !== "/login" && !loading()) {
        <!-- Show account selector in every page -->
        <div class="exchange">
          <app-account-selector
            (menuOpen)="onMenuClick($event)"
          ></app-account-selector>
        </div>

        <!-- Only show in home -->
        @if (currentUrl() === "/") {
          <!-- Cripto top 24h -->
          <div class="price-tab">
            <app-price-tab [portfolio]="currentPortfolio()"></app-price-tab>
            <!--  Cripto top 24h END-->
          </div>

          <!-- Cripto widgets -->
          <app-widgets [portfolio]="currentPortfolio()"></app-widgets>
          <!--  Cripto widgets END-->
        }

        <router-outlet></router-outlet>
      }

      <!-- LOGIN PAGE -->
      @if (currentUrl() === "/login") {
        <router-outlet></router-outlet>
      }
    }
  </div>
}
