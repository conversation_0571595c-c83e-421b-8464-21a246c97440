<div class="history-info">
  <div class="history-info-title">
    {{ portfolioMonthly()[4]?.formatDate }}
  </div>
  <div class="history-info-title">
    {{ portfolioMonthly()[3]?.formatDate }}
  </div>
  <div class="history-info-title">
    {{ portfolioMonthly()[2]?.formatDate }}
  </div>

  <div
    class="history-info-number"
    [ngStyle]="{
      color: portfolioMonthly()[3]?.monthlyProfit > 0 ? '#04dc00' : 'red'
    }"
  >
    {{ portfolioMonthly()[3]?.monthlyProfit | profitsNoDecimal }}
  </div>

  <div
    class="history-info-number"
    [ngStyle]="{
      color: portfolioMonthly()[2]?.monthlyProfit > 0 ? '#04dc00' : 'red'
    }"
  >
    {{ portfolioMonthly()[2]?.monthlyProfit | profitsNoDecimal }}
  </div>

  <div
    class="history-info-number"
    [ngStyle]="{
      color: portfolioMonthly()[1]?.monthlyProfit > 0 ? '#04dc00' : 'red'
    }"
  >
    {{ portfolioMonthly()[1]?.monthlyProfit | profitsNoDecimal }}
  </div>
</div>

<apx-chart
  [series]="chartOptions.series"
  [chart]="chartOptions.chart"
  [dataLabels]="chartOptions.dataLabels"
  [plotOptions]="chartOptions.plotOptions"
  [yaxis]="chartOptions.yaxis"
  [xaxis]="chartOptions.xaxis"
  [fill]="chartOptions.fill"
  [title]="chartOptions.title"
  [tooltip]="chartOptions.tooltip"
  [grid]="chartOptions.grid"
></apx-chart>
