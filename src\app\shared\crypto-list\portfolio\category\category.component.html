<div class="crypto-list-header">
  <div class="crypto-list-header-expand" (click)="onHeaderCategoriesClick()">
    @if (currentCategories.length == 0) {
      <i class="fa-solid fa-angles-down"></i>
    } @else {
      <i class="fa-solid fa-angles-up"></i>
    }
  </div>

  <div class="crypto-list-header-name">Category</div>
  <div class="crypto-list-header-price">Deposit %</div>
  <div class="crypto-list-header-deposits">
    Deposit <i class="fa-solid fa-caret-down"></i>
  </div>
</div>

@for (category of portfolio.sortCategory; track category) {
  <div class="crypto">
    <div class="crypto-list-table" (click)="toggleCategory(category.name)">
      <div class="crypto-list-table-logo">
        <img src="{{ category.coins[0].logo }}" />
      </div>
      <div
        class="crypto-list-table-name"
        [ngClass]="{ expanded: currentCategories.includes(category.name) }"
        >
        {{ category.name }}
      </div>
      @if (
        currentCategories.includes(category.name) ||
        currentCategories.includes("All")
        ) {
        <div class="crypto-list-table-ticker">
          @for (coin of category.coins; track coin.ticker) {
            <div class="coin">{{ coin.ticker }}</div>
          }
        </div>
      }
      <div
        class="crypto-list-table-perc"
        [ngClass]="{ expanded: currentCategories.includes(category.name) }"
        >
        {{ category.depositsPerc | profitsPerc }}
      </div>
      <div
        class="crypto-list-table-deposits"
        [ngStyle]="{ color: white }"
        [ngClass]="{ expanded: currentCategories.includes(category.name) }"
        >
        {{ category.deposits | depositsNoDecimal }}
      </div>
      @if (
        currentCategories.includes(category.name) ||
        currentCategories.includes("All")
        ) {
        <div class="crypto-list-table-depositPerc">
          @for (coin of category.coins; track coin.ticker) {
            <div class="coin">{{ coin.depositsPerc | profitsPerc }}</div>
          }
        </div>
      }
      @if (
        currentCategories.includes(category.name) ||
        currentCategories.includes("All")
        ) {
        <div class="crypto-list-table-deposit">
          @for (coin of category.coins; track coin.ticker) {
            <div class="coin">{{ coin.deposits | depositsNoDecimal }}</div>
          }
        </div>
      }
    </div>
    <!-- <app-crypto-info
    [coin]="coin"
    [showInfo]="showInfo"
    [currentCoin]="currentCoin"
  ></app-crypto-info> -->
</div>
}
