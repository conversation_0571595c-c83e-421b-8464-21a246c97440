import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Output,
  ViewChild,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NavigationEnd, Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth/auth.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

@Component({
  selector: 'app-account-selector',
  templateUrl: './account-selector.component.html',
  styleUrls: ['./account-selector.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AccountSelectorComponent {
  protected currentExchange = this.currentAccountService.currentAccount;
  protected currentAccount = this.currentAccountService.currentAccount;
  protected currentUser = this.currentAccountService.currentUser;

  protected accountForm: FormGroup;
  protected exchangeForm: FormGroup;
  protected portfoliosKeys = signal<string[]>([]);
  protected exchangeKeys = signal<string[]>([]);

  protected selectAccount: { [key: string]: string } = {};

  protected exchanges: { [key: string]: string } = {};

  protected selectedAccount = signal<string>('PAC');

  // NOT USED
  // protected showHomeButton  = signal<boolean>(false)

  protected menu = signal<boolean>(false);

  protected addedNews = this.tokeninsightService.addedNews;

  @ViewChild('menuBtn') menuBtn!: ElementRef;
  @ViewChild('sidebar') sidebar!: ElementRef;
  @ViewChild('closeBtn') closeBtn!: ElementRef;
  @ViewChild('subBtn') subBtn!: ElementRef;
  @ViewChild('subMenu') subMenu!: ElementRef;

  protected currentPage = signal('');

  @Output('menuOpen') menuOpen = new EventEmitter();

  constructor(
    private fb: FormBuilder,
    private currentAccountService: CurrentAccountService,
    private router: Router,
    private tokeninsightService: TokeninsightService,
    private authService: AuthService,
  ) {
    if (this.currentAccountService.currentUser()?.username === 'pac') {
      this.selectAccount = {
        PAC: 'portfolio4',
        Francesco: 'portfolio2',
        Andrea: 'portfolio3',
        Elisa: 'portfolio5',
      };
      this.exchanges = {
        'coinbase-fra': 'portfolio',
        'binance-fra': 'portfolio2',
        'binance-andrea': 'portfolio3',
        'binance-elisa': 'portfolio5',
      };
    }

    if (this.currentAccountService.currentUser()?.username === 'elisa') {
      this.selectAccount = {
        Elisa: 'portfolio5',
      };
      this.exchanges = {
        'binance-elisa': 'portfolio5',
      };
    }

    this.accountForm = this.fb.group({
      account: [],
    });
    this.exchangeForm = this.fb.group({
      exchange: [],
    });

    this.currentPage.set(this.router.url.slice(1));
    this.router.events.pipe(takeUntilDestroyed()).subscribe((event) => {
      if (event instanceof NavigationEnd) {
        const currentUrl = event.urlAfterRedirects;
        this.currentPage.set(currentUrl.slice(1));
        this.accountForm.get('account')!.setValue(this.selectedAccount());
      }
      console.log('CURRENT PAGE', this.currentPage());
    });

    this.portfoliosKeys.set(Object.keys(this.selectAccount));
    this.exchangeKeys.set(Object.keys(this.exchanges));

    this.accountForm.get('account')!.valueChanges.subscribe((value) => {
      console.log('CAMBIO FORM ACCOUNT');
      this.selectedAccount.set(value);
      this.getCurrentPortfolio(this.selectAccount[value]);
    });

    this.exchangeForm.get('exchange')!.valueChanges.subscribe((value) => {
      if (value == this.currentExchange()) return;
      this.getCurrentPortfolio(this.exchanges[value]);
    });

    // this.router.events.subscribe((event) => {
    //   if (event instanceof NavigationEnd) {
    //     event.urlAfterRedirects == '/account-detail'
    //       ? this.showHomeButton.set(true)
    //       : this.showHomeButton.set(false)
    //   }
    // });

    //  effect(
    //  () => {
    // if (this.currentAccountService.currentAccount() == 'pac')
    //   this.accountForm.get('account').setValue('PAC');
    // if (
    //   this.currentAccountService.currentAccount() == 'binance-fra' ||
    //   this.currentAccountService.currentAccount() == 'coinbase-fra'
    // )
    //   this.accountForm.get('account').setValue('Francesco');
    // if (this.currentAccountService.currentAccount() == 'binance-andrea')
    //   this.accountForm.get('account').setValue('Andrea');
    //    },

    //);
    // console.log('CURRENT USER', this.currentAccountService.currentUser());
    // console.log('CURRENT ACCOUNT', this.currentAccountService.currentAccount());
    // console.log(
    //   'CURRENT EXCHANGE',
    //   this.currentAccountService.currentExchange()
    // );
    // console.log(
    //   'CURRENT PORTFOLIO',
    //   this.currentAccountService.currentPortfolio()
    // );
  }

  onMenuBtnClick() {
    this.sidebar.nativeElement.classList.add('active');
    this.menuBtn.nativeElement.style.visibility = 'hidden';
    this.menu.set(true);
    this.menuOpen.emit(true);
  }

  onCLoseBtnClick() {
    this.sidebar.nativeElement.classList.remove('active');
    this.menuBtn.nativeElement.style.visibility = 'visible';
    this.menu.set(false);
    this.menuOpen.emit(false);
  }

  onSubMenuClick() {
    let subMenuOpen = this.subMenu.nativeElement.style.display;

    this.subMenu.nativeElement.style.display =
      subMenuOpen == 'block' ? 'none' : 'block';
    this.subBtn.nativeElement
      .querySelector('.dropdown')
      .classList.toggle('rotate');

    let subBtnColor = this.subBtn.nativeElement.style.backgroundColor;
    subBtnColor == '#33363a' ? '#1b1a1b' : '#33363a';
  }
  getCurrentPortfolio(exchange) {
    this.currentAccountService.getCurrentPortfolio(exchange);
  }

  onHomeClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/');

    console.log('FORM2', this.accountForm.get('account').value);
  }

  onNewsClick() {
    this.onCLoseBtnClick();
    this.router.navigateByUrl('/news');
  }

  onReportClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/account-detail');
  }

  onDepositHistoryClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/account-deposits');
  }

  onBlockchainStatsClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/blockchain-stats');
  }

  onAltcoinSeasonClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/altcoin-season');
  }

  onCryptoVolumesClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/crypto-volumes');
  }

  onTokenStatsCLick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.addedNews.set([]);
    this.router.navigateByUrl('/token-stats');
  }

  onLogoutClick() {
    this.onCLoseBtnClick();
    this.tokeninsightService.loadingNews$.next(null);
    this.tokeninsightService.loading$.next(null);
    this.authService.SignOut();
    setTimeout(() => {
      location.reload();
    }, 1000);
  }

  clearLocalStorage() {
    const keysToKeep = ['cryptoNews', 'dateNews', 'cryptopanicNews', 'user'];
    const localStorageKeys = Object.keys(localStorage);

    localStorageKeys.forEach((key) => {
      if (!keysToKeep.includes(key)) {
        localStorage.removeItem(key);
      }
    });

    console.log(JSON.parse(localStorage.getItem('coins1')));
  }
}
