import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { authGuard } from 'src/app/core/guards/auth.guard';
import { AltcoinSeasonComponent } from './altcoin-season.component';

const routes: Routes = [
  { path: '', component: AltcoinSeasonComponent, canActivate: [authGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AltcoinSeasonRoutingModule {}
