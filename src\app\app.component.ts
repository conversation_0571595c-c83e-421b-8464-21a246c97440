import {
  ChangeDetectionStrategy,
  Component,
  effect,
  Inject,
  OnInit,
  Renderer2,
  signal,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CurrentAccountService } from './core/services/data/current-account.service';
// import { FirebaseService } from './core/services/http/dbFirebase.service';
import { DOCUMENT } from '@angular/common';
import { SwUpdate } from '@angular/service-worker';
import { AuthService } from './core/services/auth/auth.service';
import { TokeninsightService } from './core/services/http/tokeninsight.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {},
})
export class AppComponent implements OnInit {
  title = 'cripto-tracker';

  protected updateModalConfig = signal<{
    modalContentTitle: string;
    modalContentText: string;
  }>({
    modalContentTitle: 'Nuova versione disponibile',
    modalContentText: 'Aggiornamento in corso...',
  });

  protected updateModalInterval: any;

  protected modalCounterSeconds = signal<number>(5);

  protected showUpdateModal = signal<boolean>(false);
  protected modalCounterSecondsEffects = effect(
    () => {
      if (this.showUpdateModal()) {
        this.renderer.setStyle(this.document.body, 'overflow', 'hidden');

        if (this.modalCounterSeconds() < 1) {
          clearInterval(this.updateModalInterval);
          this.onUpdate();
        }

        // Imposta l'interval solo se non è già attivo
        if (this.modalCounterSeconds() === 5) {
          this.updateModalInterval = setInterval(() => {
            this.modalCounterSeconds.set(this.modalCounterSeconds() - 1);
          }, 1000);
        }
      } else {
        this.renderer.removeStyle(this.document.body, 'overflow');
      }
    },
    {
      allowSignalWrites: true,
    },
  );

  protected loading = signal<boolean>(true);
  protected currentPortfolio = this.currentAccountService.currentPortfolio;
  protected currentUrl = signal<string | undefined>('');
  protected pageLoading = signal(true);
  protected menuOpen = false;
  localStorageSize: string;
  protected currentDevice = signal<'mobile' | 'desktop' | undefined>(undefined);

  constructor(
    private currentAccountService: CurrentAccountService,
    private tokeninsightService: TokeninsightService,
    private router: Router,
    private authService: AuthService,
    private swUpdate: SwUpdate,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
  ) {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.currentUrl.set(event.urlAfterRedirects);
        this.menuOpen = false;
        this.pageLoading.set(false);
      }
    });
    this.checkForPWAUpdate();
    this.checkCurrentDevice();
  }

  ngOnInit() {
    // this.authService.userData$.subscribe((data) => {
    //   if (data != null) {
    //     this.tokeninsightService.loading$.subscribe((data) => {
    //       if (data != 'ok') {
    //         this.loading = true;
    //       }
    //       if (data == 'ok') {
    //         this.loading = false;
    //       }
    //     });
    //   }
    // });

    // TODO Implementare API Binance
    // this.http
    //   .get('http://localhost:3000/binance/total?quoteAsset=EUR')
    //   .subscribe((data) => {
    //     console.log(data);
    //   });

    this.tokeninsightService.loading$.subscribe((data) => {
      if (data != 'ok') {
        this.loading.set(true);
      }
      if (
        data == 'ok' &&
        this.authService.isLoggedIn &&
        this.currentAccountService.currentUser() !== null
      ) {
        this.tokeninsightService.loadingNews$.subscribe((data) => {
          if (data == 'ok News') {
            if (this.currentUrl() == '/login') {
              this.router.navigate(['/']);
            }
            this.loading.set(false);
          }
        });
      }
    });
    this.calculateLocalStorageSize();
  }

  checkForPWAUpdate() {
    if (this.swUpdate.isEnabled) {
      this.swUpdate.versionUpdates.subscribe((v) => {
        if (v.type === 'VERSION_READY') {
          this.onUpdate();
        }
      });

      // Forza il controllo immediato per un aggiornamento
      this.swUpdate.checkForUpdate().then((isUpdateAvailable: boolean) => {
        if (isUpdateAvailable) this.showUpdateModal.set(true);
      });
    }
  }

  checkCurrentDevice() {
    const mql = window.matchMedia('(max-width: 900px)');

    if (mql.matches) {
      this.currentDevice.set('mobile');
    } else {
      this.currentDevice.set('desktop');
    }

    mql.onchange = (e) => {
      if (e.matches) {
        /* the viewport is 900 pixels wide or less */
        this.currentDevice.set('mobile');
        console.log('This is a narrow screen — less than 900px wide.');
      } else {
        /* the viewport is more than 900 pixels wide */
        this.currentDevice.set('desktop');
        console.log('This is a wide screen — more than 900px wide.');
      }
    };
  }

  calculateLocalStorageSize() {
    let totalSize = 0;
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        const value = localStorage.getItem(key);
        if (value) {
          totalSize += (key.length + value.length) * 2; // Approximation of size in bytes
        }
      }
    }
    this.localStorageSize = this.formatBytes(totalSize);
    console.log(this.localStorageSize);
  }
  formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  onUpdate() {
    setTimeout(() => {
      if (this.authService.isLoggedIn) {
        this.authService.SignOut().then(() => {
          window.location.reload();
        });
      } else {
        window.location.reload();
      }
    }, 2000);
  }

  onMenuClick(value: boolean) {
    this.menuOpen = value;
  }

  onUpdateModalClick() {
    this.onUpdate();
  }

  ngOnDestroy(): void {
    this.updateModalInterval = undefined;
  }
}
