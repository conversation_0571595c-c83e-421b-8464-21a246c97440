<app-loader-spinner [loading]="loading()"></app-loader-spinner>

@if (!loading()) {
  <div class="news">
    <!-- <div class="header">
    News <i class="fa-solid fa-house" (click)="onHomeCLick()"></i>
  </div> -->

    <div class="tabs">
      <select name="" id="" (change)="onCategoryClick($event)">
        @for (category of categories(); track category) {
          <option
            [value]="category.name"
            [selected]="currentCategory == category.name"
          >
            {{ category.name }} ({{ category.count }})
          </option>
        }
      </select>
    </div>

    <div class="empty"></div>
    @for (
      item of currentCategory() === "All Categories"
        ? filteredNewsList().slice(0, 50)
        : filteredNewsList();
      track item.title
    ) {
      <div
        class="list"
        [ngStyle]="{
          'border-bottom': contentExpandedTitle == item.title ? 'none' : null,
          'margin-bottom': contentExpandedTitle == item.title ? 0 : null,
          'padding-bottom': contentExpandedTitle == item.title ? 0 : null
        }"
      >
        <div class="image">
          <img [src]="item.image_url" width="100px" alt="" />
          @if (findAddedNews(item.title)) {
            <div class="notification">!</div>
          }
        </div>

        <div class="tags">
          @for (tag of item?.tags; track tag) {
            <div class="tag" (click)="onTagClick(tag.name)">
              {{ tag.name }}
            </div>
          }
        </div>
        @if (!item.content) {
          <a class="title" [href]="item.url" target="_blank">{{
            item.title
          }}</a>
        } @else {
          <a
            class="title mobile"
            (click)="
              contentExpandedTitle = contentExpandedTitle
                ? (contentExpandedTitle = '')
                : (contentExpandedTitle = item.title)
            "
            target="_blank"
            >{{ item.title }}</a
          >
          <div class="title">{{ item.title }}</div>
        }

        <div class="date">
          {{ item.timestamp | date }} • {{ item.timestamp | timeFromNow }}
        </div>
      </div>

      @if (item.content && contentExpandedTitle == item.title) {
        <div class="content" [innerHTML]="formatText(item.content)"></div>
      }
    }
  </div>
}
