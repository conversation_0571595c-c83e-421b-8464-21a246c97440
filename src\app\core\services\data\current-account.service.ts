import { Injectable, signal } from '@angular/core';
import { concatMap, tap } from 'rxjs';
import { IUserList, USER_LIST } from '../../interfaces/user';
import { CoingeckoService } from '../http/coingecko.service';
import { CryptopanicService } from '../http/cryptopanic.service';
import { FirebaseService } from '../http/dbFirebase.service';
import { TokeninsightService } from '../http/tokeninsight.service';
import { PortfolioBinanceAndreaHistory } from './binance-andrea-history.service';
import { PortfolioBinanceAndrea } from './binance-andrea.service';
import { PortfolioBinanceElisaHistory } from './binance-elisa-history.service';
import { PortfolioBinanceElisa } from './binance-elisa.service';
import { PortfolioBinanceFraHistory } from './binance-fra-history.service';
import { PortfolioBinanceFra } from './binance-fra.service';
import { PortfolioCoinbaseFraHistory } from './coinbase-fra-history.service';
import { PortfolioCoinbaseFra } from './coinbase-fra.service';
import { PortfolioPACHistory } from './pac-history.service';
import { PortfolioPAC } from './pac.service';

@Injectable({
  providedIn: 'root',
})
export class CurrentAccountService {
  public currentPortfolio = signal<any>(null);

  public currentPortfolioHistory = signal<any>(null);

  public currentPortfolioTrades = signal<any[]>([]);

  public currentAccount = signal<
    'pac' | 'coinbase-fra' | 'binance-fra' | 'binance-andrea' | 'binance-elisa'
  >(null);

  public currentUser = signal<IUserList>(null);

  constructor(
    protected portfolio: PortfolioCoinbaseFra,
    protected portfolio2: PortfolioBinanceFra,
    protected portfolio3: PortfolioBinanceAndrea,
    protected portfolio4: PortfolioPAC,
    protected portfolio5: PortfolioBinanceElisa,
    protected portfolioHistory: PortfolioCoinbaseFraHistory,
    protected portfolio2History: PortfolioBinanceFraHistory,
    protected portfolio3History: PortfolioBinanceAndreaHistory,
    protected portfolio4History: PortfolioPACHistory,
    protected portfolio5History: PortfolioBinanceElisaHistory,
    private firebaseService: FirebaseService,
    private coingeckoService: CoingeckoService,
    private tokeninsightService: TokeninsightService,
    private cryptopanicService: CryptopanicService,
  ) {
    this.tokeninsightService.loading$.subscribe((data) => {
      if (data == 'ok') {
        if (JSON.parse(localStorage.getItem('user')) !== null) {
          let userFound = USER_LIST.find(
            (user) =>
              user.email === JSON.parse(localStorage.getItem('user')).email,
          );

          this.currentUser.set({
            username: userFound.username,
            email: JSON.parse(localStorage.getItem('user')).email,
          });

          if (userFound.username === 'pac') {
            this.currentAccount.set('pac');
            this.currentPortfolio.set(this.portfolio4);
            this.currentPortfolioTrades.set(this.portfolio4.closedTrades);
            this.currentPortfolioHistory.set(this.portfolio4History);
          }

          if (userFound.username === 'elisa') {
            // console.log('USER FOUND elisa', userFound);
            this.currentAccount.set('binance-elisa');
            this.currentPortfolio.set(this.portfolio5);
            this.currentPortfolioTrades.set(this.portfolio5.closedTrades);
            this.currentPortfolioHistory.set(this.portfolio5History);
          }
        }
      }
    });

    // console.log('CURRENT USER', this.currentUser());
    // console.log('CURRENT ACCOUNT', this.currentAccount());
    // console.log('CURRENT EXCHANGE', this.currentExchange());
    // console.log('CURRENT PORTFOLIO', this.currentPortfolio());
  }

  public getCurrentPortfolio(exchange) {
    console.log('EXCHANGE', exchange);
    if (exchange == 'portfolio') {
      this.currentPortfolio.set(this.portfolio);
      this.currentPortfolioHistory.set(this.portfolioHistory);
      this.currentPortfolioTrades.set(this.portfolio.closedTrades);
      this.currentAccount.set('coinbase-fra');
    }

    if (exchange == 'portfolio2') {
      this.currentPortfolio.set(this.portfolio2);
      this.currentPortfolioHistory.set(this.portfolio2History);
      this.currentPortfolioTrades.set(this.portfolio2.closedTrades);
      this.currentAccount.set('binance-fra');
    }

    if (exchange == 'portfolio3') {
      this.currentPortfolio.set(this.portfolio3);
      this.currentPortfolioHistory.set(this.portfolio3History);
      this.currentPortfolioTrades.set(this.portfolio3.closedTrades);
      this.currentAccount.set('binance-andrea');
    }

    if (exchange == 'portfolio4') {
      this.currentPortfolio.set(this.portfolio4);
      this.currentPortfolioTrades.set(this.portfolio4.closedTrades);
      this.currentPortfolioHistory.set(this.portfolio4History);
      this.currentAccount.set('pac');
    }

    if (exchange == 'portfolio5') {
      this.currentPortfolio.set(this.portfolio5);
      this.currentPortfolioHistory.set(this.portfolio5History);
      this.currentPortfolioTrades.set(this.portfolio5.closedTrades);
      this.currentAccount.set('binance-elisa');
    }

    console.log(this.currentPortfolio());
    console.log(this.currentPortfolioHistory());
    console.log(this.currentAccount());
    console.log('closed trades', this.currentPortfolioTrades());

    // console.log(this.currentUser());
  }

  public async loadPortfolios(email: string) {
    let username: string;
    if (USER_LIST.find((user) => user.email == email)) {
      username = USER_LIST.find((user) => user.email == email).username;
      // console.log('USERNAME', username);
      if (username == 'pac') {
        console.log('USERNAME', username);
        // ETF DATA - Firestore
        // await new Promise((resolve) => {
        //   this.firebaseService.getEtfStats().subscribe({
        //     next: (data: any) => {
        //       // let newEtfHistoryWeekly = data[2].weeklyHistory.reverse();
        //       let newEtfHistoryDaily = data[0].dailyHistory.reverse();
        //       // console.log('ETF DATA', newEtfHistoryDaily);

        //       if (localStorage.getItem('etfHistory')) {
        //         if (
        //           JSON.parse(localStorage.getItem('etfHistory'))[0].date ==
        //           newEtfHistoryDaily[0].date
        //         ) {
        //           this.firebaseService.etfHistory.set(newEtfHistoryDaily);
        //         } else {
        //           this.firebaseService.etfHistory.set(newEtfHistoryDaily);
        //           this.firebaseService.etfStatsNewDate.set(true);
        //           localStorage.setItem(
        //             'etfHistory',
        //             JSON.stringify(newEtfHistoryDaily),
        //           );
        //         }
        //         resolve(true);
        //       } else {
        //         this.firebaseService.etfHistory.set(newEtfHistoryDaily);
        //         this.firebaseService.etfStatsNewDate.set(true);
        //         localStorage.setItem(
        //           'etfHistory',
        //           JSON.stringify(newEtfHistoryDaily),
        //         );
        //         resolve(true);
        //       }
        //     },
        //     error: (error) => {
        //       console.log(error.message);
        //       resolve(false);
        //     },
        //   });
        // });

        if (
          this.coingeckoService.shouldFetchData() &&
          this.tokeninsightService.shouldFetchRating()
        ) {
          this.coingeckoService
            .getCryptoStats()
            .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
            .subscribe();
        }

        if (
          this.coingeckoService.shouldFetchData() &&
          !this.tokeninsightService.shouldFetchRating()
        ) {
          this.coingeckoService
            .getCryptoStats()
            .pipe(
              tap(() => {
                this.tokeninsightService.setCoinsRating(
                  JSON.parse(localStorage.getItem('coinsRating')),
                );
              }),
            )
            .subscribe();
        }

        if (!this.coingeckoService.shouldFetchData()) {
          if (
            localStorage.getItem('portfolioMonthly') ||
            localStorage.getItem('portfolio5Monthly')
          ) {
            this.coingeckoService.btcPrice.set(
              JSON.parse(localStorage.getItem('btcPrice')),
            );

            this.coingeckoService.ethPrice.set(
              JSON.parse(localStorage.getItem('ethPrice')),
            );

            this.coingeckoService.btc24hChange.set(
              JSON.parse(localStorage.getItem('btcPriceChange24h')),
            );

            this.coingeckoService.eth24hChange.set(
              JSON.parse(localStorage.getItem('ethPriceChange24h')),
            );

            this.coingeckoService.eurusd.set(
              JSON.parse(localStorage.getItem('eurusd')),
            );

            this.coingeckoService.btcMarketD.set(
              JSON.parse(localStorage.getItem('btcMarketD')),
            );

            this.coingeckoService.ethMarketD.set(
              JSON.parse(localStorage.getItem('ethMarketD')),
            );

            // Portfolios

            this.portfolioHistory.portfolioMonthly = JSON.parse(
              localStorage.getItem('portfolioMonthly'),
            );

            this.portfolio2History.portfolioMonthly = JSON.parse(
              localStorage.getItem('portfolio2Monthly'),
            );
            this.portfolio3History.portfolioMonthly = JSON.parse(
              localStorage.getItem('portfolio3Monthly'),
            );
            this.portfolio4History.portfolioMonthly = JSON.parse(
              localStorage.getItem('portfolio4Monthly'),
            );
            this.portfolio5History.portfolioMonthly = JSON.parse(
              localStorage.getItem('portfolio5Monthly'),
            );

            this.portfolio.coins = JSON.parse(localStorage.getItem('coins'));
            this.portfolio.portfolioStats = JSON.parse(
              localStorage.getItem('portfolioStats'),
            );

            this.portfolio2.coins = JSON.parse(localStorage.getItem('coins2'));
            this.portfolio2.portfolioStats = JSON.parse(
              localStorage.getItem('portfolio2Stats'),
            );

            this.portfolio3.coins = JSON.parse(localStorage.getItem('coins3'));
            this.portfolio3.portfolioStats = JSON.parse(
              localStorage.getItem('portfolio3Stats'),
            );

            this.portfolio4.coins = JSON.parse(localStorage.getItem('coins4'));
            this.portfolio4.portfolioStats = JSON.parse(
              localStorage.getItem('portfolio4Stats'),
            );
            this.portfolio5.coins = JSON.parse(localStorage.getItem('coins5'));
            this.portfolio5.portfolioStats = JSON.parse(
              localStorage.getItem('portfolio5Stats'),
            );

            if (!this.tokeninsightService.shouldFetchRating()) {
              this.tokeninsightService.setCoinsRating(
                JSON.parse(localStorage.getItem('coinsRating')),
              );
            }

            if (this.tokeninsightService.shouldFetchRating()) {
              this.tokeninsightService.getCryptoRatings().subscribe(() => {});
            }
          }
          if (
            !localStorage.getItem('portfolioMonthly') ||
            !localStorage.getItem('portfolio5Monthly')
          ) {
            this.coingeckoService
              .getCryptoStats()
              .pipe(
                concatMap(() => this.tokeninsightService.getCryptoRatings()),
              )
              .subscribe();
          }
        }

        if (this.tokeninsightService.shouldFetchNews()) {
          this.tokeninsightService
            .getCryptoNews()
            .pipe(
              concatMap((data) => {
                // console.log('NEWS OK', this.tokeninsightService.cryptoNews());

                return this.cryptopanicService.getUpdates();
              }),
            )
            .subscribe({
              next: (data) => {
                if (localStorage.getItem('cryptoNews')) {
                  const prevNews = [
                    ...this.tokeninsightService.prevNews1,
                    ...this.cryptopanicService.prevNews2,
                  ];

                  const actualNews = [
                    ...this.tokeninsightService.cryptoNews(),
                    ...this.cryptopanicService.cryptopanicList(),
                  ];
                  // console.log('PREV NEWS', prevNews);
                  // console.log('ACTUAL NEWS', actualNews);

                  function sonoOggettiUguali(objA, objB) {
                    // Implementa il tuo metodo di confronto, ad esempio confronto di chiavi e valori
                    // Questo esempio assume che gli oggetti abbiano gli stessi campi
                    return JSON.stringify(objA) === JSON.stringify(objB);
                  }

                  let addedNews2 = [];

                  actualNews.forEach((item) => {
                    let oggettoOrigine = prevNews.find((oggetto) => {
                      return oggetto.title === item.title;
                    });

                    if (
                      !oggettoOrigine ||
                      !sonoOggettiUguali(oggettoOrigine, item)
                    ) {
                      addedNews2.push(item);
                    }
                  });

                  this.tokeninsightService.addedNews.set(addedNews2);
                  this.tokeninsightService.loadingNews$.next('ok News');

                  // console.log('ADDED NEWS', addedNews2);
                }
              },

              error: (error) => {
                console.error(error);
              },
            });
        }

        if (!this.tokeninsightService.shouldFetchNews()) {
          this.tokeninsightService.addedNews.set([]);

          this.tokeninsightService.cryptoNews.set(
            JSON.parse(localStorage.getItem('cryptoNews')),
          );

          this.cryptopanicService.cryptopanicList.set(
            JSON.parse(localStorage.getItem('cryptopanicNews')),
          );

          this.tokeninsightService.loadingNews$.next('ok News');

          // tokeninsightService.loadingNews$.next('ok News');

          // cryptopanicService.loading$.next('ok');

          // console.log('NEWS OK - localstorage', tokeninsightService.cryptoNews());
        }

        return Promise.resolve();
      }

      if (username == 'elisa') {
        await new Promise((resolve) => {
          this.firebaseService.getEtfStats().subscribe({
            next: (data: any) => {
              // let newEtfHistoryWeekly = data[2].weeklyHistory.reverse();
              let newEtfHistoryDaily = data[0].dailyHistory.reverse();
              // console.log('ETF DATA', newEtfHistoryDaily);

              if (localStorage.getItem('etfHistory')) {
                if (
                  JSON.parse(localStorage.getItem('etfHistory'))[0].date ==
                  newEtfHistoryDaily[0].date
                ) {
                  this.firebaseService.etfHistory.set(newEtfHistoryDaily);
                } else {
                  this.firebaseService.etfHistory.set(newEtfHistoryDaily);
                  this.firebaseService.etfStatsNewDate.set(true);
                  localStorage.setItem(
                    'etfHistory',
                    JSON.stringify(newEtfHistoryDaily),
                  );
                }
                resolve(true);
              } else {
                this.firebaseService.etfHistory.set(newEtfHistoryDaily);
                this.firebaseService.etfStatsNewDate.set(true);
                localStorage.setItem(
                  'etfHistory',
                  JSON.stringify(newEtfHistoryDaily),
                );
                resolve(true);
              }
            },
            error: (error) => {
              console.log(error.message);
              resolve(false);
            },
          });
        });

        if (
          this.coingeckoService.shouldFetchData() &&
          this.tokeninsightService.shouldFetchRating()
        ) {
          this.coingeckoService
            .getCryptoStatsSingleAccount('portfolio5')
            .pipe(
              concatMap(() =>
                this.tokeninsightService.getCryptoRatings('singleAccount'),
              ),
            )
            .subscribe();
        }

        if (
          this.coingeckoService.shouldFetchData() &&
          !this.tokeninsightService.shouldFetchRating()
        ) {
          this.coingeckoService
            .getCryptoStatsSingleAccount('portfolio5')
            .pipe(
              tap(() => {
                this.tokeninsightService.setCoinsRatingSinglePortfolio(
                  JSON.parse(localStorage.getItem('coinsRating')),
                );
              }),
            )
            .subscribe();
        }

        if (!this.coingeckoService.shouldFetchData()) {
          if (localStorage.getItem('portfolio5Monthly')) {
            this.coingeckoService.btcPrice.set(
              JSON.parse(localStorage.getItem('btcPrice')),
            );

            this.coingeckoService.ethPrice.set(
              JSON.parse(localStorage.getItem('ethPrice')),
            );

            this.coingeckoService.btc24hChange.set(
              JSON.parse(localStorage.getItem('btcPriceChange24h')),
            );

            this.coingeckoService.eth24hChange.set(
              JSON.parse(localStorage.getItem('ethPriceChange24h')),
            );

            this.coingeckoService.eurusd.set(
              JSON.parse(localStorage.getItem('eurusd')),
            );

            this.coingeckoService.btcMarketD.set(
              JSON.parse(localStorage.getItem('btcMarketD')),
            );

            this.coingeckoService.ethMarketD.set(
              JSON.parse(localStorage.getItem('ethMarketD')),
            );

            this.portfolio5History.portfolioMonthly = JSON.parse(
              localStorage.getItem('portfolio5Monthly'),
            );

            this.portfolio5.coins = JSON.parse(localStorage.getItem('coins5'));
            this.portfolio5.portfolioStats = JSON.parse(
              localStorage.getItem('portfolio5Stats'),
            );

            if (!this.tokeninsightService.shouldFetchRating()) {
              this.tokeninsightService.setCoinsRatingSinglePortfolio(
                JSON.parse(localStorage.getItem('coinsRating')),
              );
            }

            if (this.tokeninsightService.shouldFetchRating()) {
              this.tokeninsightService
                .getCryptoRatings('singleAccount')
                .subscribe(() => {});
            }
          }
        }

        if (this.tokeninsightService.shouldFetchNews()) {
          this.tokeninsightService
            .getCryptoNews()
            .pipe(
              concatMap((data) => {
                // console.log('NEWS OK', this.tokeninsightService.cryptoNews());

                return this.cryptopanicService.getUpdates();
              }),
            )
            .subscribe({
              next: (data) => {
                if (localStorage.getItem('cryptoNews')) {
                  const prevNews = [
                    ...this.tokeninsightService.prevNews1,
                    ...this.cryptopanicService.prevNews2,
                  ];

                  const actualNews = [
                    ...this.tokeninsightService.cryptoNews(),
                    ...this.cryptopanicService.cryptopanicList(),
                  ];
                  // console.log('PREV NEWS', prevNews);
                  // console.log('ACTUAL NEWS', actualNews);

                  function sonoOggettiUguali(objA, objB) {
                    // Implementa il tuo metodo di confronto, ad esempio confronto di chiavi e valori
                    // Questo esempio assume che gli oggetti abbiano gli stessi campi
                    return JSON.stringify(objA) === JSON.stringify(objB);
                  }

                  let addedNews2 = [];

                  actualNews.forEach((item) => {
                    let oggettoOrigine = prevNews.find((oggetto) => {
                      return oggetto.title === item.title;
                    });

                    if (
                      !oggettoOrigine ||
                      !sonoOggettiUguali(oggettoOrigine, item)
                    ) {
                      addedNews2.push(item);
                    }
                  });

                  this.tokeninsightService.addedNews.set(addedNews2);

                  this.tokeninsightService.loadingNews$.next('ok News');

                  // console.log('ADDED NEWS', addedNews2);
                }
              },

              error: (error) => {
                console.error(error);
              },
            });
        }

        if (!this.tokeninsightService.shouldFetchNews()) {
          this.tokeninsightService.addedNews.set([]);

          this.tokeninsightService.cryptoNews.set(
            JSON.parse(localStorage.getItem('cryptoNews')),
          );

          this.cryptopanicService.cryptopanicList.set(
            JSON.parse(localStorage.getItem('cryptopanicNews')),
          );

          this.tokeninsightService.loadingNews$.next('ok News');

          // this.tokeninsightService.loadingNews$.next('ok News');

          // this.cryptopanicService.loading$.next('ok');

          // console.log('NEWS OK - localstorage', this.tokeninsightService.cryptoNews());
        }
      }
    }
  }
}
