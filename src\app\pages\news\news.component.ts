import { Component, OnInit, signal } from '@angular/core';
import { Router } from '@angular/router';
import { CryptopanicService } from 'src/app/core/services/http/cryptopanic.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

@Component({
  selector: 'app-news',
  templateUrl: './news.component.html',
  styleUrl: './news.component.scss',
})
export class NewsComponent implements OnInit {
  protected newsList = [];
  protected filteredNewsList = signal([]);
  protected categories = signal([]);
  protected currentCategory = signal<string>('All Categories');
  protected pageLoading$ = signal(true);
  protected cryptopanicList = [];
  protected addedNews = this.tokeninsightService.addedNews;
  protected contentExpandedTitle = '';
  protected loading = signal<boolean>(true);

  constructor(
    private tokeninsightService: TokeninsightService,
    private cryptopanicService: CryptopanicService,
    private router: Router,
  ) {
    console.log('news', this.tokeninsightService.shouldFetchNews());

    this.cryptopanicService.loading$.subscribe((data) => {
      if (data == 'ok') {
        console.log('CRYPTOPANIC LOGGER');

        this.setNews(this.tokeninsightService.cryptoNews());
        this.newsList.push(...this.cryptopanicService.cryptopanicList());

        this.filteredNewsList().push(
          ...this.cryptopanicService.cryptopanicList(),
        );
        this.filteredNewsList.set(
          this.filteredNewsList().sort((a, b) =>
            b.timestamp > a.timestamp ? 1 : -1,
          ),
        );

        let allCategories = this.categories().find(
          (item) => item.name == 'All Categories',
        );

        if (allCategories) {
          this.categories().find(
            (item) => item.name == 'All Categories',
          ).count =
            this.categories().find((item) => item.name == 'All Categories')
              .count +
            this.filteredNewsList().filter(
              (item) => item.tags[0]?.name == 'Top News',
            ).length;
        } else {
          this.categories().push({
            name: 'All Categories',
            count: this.newsList.length,
          });
        }

        this.categories().push({
          name: 'Top News',
          count: this.filteredNewsList().filter(
            (item) => item.tags[0].name == 'Top News',
          ).length,
        });

        this.categories.set(
          this.categories().sort((a, b) => b.count - a.count),
        );
      }
      this.loading.set(false);
    });

    this.setNews(this.tokeninsightService.cryptoNews());
    this.newsList.push(...this.cryptopanicService.cryptopanicList());

    this.filteredNewsList().push(...this.cryptopanicService.cryptopanicList());
    this.filteredNewsList.set(
      this.filteredNewsList().sort((a, b) =>
        b.timestamp > a.timestamp ? 1 : -1,
      ),
    );

    let allCategories = this.categories().find(
      (item) => item.name == 'All Categories',
    );

    if (allCategories) {
      this.categories().find((item) => item.name == 'All Categories').count =
        this.categories().find((item) => item.name == 'All Categories').count +
        this.filteredNewsList().filter(
          (item) => item.tags[0]?.name == 'Top News',
        ).length;
    } else {
      this.categories().push({
        name: 'All Categories',
        count: this.newsList.length,
      });
    }

    this.categories().push({
      name: 'Top News',
      count: this.filteredNewsList().filter(
        (item) => item.tags[0].name == 'Top News',
      ).length,
    });

    this.categories.set(this.categories().sort((a, b) => b.count - a.count));
    this.loading.set(false);
  }
  ngOnInit(): void {
    throw new Error('Method not implemented.');
  }

  setNews(data: any) {
    this.newsList.push(...data);

    this.filteredNewsList().push(...data);

    const newsOccurrences: { [name: string]: number } = {};

    // Iterazione attraverso ogni elemento nell'array
    this.filteredNewsList().forEach((news: any) => {
      news.tags.forEach((tag) => {
        const tagName = tag.name;
        newsOccurrences[tagName] = (newsOccurrences[tagName] || 0) + 1;
      });
    });

    // Creazione dell'array finale
    let tokeninsightCategories = Object.keys(newsOccurrences).map(
      (tagName) => ({
        name: tagName,
        count: newsOccurrences[tagName],
      }),
    );

    let allCategories = this.categories().find(
      (item) => item.name == 'All Categories',
    );
    allCategories
      ? (this.categories().find((item) => item.name == 'All Categories').count =
          allCategories.count =
            this.newsList.length)
      : this.categories().push({
          name: 'All Categories',
          count: this.newsList.length,
        });

    this.categories().push(
      ...tokeninsightCategories.sort((a, b) => b.count - a.count),
    );

    // console.log('UNIQUE TAGS', this.categories());
    this.filteredNewsList.set(
      this.filteredNewsList().sort((a, b) =>
        b.timestamp > a.timestamp ? 1 : -1,
      ),
    );

    this.pageLoading$.set(false);
    // console.log('SET', this.categories());
  }
  onCategoryClick(data) {
    // console.log('OK', data.target.value);
    let tagName = data.target.value;

    console.log('NEWSLIST', this.newsList);

    if (tagName == 'All Categories') {
      this.filteredNewsList.set(
        this.newsList.sort((a, b) => (b.timestamp > a.timestamp ? 1 : -1)),
      );
    } else {
      this.filteredNewsList.set(
        this.newsList.filter((item) =>
          item.tags.find((tag) => tag.name == tagName),
        ),
      );
      console.log('FILTERED NEWS', this.filteredNewsList());
    }
  }

  onTagClick(tagName: string) {
    this.filteredNewsList.set(
      this.newsList
        .filter((item) => item.tags.find((tag) => tag.name == tagName))
        .sort((a, b) => (b.timestamp > a.timestamp ? 1 : -1)),
    );
    this.currentCategory.set(tagName);
    console.log('FILTERED NEWS', this.filteredNewsList());
  }

  findAddedNews(title) {
    return this.addedNews().find((item) => item.title == title);
  }

  formatText(text: string): string {
    // Dividi il testo in frasi utilizzando il punto come delimitatore
    const sentences = text.split('. ');

    // Unisci le frasi aggiungendo <br><br> dopo ogni frase
    let formattedText = sentences.join('. <br><br>');

    // Rimuovi gli ultimi due <br>
    formattedText = formattedText.replace(/\. <br><br>$/, '');

    return formattedText;
  }

  onHomeCLick() {
    this.router.navigateByUrl('/');
  }
}
