.header {
  background-color: rgba(255, 166, 0, 0.5);
  padding: 1rem;
  font-size: 1.2rem;
  font-weight: 500;
  margin: 1rem;
  border-radius: 10px;
}

// background-color: #43495c;

.coinbase,
.crypto-com,
.binance {
  width: 100%;
  height: 40px;
  padding: 0.7rem 0;
  border-bottom: none;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

hr {
  background-color: white;
  color: white;
  height: 2px;
  border-width: 0;
}

.home {
  width: 100%;
  padding: 1rem;

  & .home-desktop-1 {
    perspective: 1000px;

    & .card {
      transform-style: preserve-3d;
      // transition: transform 1s;
      // transform-origin: center right;
      // transition-timing-function: ease-in-out;
      margin-bottom: 1.5rem;

      position: relative;
      width: 100%;
      height: 283.67px;
      z-index: 999;

      .card-inner {
        width: 100%;
        height: 100%;
        position: absolute;
        transform-style: preserve-3d;

        & .card-front {
          background-color: var(--card-bg);
          border-radius: 20px;
          padding: 0.7rem;
          margin-bottom: 1rem;
          position: absolute;
          width: 100%;
          height: 100%;
          backface-visibility: hidden;
          transform: rotateY(0deg);
          transition: transform 1.2s cubic-bezier(0.75, 0, 0.85, 1);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.8);

          & .card-logo {
            display: grid;
            grid-template-columns: 85% auto;
            grid-template-rows: auto;
            padding: 1rem 1rem 2rem 1rem;
            position: relative;
            min-height: 75px;
            margin-left: -0.2rem;

            &-img1,
            &-img2 {
              grid-column: 1/2;
              grid-row: 1/2;
              align-self: center;
              min-height: 35px;
            }

            & .ratingAccount {
              grid-column: 2/3;
              grid-row: 1/2;
              justify-self: end;
              align-self: center;
              padding: 0.2rem 1rem;
              width: 48px;
              height: 18px;
              border-radius: 10px;
              padding: 1rem;
              display: flex;
              justify-content: center;
              align-items: center;
              // top: 2.3rem;
              // right: 1rem;
              font-weight: 500;
              letter-spacing: 1px;

              &.exchange {
                top: 1.6rem;
              }
            }

            &-pac {
              display: flex;
              justify-content: center;
              width: 100%;
            }
          }
          & .home-balance {
            display: grid;
            // grid-row-gap: 0.5rem;
            grid-template-rows: auto auto;
            grid-template-columns: 60% 40%;
            padding: 1rem 1rem 2rem 1rem;

            &-title {
              grid-row: 1/2;
              grid-column: 1/2;
              align-self: center;
              font-size: 1.8rem;
              font-weight: 500;
              color: #9b9b9b;
              margin-bottom: 0.2rem;
            }

            &-profit-title {
              grid-row: 1/2;
              grid-column: 2/3;
              align-self: center;
              justify-self: end;
              display: flex;
              align-items: center;
              font-size: 1.8rem;
              font-weight: 500;
              color: #9b9b9b;
              margin-bottom: 0.2rem;
            }

            &-current {
              grid-row: 2/3;
              grid-column: 1/2;
              align-self: start;
              font-size: 2.8rem;
              font-weight: 600;
              display: flex;
              align-items: center;

              &-gain {
                justify-content: end;
                align-items: center;
                display: flex;
                flex-direction: row;
                border-radius: 15px;
                padding: 0.15rem 0.8rem;
                margin-left: 1rem;
                height: 22px;

                &-icon {
                  display: flex;
                  margin-right: 0.6rem;

                  & i {
                    padding-top: 0.25rem;
                    font-size: 1.2rem;
                  }
                }

                &-number {
                  font-size: 1.4rem;
                  font-weight: 500;
                }
              }
            }

            &-profit {
              grid-row: 2/3;
              grid-column: 2/3;
              align-self: center;
              justify-self: end;
              font-size: 2.8rem;
            }

            &-profit {
              font-weight: 500;
            }
          }

          & .home-deposits {
            display: grid;
            grid-row-gap: 0.3rem;
            grid-template-rows: 50% 50%;
            grid-template-columns: 70% 30%;
            padding: 1rem;
            // background-color: rgb(10, 10, 10);
            border-radius: 15px;
            margin-bottom: 1.5rem;

            &-title {
              grid-row: 1/2;
              grid-column: 1/-1;
              align-self: center;
              font-size: 1.4rem;
              font-weight: 500;
              color: #9b9b9b;
            }
            &-current {
              grid-row: 2/3;
              grid-column: 1/2;
              align-self: center;
              font-size: 1.6rem;
              font-weight: 500;
              // color: #505fd4;
            }

            &-fees-title {
              grid-row: 1/2;
              grid-column: 2/-1;
              align-self: center;
              justify-self: end;
              font-size: 1.4rem;
              font-weight: 500;
              color: #9b9b9b;
            }

            &-fees-number {
              grid-row: 2/3;
              grid-column: 2/3;
              align-self: center;
              justify-self: end;
              font-size: 1.6rem;
              font-weight: 500;
              color: var(--dark-orange);
            }
          }

          & .home-extra-card {
            display: grid;
            grid-template-columns: 50% 50%;
            padding: 1rem;
            color: #9b9b9b;
            border-top: 1px solid #303030;
            margin-top: 1rem;

            span {
              font-weight: 500;
              font-size: 1.2rem;
            }

            & :last-child {
              justify-self: end;
            }
          }
        }

        & .card-back {
          position: absolute;
          width: 100%;
          height: 100%;
          backface-visibility: hidden;
          transform: rotateY(180deg);
          background-color: #303130;
          border-radius: 20px;
          padding: 0.7rem;
          margin-bottom: 1rem;
          transition: transform 0.8s cubic-bezier(0.75, 0, 0.85, 1);

          & .card-logo {
            display: grid;
            grid-template-columns: 85% auto;
            grid-template-rows: auto;
            padding: 1rem 1rem 2rem 1rem;
            position: relative;
            min-height: 75px;
            margin-left: -0.2rem;

            &-img1,
            &-img2 {
              grid-column: 1/2;
              grid-row: 1/2;
              align-self: center;
              min-height: 35px;
            }

            & .ratingAccount {
              grid-column: 2/3;
              grid-row: 1/2;
              justify-self: end;
              align-self: center;
              padding: 0.2rem 1rem;
              width: 48px;
              height: 18px;
              border-radius: 10px;
              padding: 1rem;
              display: flex;
              justify-content: center;
              align-items: center;
              // top: 2.3rem;
              // right: 1rem;
              font-weight: 500;
              letter-spacing: 1px;

              &.exchange {
                top: 1.6rem;
              }
            }

            &-pac {
              display: flex;
              justify-content: center;
              width: 100%;
            }
          }

          & .home-balance {
            display: grid;
            // grid-row-gap: 0.5rem;
            grid-template-rows: auto auto;
            grid-template-columns: 50% 50%;
            padding: 0.5rem 1rem 2rem 1rem;

            &-title {
              grid-row: 1/2;
              grid-column: 1/2;
              align-self: center;
              font-size: 1.8rem;
              font-weight: 500;
              color: #9b9b9b;
              margin-bottom: 0.2rem;
            }

            &-profit-title {
              grid-row: 1/2;
              grid-column: 2/3;
              align-self: center;
              justify-self: end;
              display: flex;
              align-items: center;
              font-size: 1.8rem;
              font-weight: 500;
              color: #9b9b9b;
              margin-bottom: 0.2rem;
            }

            &-profit-total-title {
              grid-row: 3/4;
              grid-column: 1/3;
              align-self: center;
              justify-self: center;
              display: flex;
              align-items: center;
              font-size: 1.8rem;
              font-weight: 500;
              color: #9b9b9b;
              margin-top: 14px;
              margin-bottom: 0.2rem;
            }

            &-current {
              grid-row: 2/3;
              grid-column: 1/2;
              align-self: start;
              font-size: 3rem;
              font-weight: 600;
              display: flex;
              align-items: center;

              &-gain {
                justify-content: end;
                align-items: center;
                display: flex;
                flex-direction: row;
                border-radius: 15px;
                padding: 0.15rem 0.8rem;
                margin-left: 1rem;
                height: 22px;

                &-icon {
                  display: flex;
                  margin-right: 0.6rem;

                  & i {
                    padding-top: 0.25rem;
                    font-size: 1.2rem;
                  }
                }

                &-number {
                  font-size: 1.4rem;
                  font-weight: 500;
                }
              }
            }

            &-profit {
              grid-row: 2/3;
              grid-column: 2/3;
              align-self: center;
              justify-self: end;
              font-size: 2.8rem;
              font-weight: 500;
            }

            &-profit-total {
              grid-row: 5/6;
              grid-column: 1/3;
              align-self: center;
              justify-self: center;
              font-size: 3rem;
              font-weight: 500;
            }
          }

          & .home-extra-back {
            display: grid;
            grid-row-gap: 0.3rem;
            grid-template-rows: 50% 50%;
            grid-template-columns: calc(100% / 3) calc(100% / 3) calc(100% / 3);
            padding: 1rem;
            // background-color: rgb(10, 10, 10);
            border-radius: 15px;

            &-earn {
              grid-row: 1/2;
              grid-column: 1/2;
              align-self: center;
              font-size: 1.4rem;
              font-weight: 500;
              color: #9b9b9b;

              &-number {
                grid-row: 2/3;
                grid-column: 1/2;
                align-self: center;
                font-size: 1.6rem;
                font-weight: 500;
                // color: #505fd4;
                color: #04dc00;
              }
            }
            &-gift {
              grid-row: 1/2;
              grid-column: 2/3;
              align-self: center;
              justify-self: center;
              font-size: 1.4rem;
              font-weight: 500;
              color: #9b9b9b;

              &-number {
                grid-row: 2/3;
                grid-column: 2/3;
                align-self: center;
                justify-self: center;
                font-size: 1.6rem;
                font-weight: 500;
                // color: #505fd4;
                color: #04dc00;
              }
            }

            &-fees {
              &-title {
                grid-row: 1/2;
                grid-column: 3/4;
                align-self: center;
                justify-self: end;
                font-size: 1.4rem;
                font-weight: 500;
                color: #9b9b9b;
              }
              &-number {
                grid-row: 2/3;
                grid-column: 3/4;
                align-self: center;
                justify-self: end;
                font-size: 1.6rem;
                font-weight: 500;
                color: var(--dark-orange);
              }
            }
          }
        }
      }
    }

    & .dots {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 3rem;

      & .dot {
        font-size: 1.2rem;
        color: #282828;
        margin-right: 1rem;
      }
    }

    .home-extra {
      display: grid;
      grid-row-gap: 0.3rem;
      grid-template-columns: calc(100% / 3) calc(100% / 3) calc(100% / 3);
      padding: 1.7rem;
      background-color: var(--card-bg);
      border-radius: 15px;
      margin-top: 1rem;
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.8);

      &-earn {
        grid-row: 1/2;
        grid-column: 1/2;
        align-self: center;
        font-size: 1.2rem;
        font-weight: 500;
        color: #9b9b9b;

        &-number {
          grid-row: 2/3;
          grid-column: 1/2;
          align-self: center;
          font-size: 1.6rem;
          font-weight: 500;
          // color: #505fd4;
          color: #04dc00;
        }
      }
      &-gift {
        grid-row: 1/2;
        grid-column: 2/3;
        align-self: center;
        justify-self: center;
        font-size: 1.2rem;
        font-weight: 500;
        color: #9b9b9b;

        &-number {
          grid-row: 2/3;
          grid-column: 2/3;
          align-self: center;
          justify-self: center;
          font-size: 1.6rem;
          font-weight: 500;
          // color: #505fd4;
          color: #04dc00;
        }
      }

      &-fees {
        &-title {
          grid-row: 1/2;
          grid-column: 3/4;
          align-self: center;
          justify-self: end;
          font-size: 1.2rem;
          font-weight: 500;
          color: #9b9b9b;
        }
        &-number {
          grid-row: 2/3;
          grid-column: 3/4;
          align-self: center;
          justify-self: end;
          font-size: 1.6rem;
          font-weight: 500;
          color: var(--dark-orange);
        }
      }
    }

    .home-extra-profits {
      display: grid;
      grid-row-gap: 0.3rem;
      grid-template-columns: calc(100% / 3) calc(100% / 3) calc(100% / 3);
      padding: 1.7rem;
      // background-color: #0d1a29;
      background-color: var(--card-bg);
      border-radius: 15px;
      margin-top: 1rem;
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.8);

      &-ytd {
        &-title {
          grid-row: 1/2;
          grid-column: 1/2;
          align-self: center;
          font-size: 1.2rem;
          font-weight: 500;
          color: #9b9b9b;
        }

        &-number {
          grid-row: 2/3;
          grid-column: 1/2;
          font-size: 1.6rem;
          font-weight: 500;
          // color: #505fd4;
          // color: #04dc00;
        }
      }

      &-3m {
        &-title {
          grid-row: 1/2;
          grid-column: 2/3;
          align-self: center;
          justify-self: center;
          font-size: 1.2rem;
          font-weight: 500;
          color: #9b9b9b;
        }

        &-number {
          grid-row: 2/3;
          grid-column: 2/3;
          font-size: 1.6rem;
          font-weight: 500;
          justify-self: center;
          // color: #505fd4;
          // color: #04dc00;
        }
      }

      &-1m {
        &-title {
          grid-row: 1/2;
          grid-column: 3/4;
          align-self: center;
          justify-self: end;
          font-size: 1.2rem;
          font-weight: 500;
          color: #9b9b9b;
        }

        &-number {
          grid-row: 2/3;
          grid-column: 3/4;
          font-size: 1.6rem;
          font-weight: 500;
          justify-self: end;
          // color: #505fd4;
          // color: #04dc00;
        }
      }
    }
  }
}

@media (min-width: 900px) {
  .home-desktop {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%; // margin: 0% 2%;

    & .home {
      width: 100%;
      padding-left: 0;
      padding-right: 0;
    }

    .home-desktop-1 {
      flex-direction: column;
      width: 100%;

      & .card {
        width: 100%;
      }
    }

    & .crypto-list {
      margin-top: 1rem;
      width: 100%;
      margin-left: 2rem;
    }
  }
}

@media (min-width: 1200px) {
  .home-desktop {
    flex-direction: row;

    & .home {
      width: 42%;
    }

    & .crypto-list {
      margin-top: 1rem;
      width: calc(58% - 2rem);
      margin-left: 2rem;
    }
  }
}
