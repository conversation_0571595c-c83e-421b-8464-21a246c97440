// Binance

import { Injectable } from '@angular/core';
import { IPortfolioMonthly, IPortfolioYearly } from '../../interfaces/coins';
import { InflationService } from '../utils/inflation.service';

@Injectable({
  providedIn: 'root',
})
export class PortfolioBinanceAndreaHistory {
  constructor(private inflationService: InflationService) {
    this.portfolioMonthly.forEach((month) => {
      const formatDate = this.formatDate(month.date);
      month.formatDate = formatDate;
    });
  }

  public portfolioMonthly: IPortfolioMonthly[] = [
    {
      title: 'December 2023',
      date: '12/01/2023',
      formatDate: '',
      deposits: 0,
      current: 0,
      profit: 0,
      profitPerc: 0,
    },
    {
      title: 'January 2024',
      date: '01/01/2024',
      formatDate: '',
      deposits: 1750,
      current: 1793,
      profit: 43,
      profitPerc: 2,
    },
    {
      title: 'February 2024',
      date: '02/01/2024',
      formatDate: '',
      deposits: 3250,
      current: 3093,
      profit: -157,
      profitPerc: -5,
    },
    {
      title: 'March 2024',
      date: '03/01/2024',
      formatDate: '',
      deposits: 3350,
      current: 4793,
      profit: 1443,
      profitPerc: 43,
    },
    {
      title: 'April 2024',
      date: '04/01/2024',
      formatDate: '',
      deposits: 3500,
      current: 6036,
      profit: 2536,
      profitPerc: 72,
    },
    {
      title: 'May 2024',
      date: '05/01/2024',
      formatDate: '',
      deposits: 3500,
      current: 4521,
      profit: 1021,
      profitPerc: 29,
    },
    {
      title: 'June 2024',
      date: '06/01/2024',
      formatDate: '',
      deposits: 3500,
      current: 5429,
      profit: 1929,
      profitPerc: 55,
    },
    {
      title: 'July 2024',
      date: '07/01/2024',
      formatDate: '',
      deposits: 4090,
      current: 5262,
      profit: 1172,
      profitPerc: 29,
    },
    {
      title: 'August 2024',
      date: '08/01/2024',
      formatDate: '',
      deposits: 4240,
      current: 4963,
      profit: 723,
      profitPerc: 17,
    },
    {
      title: 'September 2024',
      date: '09/01/2024',
      formatDate: '',
      deposits: 4440,
      current: 4347,
      profit: -93,
      profitPerc: -2,
    },
    {
      title: 'October 2024',
      date: '10/01/2024',
      formatDate: '',
      deposits: 4440,
      current: 5134,
      profit: 694,
      profitPerc: 16,
    },
    {
      title: 'November 2024',
      date: '11/01/2024',
      formatDate: '',
      deposits: 4440,
      current: 4832,
      profit: 392,
      profitPerc: 9,
    },
    {
      title: 'December 2024',
      date: '12/01/2024',
      formatDate: '',
      deposits: 4467,
      current: 7772,
      profit: 3305,
      profitPerc: 74,
    },
    {
      title: 'January 2025',
      date: '01/01/2025',
      formatDate: '',
      deposits: 4567,
      current: 6974,
      profit: 2407,
      profitPerc: 53,
    },
    {
      title: 'February 2025',
      date: '02/01/2025',
      formatDate: '',
      deposits: 4667,
      current: 6653,
      profit: 1986,
      profitPerc: 42,
    },
    {
      title: 'March 2025',
      date: '03/01/2025',
      formatDate: '',
      deposits: 4667,
      current: 4865,
      profit: 198,
      profitPerc: 4,
    },
    {
      title: 'April 2025',
      date: '04/01/2025',
      formatDate: '',
      deposits: 4767,
      current: 4411,
      profit: -357,
      profitPerc: -7,
    },
    {
      title: 'May 2025',
      date: '05/01/2025',
      formatDate: '',
      deposits: 4967,
      current: 4773,
      profit: -194,
      profitPerc: -3,
    },
    {
      title: 'June 2025',
      date: '06/01/2025',
      formatDate: '',
      deposits: 5068,
      current: 5313,
      profit: 245,
      profitPerc: 5,
    },
    {
      title: 'July 2025',
      date: '07/01/2025',
      formatDate: '',
      deposits: 5068,
      current: 4918,
      profit: -150,
      profitPerc: -3,
    },
    {
      title: 'August 2025',
      date: '08/01/2025',
      formatDate: '',
      deposits: 5068,
      current: 6106,
      profit: 1038,
      profitPerc: 20,
    },
    {
      title: 'September 2025',
      date: '09/01/2025',
      formatDate: '',
      deposits: 5068,
      current: 6881,
      profit: 1813,
      profitPerc: 36,
    },
  ];

  public portfolioYearly: IPortfolioYearly[] = [
    {
      year: 2023,
      start: 0,
      deposits: 1750,
      startAndDeposits: 1750,
      fees: -29,
      withdraw: 0,
      closedTrades: 0,
      openTrades: 43,
      end: 1793,
      profit: 43,
      taxes: 0,
      netProfit: 43,
      netProfitPerc: 2,
    },
    {
      year: 2024,
      start: 1793,
      deposits: 2699,
      startAndDeposits: 4492,
      fees: -36.62,
      withdraw: 0,
      closedTrades: 1800,
      openTrades: 682,
      end: 6974,
      profit: 2482,
      taxes: 0,
      netProfit: 2445.38,
      netProfitPerc: 54,
    },
  ];

  public getTotalInflation() {
    return this.inflationService.calculateEffectiveInflation(
      this.portfolioMonthly,
    );
  }

  formatDate(date: string) {
    let year: string | number = new Date(date)
      .getUTCFullYear()
      .toString()
      .substring(-2);
    let month = '' + (new Date(date).getMonth() + 1);
    let day = '' + new Date(date).getDate();

    year = date.startsWith('01/01') ? (+year + 1).toString() : year;

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return `${month}/${year}`;
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('date');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
