import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { AddDepositModule } from 'src/app/shared/add-deposit/add-deposit.module';
import { AccountDepositsComponent } from './account-deposits.component';

@NgModule({
  declarations: [AccountDepositsComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DatePipe,
    PipesModule,
    AddDepositModule,
  ],
  exports: [AccountDepositsComponent],
})
export class AccountDepositsModule {}
