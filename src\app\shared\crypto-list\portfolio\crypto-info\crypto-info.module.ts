import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { CryptoInfoComponent } from './crypto-info.component';

@NgModule({
  declarations: [CryptoInfoComponent],
  imports: [CommonModule, PipesModule, FormsModule],
  exports: [CryptoInfoComponent],
})
export class CryptoInfoModule {}
