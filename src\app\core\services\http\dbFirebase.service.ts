import { Injectable, signal } from '@angular/core';
import firebase from 'firebase/compat/app';
import 'firebase/compat/firestore';

import { AngularFirestore } from '@angular/fire/compat/firestore';

@Injectable({
  providedIn: 'root',
})
export class FirebaseService {
  constructor(private firestore: AngularFirestore) {}

  public etfStats = signal(null);
  public etfHistory = signal(null);
  public etfStatsNewDate = signal(false);

  getData(collection: string) {
    return this.firestore.collection(collection).valueChanges();
  }

  // addCollectionBackup(collection: string) {
  //   return this.firestore.collection(collection).add({});
  // }

  getEtfStats() {
    return this.firestore.collection('bitcoinEtf').valueChanges();
  }

  addPortfoliosBackup(data: any, collection: string, doc: string) {
    return this.firestore.collection(collection).doc(doc).set(data);
  }

  addAccountDeposits(data: any, doc: any) {
    return this.firestore
      .collection('portfolios-deposits')
      .doc(doc)
      .update({ deposits: firebase.firestore.FieldValue.arrayUnion(data) });
  }

  getAccountDeposits(doc: string) {
    return this.firestore
      .collection('portfolios-deposits')
      .doc(doc)
      .valueChanges();
  }

  editCoins(data: any, portfolio: string, collection: string) {
    return this.firestore
      .collection(collection)
      .doc(portfolio)
      .update({ coins: data });
  }

  addStats(data: any, portfolio: string, collection: string) {
    return this.firestore
      .collection(collection)
      .doc(portfolio)
      .update({ stats: data });
  }

  addHistory(data: any, portfolio: string, collection: string) {
    return this.firestore
      .collection(collection)
      .doc(portfolio)
      .update({ history: data });
  }
}
