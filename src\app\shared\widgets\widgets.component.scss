.widgets {
  position: relative;
  width: 100%;
  z-index: 0;
  // border-bottom: 1px solid gray;
  margin-bottom: 1rem;
  padding-bottom: 1rem;

  .newsWidget {
    background-color: #0000003a;
    padding: 1.5rem 0;
    margin: 0 1rem;
    position: relative;
    border: 1px solid gray;
    padding: 1rem;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    &-mobile {
      // border-radius: 15px;
      // border: 1px solid gray;
      margin: 3rem 1rem;
      // margin-top: 0;
      // padding: 1.2rem;
      overflow: hidden;
      padding-bottom: 0;
      border: 1px solid #2d2d2d;
      background-color: #0000003a;
      border-radius: 10px;

      .item {
        padding: 1rem;
        width: 100%;
        border-bottom: 1px solid #2d2d2d;

        & .title {
          font-size: 1.4rem;
          margin-bottom: 0.5rem;
          font-weight: 500;
          // min-height: 29.33px;
          overflow: hidden;
          // white-space: nowrap;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-height: 1.3;
          padding: 0.5rem;
          // margin-left: 1rem;
        }

        & .date {
          font-size: 1.2rem;
          color: gray;
          text-align: end;
          // margin-left: 1rem;
        }
      }

      .dots {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 1rem;

        .dot {
          font-size: 1.2rem;
          color: #282828;
          margin-right: 1rem;
        }
      }

      & .list {
        display: grid;
        grid-template-columns: 25% 75%;
        grid-template-rows: 75% 25%;
        row-gap: 0.5rem;
        width: 100%;
        // border-bottom: 1px solid #494949;

        & a {
          text-decoration: none;
          cursor: grab;
          color: inherit;
        }

        &:last-child {
          border-bottom: none;
        }

        & .image {
          grid-column: 1/2;
          grid-row: 1/-1;
          align-self: center;
          justify-self: center;
          border-radius: 10px;
          height: 80px;
          position: relative;

          & img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
          }
        }

        & .tags {
          justify-self: start;
          display: flex;
          grid-column: 1/3;
          grid-row: 1/2;
          font-size: 1.4rem;
          margin-bottom: 0.7rem;

          & .tag {
            display: flex;
            justify-content: center;
            align-items: center;
            color: gray;
            background-color: rgb(30, 30, 30);
            border-radius: 5px;
            padding: 1rem;
            margin-right: 0.5rem;
            font-size: 1.2rem;
            cursor: pointer;
          }
        }

        & .title {
          justify-self: start;
          align-self: start;
          grid-column: 1/-1;
          grid-row: 1/2;
          font-size: 1.2rem;
          font-weight: 500;
          // margin-left: 1rem;
        }

        & .date {
          justify-content: start;
          align-self: flex-end;
          grid-column: 1/-1;
          grid-row: 2/3;
          font-size: 1.2rem;
          color: gray;
          // margin-left: 1rem;
        }
      }
    }

    &-desktop {
      display: none;
    }
  }
}

@media (min-width: 900px) {
  .widgets {
    & .newsWidget-mobile {
      display: none;
    }
  }
}

@media (min-width: 1200px) {
  .widgets {
    // margin: 0 12%;
    display: flex;
    justify-content: space-between;
    border-bottom: none;
    margin-bottom: 0;
    width: 100%;
    margin-top: 1rem;

    & .newsWidget-desktop {
      width: 50%;
      max-height: 105px;
      margin: 0;
      display: flex;
      flex-direction: column;
      background-color: var(--card-bg);
      border-radius: 15px;
      padding: 1rem;

      & a {
        text-decoration: none;
        color: inherit;
      }

      & .list {
        display: grid;
        grid-template-columns: 82% 18%;
        grid-template-rows: 50% 50%;
        width: 100%;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid rgb(99, 99, 99);

        &:first-child {
          padding-top: 0.5rem;
          padding-bottom: 0.5rem;
        }

        &:last-child {
          margin-top: 0.5rem;
          padding-bottom: 0;
          border-bottom: none;
        }

        .date {
          grid-row: 2/3;
        }

        // & .image {
        //   grid-column: 1/2;
        //   grid-row: 1/-1;
        //   align-self: center;
        //   justify-self: center;
        //   border-radius: 10px;
        //   height: 100%;
        //   max-height: 90px;
        //   min-width: 89px;
        //   position: relative;

        //   & img {
        //     width: 100%;
        //     height: 100%;
        //     object-fit: cover;
        //     border-radius: 10px;
        //   }
        // }

        // & .tags {
        //   justify-self: start;
        //   display: flex;
        //   grid-column: 2/3;
        //   grid-row: 1/2;
        //   font-size: 1.2rem;
        //   margin-left: 1rem;

        //   & .tag {
        //     display: flex;
        //     justify-content: center;
        //     align-items: center;
        //     color: gray;
        //     background-color: rgb(30, 30, 30);
        //     border-radius: 5px;
        //     padding: 0.7rem;
        //     margin-right: 0.5rem;
        //     cursor: pointer;
        //   }
        // }

        & .title {
          grid-column: 1/2;
          grid-row: 1/-1;
          align-self: center;
          justify-self: start;
          width: 100%;
          font-size: 1.4rem;
          font-weight: 400;
          padding: 0 1rem;
          // text-align: justify;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2; /* number of lines to show */
          line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        & .date {
          grid-column: 2/3;
          align-self: end;
          font-size: 1.2rem;
          color: gray;
          margin-left: 0.2rem;
        }
      }
    }
  }
}
