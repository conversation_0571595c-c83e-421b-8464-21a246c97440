import { Injectable } from '@angular/core';
import { Coin, ICoin, IPortfolioStats, ITrade } from '../../interfaces/coins';

@Injectable({
  providedIn: 'root',
})
export class PortfolioCryptoFra extends Coin {
  constructor() {
    super();
  }

  // CRYPTO.COM
  public coins: ICoin[] = [];

  public portfolioStats: IPortfolioStats = {
    gifts: 0,
    totalFees: -102.8,
  };

  public closedTrades: ITrade[] = [];
}
