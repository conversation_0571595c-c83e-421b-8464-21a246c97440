import { Component, computed, effect, signal, untracked } from '@angular/core';
import { ITrade } from 'src/app/core/interfaces/coins';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-closed-trades',
  templateUrl: './closed-trades.component.html',
  styleUrl: './closed-trades.component.scss',
})
export class ClosedTradesComponent {
  protected tradeList = this.currentAccountService.currentPortfolioTrades;
  protected tradeListEffect = effect(() => {
    this.tradeList();
    untracked(() => {
      this.tradeListFilter.set([
        ...this.tradeList()
          .sort((a, b) => b.tradeNumber - a.tradeNumber)
          .filter((trade) => {
            return new Date(trade.date).getFullYear() == 2025;
          }),
      ]);
      this.onSelectYear({ target: { value: '2025' } });
    });
  });
  protected currentFilter = signal<string>('2025');
  protected tradeListFilter = signal<ITrade[]>([]);
  protected tradeListFilterSlice = computed<ITrade[]>(() =>
    this.tradeListFilter().slice(0, this.tradesToAdd()),
  );

  protected yearList = computed(() => [
    ...new Set(
      this.tradeList().map((trade) => new Date(trade.date).getFullYear()),
    ),
  ]);
  protected totalProfit = computed(() =>
    this.tradeListFilter().reduce((acc, curr) => acc + curr.profit, 0),
  );
  protected tradesToAddStep = 20;
  protected tradesToAdd = signal<number>(this.tradesToAddStep);

  // protected eff = effect(() => {
  //   console.log('tradelist', this.tradeList());
  // });

  constructor(private currentAccountService: CurrentAccountService) {
    // this.tradeListFilter.set([
    //   ...this.tradeList().filter((trade) => {
    //     return new Date(trade.date).getFullYear() == 2025;
    //   }),
    // ]);
  }

  onSelectYear(data: any) {
    const year = data.target.value;

    this.tradesToAdd.set(this.tradesToAddStep);

    if (year == 'all') {
      this.tradeListFilter.set([...this.tradeList()]);
    } else {
      this.tradeListFilter.set([
        ...this.tradeList().filter((trade) => {
          return new Date(trade.date).getFullYear() == year;
        }),
      ]);
    }
  }

  onScroll() {
    // console.log('SCROLLED');
    this.tradesToAdd.update((prev) => prev + this.tradesToAddStep);
  }
}
