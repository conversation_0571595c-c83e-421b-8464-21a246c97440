import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { NgApexchartsModule } from 'ng-apexcharts';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from '../../shared/loader-spinner/loader-spinner.module';
import { BlockchainFeesComponent } from './blockchain-fees/blockchain-fees.component';
import { BlockchainLinechartComponent } from './blockchain-linechart/blockchain-linechart.component';
import { BlockchainProtocolsComponent } from './blockchain-protocols/blockchain-protocols.component';
import { BlockchainStatsRoutingModule } from './blockchain-stats-routing.module';
import { BlockchainStatsComponent } from './blockchain-stats.component';
import { BlockchainTvlComponent } from './blockchain-tvl/blockchain-tvl.component';
import { BlockchainVolumeComponent } from './blockchain-volume/blockchain-volume.component';

@NgModule({
  declarations: [
    BlockchainStatsComponent,
    BlockchainTvlComponent,
    BlockchainFeesComponent,
    BlockchainVolumeComponent,
    BlockchainProtocolsComponent,
    BlockchainLinechartComponent,
  ],

  imports: [
    CommonModule,
    BlockchainStatsRoutingModule,
    PipesModule,
    LoaderSpinnerModule,
    NgApexchartsModule,
  ],
  exports: [
    BlockchainStatsComponent,
    BlockchainTvlComponent,
    BlockchainFeesComponent,
    BlockchainVolumeComponent,
    BlockchainProtocolsComponent,
    BlockchainLinechartComponent,
  ],
})
export class BlockchainStatsModule {}
