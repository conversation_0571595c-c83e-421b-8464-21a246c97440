import { HttpClient } from '@angular/common/http';
import { Component, signal } from '@angular/core';
import { Router } from '@angular/router';
import { GoogleSheetService } from 'src/app/core/services/http/google-sheet.service';

@Component({
  selector: 'app-crypto-volumes',
  templateUrl: './crypto-volumes.component.html',
  styleUrl: './crypto-volumes.component.scss',
})
export class CryptoVolumesComponent {
  protected cryptoList;
  protected currentFilter = '7d';
  protected loading = signal<boolean>(true);

  constructor(
    private http: HttpClient,
    private googleSheetService: GoogleSheetService,
    private router: Router,
  ) {
    // if (this.googleSheetService.googleAllData()?.length > 0) {
    //   this.cryptoList = this.googleSheetService.googleAllData;
    //   this.loading = false;
    // }
    this.googleSheetService.fetchAllData().subscribe({
      next: (data) => {
        if (this.googleSheetService.googleAllData()?.length > 0) {
          console.log('EFFECT', this.googleSheetService.googleAllData());
          this.cryptoList = this.googleSheetService.googleAllData;
          this.loading.set(false);
        }
      },
      error: (error) => {
        console.log(error);
        this.loading.set(false);
        this.router.navigateByUrl('/');
      },
    });
  }

  onSortClick(value) {
    this.currentFilter = value;
    // console.log(value);
    if (value == '24h')
      this.cryptoList.set(
        this.cryptoList().sort(
          (a, b) =>
            b[8].replace('$', '').replaceAll('.', '') -
            a[8].replace('$', '').replaceAll('.', ''),
        ),
      );
    if (value == '7d')
      this.cryptoList.set(
        this.cryptoList().sort(
          (a, b) =>
            b[9].replace('$', '').replaceAll('.', '') -
            a[9].replace('$', '').replaceAll('.', ''),
        ),
      );
    if (value == '30d')
      this.cryptoList.set(
        this.cryptoList().sort(
          (a, b) =>
            b[10].replace('$', '').replaceAll('.', '') -
            a[10].replace('$', '').replaceAll('.', ''),
        ),
      );

    // console.log('ok', this.cryptoList);
  }
}
