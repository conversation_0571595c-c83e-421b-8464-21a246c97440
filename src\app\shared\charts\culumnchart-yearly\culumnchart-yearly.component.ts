import { Component, ViewChild, effect } from '@angular/core';

import {
  ApexAxisChartSeries,
  ApexChart,
  ApexDataLabels,
  ApexFill,
  ApexGrid,
  ApexLegend,
  ApexPlotOptions,
  ApexTitleSubtitle,
  ApexTooltip,
  ApexXAxis,
  ApexYAxis,
  ChartComponent,
} from 'ng-apexcharts';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

type ChartOptions = {
  series: ApexAxisChartSeries;
  chart: ApexChart;
  xaxis: ApexXAxis;
  plotOptions: ApexPlotOptions;
  markers: any; //ApexMarkers;
  stroke: any; //ApexStroke;
  yaxis: ApexYAxis | ApexYAxis[];
  dataLabels: ApexDataLabels;
  title: ApexTitleSubtitle;
  legend: ApexLegend;
  fill: ApexFill;
  tooltip: ApexTooltip;
  grid: ApexGrid;
};

@Component({
  selector: 'app-culumnchart-yearly',
  templateUrl: './culumnchart-yearly.component.html',
  styleUrl: './culumnchart-yearly.component.scss',
})
export class CulumnchartYearlyComponent {
  @ViewChild('chart', { static: false }) chart: ChartComponent;
  public chartOptions: Partial<ChartOptions>;

  constructor(private currentAccountService: CurrentAccountService) {
    this.crateColumnYearlyChart();

    effect(() =>
      this.currentAccountService.currentPortfolioHistory().portfolioYearly
        ? this.crateColumnYearlyChart()
        : null,
    );
  }

  crateColumnYearlyChart() {
    this.chartOptions = {
      series: [
        {
          name: 'Capitale',
          data: this.currentAccountService
            .currentPortfolioHistory()
            .portfolioYearly?.map((data) => data.start),
          group: 'capital',
        },
        {
          name: 'Depositi',
          data: this.currentAccountService
            .currentPortfolioHistory()
            .portfolioYearly?.map((data) => data.deposits),
          group: 'capital',
        },
        {
          name: 'Utile',
          data: this.currentAccountService
            .currentPortfolioHistory()
            .portfolioYearly.map((data) => data.netProfit),
          group: 'profit',
        },

        // {
        //   name: 'Total Profit',
        //   type: 'line',
        //   data: this.currentAccountService
        //     .currentPortfolioHistory()
        //     .portfolioYearly.map((data) => data.netProfit),
        // },
      ],
      chart: {
        height: 350,
        offsetX: -10,
        type: 'bar',
        stacked: true,
        foreColor: '#fff',
        toolbar: {
          show: false,
        },
        zoom: { autoScaleYaxis: true },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          borderRadius: 0,
          columnWidth: '80%',
          dataLabels: {
            position: 'center',
          },
          // colors: {
          //   ranges: [
          //     {
          //       from: -Infinity,
          //       to: 0,
          //       color: '#F15B46',
          //     },
          //     {
          //       from: 0,
          //       to: Infinity,
          //       color: 'var(--green-profit)',
          //     },
          //   ],
          // },
        },
      },
      fill: {
        colors: [
          '#2196F3',
          '#1e38fc',
          function ({ value }) {
            return value > 0 ? 'var(--green-profit)' : '#ed451f';
          },
        ],
        opacity: 1,
      },
      dataLabels: {
        enabled: true,
        textAnchor: 'middle',
        // offsetY: -20,
        formatter: (value) => (+value).toFixed(0) + ' €',
      },
      grid: {
        show: true,
        position: 'back',
        borderColor: 'rgb(35, 35,35)',
        padding: {
          bottom: 10,
        },
        xaxis: {
          lines: {
            show: false,
          },
        },
        yaxis: {
          lines: {
            show: true,
          },
        },
      },
      stroke: {
        width: [1, 1, 4],
      },

      xaxis: {
        categories: this.currentAccountService
          .currentPortfolioHistory()
          .portfolioYearly.map((data) => data.year),
      },
      yaxis: [
        {
          labels: {
            formatter: (value) => {
              return value.toFixed(0) + ' €';
            },
          },
          // axisTicks: {
          //   show: true,
          // },
          // axisBorder: {
          //   show: true,
          //   color: '#fff',
          // },
          // labels: {
          //   style: {
          //     colors: '#008FFB',
          //   },
          // },
          tooltip: {
            enabled: true,
          },
        },
      ],
      tooltip: {
        theme: 'dark',
        fixed: {
          enabled: false,
          position: 'topLeft', // topRight, topLeft, bottomRight, bottomLeft
          offsetY: 30,
          offsetX: 60,
        },
      },

      legend: {
        horizontalAlign: 'center',
        offsetY: 5,
        labels: {
          colors: '#fff',
          // offsetX: 50,
        },
        markers: {
          strokeWidth: 0,
          offsetX: -2,
          fillColors: [
            '#2196F3',
            '#1e38fc',
            // 'var(--green-profit)'
            this.currentAccountService.currentPortfolioHistory()
              .portfolioYearly[
              this.currentAccountService.currentPortfolioHistory()
                .portfolioYearly.length - 1
            ].profit > 0
              ? 'var(--green-profit)'
              : '#ed451f',
          ],
        },
        onItemClick: {
          toggleDataSeries: false,
        },
      },
    };
  }
}
