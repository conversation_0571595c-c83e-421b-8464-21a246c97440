.menu-container {
  // min-height: 100vh;
  background-size: cover;
  background-position: center;
  position: relative;
  display: none;
  // height: 50px;
  // width: 59px;

  & .notification {
    position: absolute;
    top: -2px;
    right: -6px;
    background-color: green;

    border-radius: 50%;
    font-size: 1.4rem;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  & .menu-header {
    display: flex;
    justify-content: start;
    align-items: center;
    color: #f7931a;
    border-bottom: 1px solid gray;
    font-size: 1.8rem;

    img {
      width: 30px;
      margin: 12px;
    }
  }
}

.side-bar {
  background: #161616;
  width: 100%;
  height: 100dvh;
  overflow-y: auto;
  transition: 0.4s ease-in-out;
  transition-property: right;
  display: flex;
  flex-direction: column;
}
.side-bar::-webkit-scrollbar {
  width: 0px;
}

.side-bar.active {
  right: 0;
}
h1 {
  text-align: center;
  font-weight: 500;
  font-size: 25px;
  padding-bottom: 13px;
  font-family: sans-serif;
  letter-spacing: 2px;
}

.side-bar .menu {
  width: 100%;
  margin-top: 10px;
}

.side-bar .menu .item {
  position: relative;
  cursor: pointer;

  &.stock-tracker {
    background: #3c3c3c;
    margin: 0.5rem;
    border-radius: 12px;
    a {
      padding: 5px 15px;
      .icon {
        margin-right: 5px;
        margin-left: -2px;
        img {
          margin: 0;
          width: 25px;
        }
      }

      i {
        margin-left: 7px;
      }
    }
  }
}

.side-bar .menu .item a {
  color: #9a9a9a;
  font-size: 1.6rem;
  text-decoration: none;
  display: block;
  padding: 5px 25px;
  line-height: 40px;
  display: flex;
  align-items: center;

  & .notificationNews {
    margin-left: 1rem;
    background-color: green;
    border-radius: 50%;
    color: white;
    font-size: 1.4rem;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  & .icon {
    // color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 35px;
  }
}

.side-bar .menu .item:not(:first-child) a:hover {
  // background: #33363a;
  transition: 0.3s ease-in;
  color: white;
  // border-radius: 8px;
  // margin: 0 1rem;
  // padding: 5px 15px;
}

.side-bar .menu .item i {
  margin-right: 15px;
}

.side-bar .menu .item a .dropdown {
  position: absolute;
  right: 0;
  margin: 20px;
  transition: 0.3s ease;
}

.side-bar .menu .item .sub-menu {
  background: #262627;
  display: none;
}

.side-bar .menu .item .sub-menu a {
  padding-left: 80px;
}

.rotate {
  transform: rotate(90deg);
}

.close-btn {
  position: absolute;
  color: #8d8d8d;
  font-size: 23px;
  right: 0px;
  margin: 15px;
  cursor: pointer;
}

.menu-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 25px;
  cursor: pointer;
}

.main {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px;
}

.main h1 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 60px;
  text-align: center;
  line-height: 80px;
}

@media screen and (min-width: 900px) {
  .menu-container {
    display: block;

    .side-bar {
      margin: 1rem;
      width: calc(100% - 1rem);
      height: calc(100dvh - 2rem);
      border-radius: 14px;
    }

    .menu-header {
      padding-left: 1rem;
      font-weight: 600;
    }

    .menu {
      position: relative;
      height: 100%;

      .item {
        &.selected {
          background: #393939;
          margin: 0 1rem;
          border-radius: 8px;

          padding: 0;
          a {
            font-weight: 600;
            padding: 5px 15px;
            color: white;
          }
        }

        &.stock-tracker {
          background: unset;
          border-top: 1px solid gray;
          border-radius: 0;
          padding-top: 0.5rem;
          a {
            color: #6464bc;
          }

          img {
            border-radius: 50%;
          }
        }
        &.logout {
          position: absolute;
          bottom: 0;
          width: 100%;
        }
      }
    }
  }
}
