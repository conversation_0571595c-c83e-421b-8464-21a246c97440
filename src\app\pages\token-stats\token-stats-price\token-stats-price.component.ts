import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-token-stats-price',
  templateUrl: './token-stats-price.component.html',
  styleUrl: './token-stats-price.component.scss',
})
export class TokenStatsPriceComponent {
  @Input() cryptoList!: any;
  protected currentFilter;
  constructor() {}

  onSortClick(days) {
    this.currentFilter = days;
    if (days == '7d')
      this.cryptoList = this.cryptoList.sort(
        (a, b) => b.values.USD.percentChange7d - a.values.USD.percentChange7d
      );
    if (days == '30d')
      this.cryptoList = this.cryptoList.sort(
        (a, b) => b.values.USD.percentChange30d - a.values.USD.percentChange30d
      );
  }
}
