import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, forkJoin, tap } from 'rxjs';
import { PortfolioBinanceAndreaHistory } from '../data/binance-andrea-history.service';
import { PortfolioBinanceAndrea } from '../data/binance-andrea.service';
import { PortfolioBinanceElisaHistory } from '../data/binance-elisa-history.service';
import { PortfolioBinanceElisa } from '../data/binance-elisa.service';
import { PortfolioBinanceFraHistory } from '../data/binance-fra-history.service';
import { PortfolioBinanceFra } from '../data/binance-fra.service';
import { PortfolioCoinbaseFraHistory } from '../data/coinbase-fra-history.service';
import { PortfolioCoinbaseFra } from '../data/coinbase-fra.service';
import { PortfolioPACHistory } from '../data/pac-history.service';
import { PortfolioPAC } from '../data/pac.service';

@Injectable({
  providedIn: 'root',
})
export class CoingeckoService {
  protected coins = [];
  protected coinsPrice = [];
  protected tickersStr1: string;
  protected tickersStr3: string;
  protected tickersStr4: string;
  protected tickersStr5: string;
  protected tickersStr: string;
  public loading$ = new BehaviorSubject('');
  public eurusd = signal<number | null>(null);
  public btcPrice = signal<number | null>(null);
  public ethPrice = signal<number | null>(null);
  public btc24hChange = signal<number | null>(null);
  public eth24hChange = signal<number | null>(null);
  public btcMarketD = signal<number | null>(null);
  public ethMarketD = signal<number | null>(null);

  constructor(
    private portfolio: PortfolioCoinbaseFra,
    private portfolio2: PortfolioBinanceFra,
    private portfolio3: PortfolioBinanceAndrea,
    private portfolio4: PortfolioPAC,
    protected portfolio5: PortfolioBinanceElisa,
    private portfolioHistory: PortfolioCoinbaseFraHistory,
    private portfolio2History: PortfolioBinanceFraHistory,
    private portfolio3History: PortfolioBinanceAndreaHistory,
    private portfolio4History: PortfolioPACHistory,
    protected portfolio5History: PortfolioBinanceElisaHistory,
    private http: HttpClient,
  ) {
    this.tickersStr1 = this.portfolio.coins
      .map((coin) => coin.nameApi)
      .join('%2C%20');

    this.tickersStr3 = this.portfolio2.coins
      .map((coin) => coin.nameApi)
      .join('%2C%20');
    this.tickersStr4 = this.portfolio3.coins
      .map((coin) => coin.nameApi)
      .join('%2C%20');

    this.tickersStr5 = this.portfolio5.coins
      .map((coin) => coin.nameApi)
      .join('%2C%20');

    this.tickersStr =
      this.tickersStr1 +
      '%2C%20' +
      this.tickersStr3 +
      '%2C%20' +
      this.tickersStr4 +
      '%2C%20' +
      this.tickersStr5;
  }

  public getCryptoStats() {
    const eurusd = this.http.get('https://open.er-api.com/v6/latest/USD').pipe(
      tap((data: any) => {
        this.eurusd.set(data.rates.EUR);
      }),
    );

    const marketDominance = this.http
      .get('https://api.coingecko.com/api/v3/global')
      .pipe(
        tap((data: any) => {
          this.btcMarketD.set(data.data.market_cap_percentage.btc);
          this.ethMarketD.set(data.data.market_cap_percentage.eth);
          localStorage.setItem('btcMarketD', JSON.stringify(this.btcMarketD()));
          localStorage.setItem('ethMarketD', JSON.stringify(this.ethMarketD()));
        }),
      );

    const marketPrice = this.http
      .get(
        `https://api.coingecko.com/api/v3/coins/markets?vs_currency=eur&ids=${this.tickersStr}&per_page=100&page=1&locale=it&precision=full`,
      )
      .pipe(
        tap((data: any) => {
          this.portfolio.coins.forEach((coinData) => {
            data.forEach((coingecko) => {
              if (coinData.nameApi == coingecko.id) {
                coinData.price = coingecko.current_price;
                coinData.ath = coingecko.ath;
                coinData.athDate = coingecko.ath_date;
                coinData.athDist = coingecko.athDist;
                coinData.marketCap = coingecko.market_cap;
                coinData.rank = coingecko.market_cap_rank;
                coinData.change24h = coingecko.price_change_percentage_24h;
              }
            });
          });
          this.portfolio2.coins.forEach((coinData) => {
            data.forEach((coingecko) => {
              if (coinData.nameApi == coingecko.id) {
                coinData.price = coingecko.current_price;
                coinData.ath = coingecko.ath;
                coinData.athDate = coingecko.ath_date;
                coinData.athDist = coingecko.athDist;
                coinData.marketCap = coingecko.market_cap;
                coinData.rank = coingecko.market_cap_rank;
                coinData.change24h = coingecko.price_change_percentage_24h;
              }
            });
          });
          this.portfolio3.coins.forEach((coinData) => {
            data.forEach((coingecko) => {
              if (coinData.nameApi == coingecko.id) {
                coinData.price = coingecko.current_price;
                coinData.ath = coingecko.ath;
                coinData.athDate = coingecko.ath_date;
                coinData.athDist = coingecko.athDist;
                coinData.marketCap = coingecko.market_cap;
                coinData.rank = coingecko.market_cap_rank;
                coinData.change24h = coingecko.price_change_percentage_24h;
              }
            });
          });
          this.portfolio5.coins.forEach((coinData) => {
            data.forEach((coingecko) => {
              if (coinData.nameApi == coingecko.id) {
                coinData.price = coingecko.current_price;
                coinData.ath = coingecko.ath;
                coinData.athDate = coingecko.ath_date;
                coinData.athDist = coingecko.athDist;
                coinData.marketCap = coingecko.market_cap;
                coinData.rank = coingecko.market_cap_rank;
                coinData.change24h = coingecko.price_change_percentage_24h;
              }
            });
          });
          // this.portfolio4.coins.forEach((coinData) => {
          //   data.forEach((coingecko) => {
          //     if (coinData.nameApi == coingecko.id) {
          //       coinData.price = coingecko.current_price;
          //       coinData.ath = coingecko.ath;
          //       coinData.athDate = coingecko.ath_date;
          //       coinData.athDist = coingecko.athDist;
          //       coinData.marketCap = coingecko.market_cap;
          //       coinData.rank = coingecko.market_cap_rank;
          //       coinData.change24h = coingecko.price_change_percentage_24h;
          //     }
          //   });
          // });
        }),
        tap((data: any) => {
          this.portfolio2.calculateStats();
          this.portfolio3.calculateStats();
          this.portfolio.calculateStats();
          this.portfolio5.calculateStats();
          // this.portfolio4.calculateStats();
        }),
        tap((data: any) => {
          this.portfolioHistory.portfolioMonthly.push({
            title: 'Now',
            date: new Date().toString(),
            formatDate: this.formatDate(new Date().toString()),
            deposits: this.portfolio.portfolioStats.totalDeposits,
            current: this.portfolio.portfolioStats.current,
            profit: this.portfolio.portfolioStats.profits,
            profitPerc: this.portfolio.portfolioStats.profitsPerc,
          });
          this.portfolio2History.portfolioMonthly.push({
            title: 'Now',
            date: new Date().toString(),
            formatDate: this.formatDate(new Date().toString()),
            deposits: this.portfolio2.portfolioStats.totalDeposits,
            current: this.portfolio2.portfolioStats.current,
            profit: this.portfolio2.portfolioStats.profits,
            profitPerc: this.portfolio2.portfolioStats.profitsPerc,
          });
          this.portfolio3History.portfolioMonthly.push({
            title: 'Now',
            date: new Date().toString(),
            formatDate: this.formatDate(new Date().toString()),
            deposits: this.portfolio3.portfolioStats.totalDeposits,
            current: this.portfolio3.portfolioStats.current,
            profit: this.portfolio3.portfolioStats.profits,
            profitPerc: this.portfolio3.portfolioStats.profitsPerc,
          });
          this.portfolio5History.portfolioMonthly.push({
            title: 'Now',
            date: new Date().toString(),
            formatDate: this.formatDate(new Date().toString()),
            deposits: this.portfolio5.portfolioStats.totalDeposits,
            current: this.portfolio5.portfolioStats.current,
            profit: this.portfolio5.portfolioStats.profits,
            profitPerc: this.portfolio5.portfolioStats.profitsPerc,
          });
          // this.portfolio4History.portfolioMonthly.push({
          //   title: 'Now',
          //   date: new Date().toString(),
          //   formatDate: this.formatDate(new Date().toString()),
          //   deposits: this.portfolio4.portfolioStats.totalDeposits,
          //   current: this.portfolio4.portfolioStats.current,
          //   profit: this.portfolio4.portfolioStats.profits,
          //   profitPerc: this.portfolio4.portfolioStats.profitsPerc,
          // });
        }),
        tap((data: any) => {
          // Calculate PAC portfolio

          this.portfolio4.mergeAccounts();
          this.portfolio4History.portfolioMonthly.push({
            title: 'Now',
            date: new Date().toString(),
            formatDate: this.formatDate(new Date().toString()),
            deposits: this.portfolio4.portfolioStats.totalDeposits,
            current: this.portfolio4.portfolioStats.current,
            profit: this.portfolio4.portfolioStats.profits,
            profitPerc: this.portfolio4.portfolioStats.profitsPerc,
          });

          // console.log('COINS 4', this.portfolio4, 'COINS 5', this.portfolio5);
        }),
      );

    return forkJoin([eurusd, marketDominance, marketPrice]).pipe(
      tap((data: any) => {
        if (!!data) {
          // console.log('FORKJOIN OK');
          data = data[2];
          // BTC & ETH data
          this.btcPrice.set(
            data.find((item) => item.symbol == 'btc').current_price,
          );
          this.ethPrice.set(
            data.find((item) => item.symbol == 'eth').current_price,
          );
          this.btc24hChange.set(
            data.find((item) => item.symbol == 'btc')
              .price_change_percentage_24h,
          );
          this.eth24hChange.set(
            data.find((item) => item.symbol == 'eth')
              .price_change_percentage_24h,
          );

          localStorage.setItem('btcPrice', JSON.stringify(this.btcPrice()));

          localStorage.setItem('ethPrice', JSON.stringify(this.ethPrice()));

          localStorage.setItem(
            'btcPriceChange24h',
            JSON.stringify(this.btc24hChange()),
          );

          localStorage.setItem(
            'ethPriceChange24h',
            JSON.stringify(this.eth24hChange()),
          );

          // EURUSD rate
          localStorage.setItem('eurusd', JSON.stringify(this.eurusd()));

          // Portfolio data

          localStorage.setItem('coins', JSON.stringify(this.portfolio.coins));
          localStorage.setItem('coins2', JSON.stringify(this.portfolio2.coins));
          localStorage.setItem('coins3', JSON.stringify(this.portfolio3.coins));
          localStorage.setItem('coins4', JSON.stringify(this.portfolio4.coins));
          localStorage.setItem('coins5', JSON.stringify(this.portfolio5.coins));
          localStorage.setItem('date', new Date().toString());
          localStorage.setItem(
            'portfolioStats',
            JSON.stringify(this.portfolio.portfolioStats),
          );

          localStorage.setItem(
            'portfolio2Stats',
            JSON.stringify(this.portfolio2.portfolioStats),
          );
          localStorage.setItem(
            'portfolio3Stats',
            JSON.stringify(this.portfolio3.portfolioStats),
          );
          localStorage.setItem(
            'portfolio4Stats',
            JSON.stringify(this.portfolio4.portfolioStats),
          );
          localStorage.setItem(
            'portfolio5Stats',
            JSON.stringify(this.portfolio5.portfolioStats),
          );

          localStorage.setItem(
            'portfolioMonthly',
            JSON.stringify(this.portfolioHistory.portfolioMonthly),
          );

          localStorage.setItem(
            'portfolio2Monthly',
            JSON.stringify(this.portfolio2History.portfolioMonthly),
          );
          localStorage.setItem(
            'portfolio3Monthly',
            JSON.stringify(this.portfolio3History.portfolioMonthly),
          );
          localStorage.setItem(
            'portfolio4Monthly',
            JSON.stringify(this.portfolio4History.portfolioMonthly),
          );
          localStorage.setItem(
            'portfolio5Monthly',
            JSON.stringify(this.portfolio5History.portfolioMonthly),
          );

          console.log('COINGECKO DATI AGGIORNATI');

          // this.loading$.next('ok');
          // console.log('coins', this.portfolio.coins);
          // console.log('portfolioStats', this.portfolio.portfolioStats);
          // console.log(
          //   'portfolioHistory',
          //   this.portfolioHistory.portfolioMonthly
          // );
        }
      }),
    );
  }

  public getCryptoStatsSingleAccount(portfolio: string) {
    const eurusd = this.http.get('https://open.er-api.com/v6/latest/USD').pipe(
      tap((data: any) => {
        this.eurusd.set(data.rates.EUR);
      }),
    );

    const marketDominance = this.http
      .get('https://api.coingecko.com/api/v3/global')
      .pipe(
        tap((data: any) => {
          this.btcMarketD.set(data.data.market_cap_percentage.btc);
          this.ethMarketD.set(data.data.market_cap_percentage.eth);
          localStorage.setItem('btcMarketD', JSON.stringify(this.btcMarketD()));
          localStorage.setItem('ethMarketD', JSON.stringify(this.ethMarketD()));
        }),
      );

    const marketPrice = this.http
      .get(
        `https://api.coingecko.com/api/v3/coins/markets?vs_currency=eur&ids=${this.tickersStr5}&per_page=100&page=1&locale=it&precision=full`,
      )
      .pipe(
        tap((data: any) => {
          // console.log('DATA', data);
          this[portfolio].coins.forEach((coinData) => {
            data.forEach((coingecko) => {
              if (coinData.nameApi == coingecko.id) {
                coinData.price = coingecko.current_price;
                coinData.ath = coingecko.ath;
                coinData.athDate = coingecko.ath_date;
                coinData.athDist = coingecko.athDist;
                coinData.marketCap = coingecko.market_cap;
                coinData.rank = coingecko.market_cap_rank;
                coinData.change24h = coingecko.price_change_percentage_24h;
              }
            });
          });
        }),
        tap((data: any) => {
          this[portfolio].calculateStats();
        }),
        tap((data: any) => {
          this[portfolio + 'History'].portfolioMonthly.push({
            title: 'Now',
            date: new Date().toString(),
            formatDate: this.formatDate(new Date().toString()),
            deposits: this.portfolio5.portfolioStats.totalDeposits,
            current: this.portfolio5.portfolioStats.current,
            profit: this.portfolio5.portfolioStats.profits,
            profitPerc: this.portfolio5.portfolioStats.profitsPerc,
          });
        }),
      );

    return forkJoin([eurusd, marketDominance, marketPrice]).pipe(
      tap((data: any) => {
        if (!!data) {
          data = data[2];
          // console.log('DATA', data);
          // BTC & ETH data
          this.btcPrice.set(
            data.find((item) => item.symbol == 'btc').current_price,
          );
          this.ethPrice.set(
            data.find((item) => item.symbol == 'eth').current_price,
          );
          this.btc24hChange.set(
            data.find((item) => item.symbol == 'btc')
              .price_change_percentage_24h,
          );
          this.eth24hChange.set(
            data.find((item) => item.symbol == 'eth')
              .price_change_percentage_24h,
          );

          localStorage.setItem('btcPrice', JSON.stringify(this.btcPrice()));

          localStorage.setItem('ethPrice', JSON.stringify(this.ethPrice()));

          localStorage.setItem(
            'btcPriceChange24h',
            JSON.stringify(this.btc24hChange()),
          );

          localStorage.setItem(
            'ethPriceChange24h',
            JSON.stringify(this.eth24hChange()),
          );

          // EURUSD rate
          localStorage.setItem('eurusd', JSON.stringify(this.eurusd()));

          // Portfolio data

          localStorage.setItem('coins5', JSON.stringify(this.portfolio5.coins));
          localStorage.setItem('date', new Date().toString());

          localStorage.setItem(
            'portfolio5Stats',
            JSON.stringify(this.portfolio5.portfolioStats),
          );

          localStorage.setItem(
            'portfolio5Monthly',
            JSON.stringify(this.portfolio5History.portfolioMonthly),
          );

          console.log('COINGECKO DATI AGGIORNATI');

          // this.loading$.next('ok');
          // console.log('coins', this.portfolio.coins);
          console.log('portfolioStats', this.portfolio5.portfolioStats);
          console.log(
            'portfolioHistory',
            this.portfolio5History.portfolioMonthly,
          );
        }
      }),
    );
  }

  getTrendingCoins() {
    return this.http
      .get('https://api.coingecko.com/api/v3/search/trending')
      .pipe(
        tap((data: any) => {
          let newCoins = [];

          data.coins.forEach((coin) => {
            newCoins.push(coin.item);
          });

          console.log('TRENDING', data.coins);

          newCoins.forEach((coin) => {
            let data = coin.data;
            let content = coin.content;
            let changeH24 = {
              priceChangeH24: coin.data.price_change_percentage_24h.usd,
            };

            Object.assign(coin, data, content, changeH24);

            coin.market_cap = coin.market_cap
              .replace('$', '')
              .replaceAll(',', '');

            delete coin.data;
            delete coin.content;
            delete coin.price_change_percentage_24h;
          });

          newCoins = newCoins.sort((a, b) => b.market_cap - a.market_cap);
          console.log('TRENDING2', newCoins);
        }),
      );
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('date')
      ? localStorage.getItem('date')
      : null;

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 2) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  formatDate(date: string) {
    let year: string | number = new Date(date)
      .getUTCFullYear()
      .toString()
      .substring(-2);

    let month = (new Date(date).getMonth() + 2).toString();

    month.startsWith('12') ? (month = '01') : null;
    month.startsWith('12') ? (year = (+year + 1).toString()) : null;

    let day = '' + new Date(date).getDate();

    year = month.startsWith('01') ? (+year + 1).toString() : year;

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return `${month}/${year}`;
  }
}
