import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PipesModule } from '../../../../core/utils/pipes.module';
import { CryptoInfoModule } from '../crypto-info/crypto-info.module';
import { ProfitsComponent } from './profits.component';

@NgModule({
  declarations: [ProfitsComponent],
  imports: [CommonModule, PipesModule, FormsModule, CryptoInfoModule],
  exports: [ProfitsComponent],
})
export class ProfitsModule {}
