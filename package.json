{"dependencies": {"@angular/animations": "^18.2.2", "@angular/cdk": "~18.2.2", "@angular/common": "^18.2.2", "@angular/compiler": "^18.2.2", "@angular/core": "^18.2.2", "@angular/fire": "^18.0.1", "@angular/forms": "^18.2.2", "@angular/platform-browser": "^18.2.2", "@angular/platform-browser-dynamic": "^18.2.2", "@angular/router": "^18.2.2", "@angular/service-worker": "^18.2.2", "apexcharts": "^3.52.0", "firebase": "^10.7.2", "firebase-tools": "^13.16.0", "ng-apexcharts": "^1.11.0", "ngx-infinite-scroll": "^18.0.0", "rxjs": "~7.8.0", "swiper": "^11.1.12", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.2", "@angular/cli": "~18.2.2", "@angular/compiler-cli": "^18.2.2", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "^3.2.5", "typescript": "~5.5.4"}, "name": "cripto-tracker", "private": true, "scripts": {"build": "ng build --configuration production", "ng": "ng", "start": "ng serve", "test": "ng test", "watch": "ng build --watch --configuration development"}, "version": "1.17.30"}