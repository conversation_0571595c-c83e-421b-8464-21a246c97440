import { Component, effect } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { coinListCoingecko } from 'src/app/core/interfaces/coinListCoingecko';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { FirebaseService } from 'src/app/core/services/http/dbFirebase.service';

@Component({
  selector: 'app-add-deposit',
  templateUrl: './add-deposit.component.html',
  styleUrl: './add-deposit.component.scss',
})
export class AddDepositComponent {
  protected addDepositForm: FormGroup;
  protected currentAccount: string;
  protected currentAccountText: string;
  protected coinList = coinListCoingecko;
  protected yearsList = [];
  protected filteredList = [];
  protected filteredListItem = [];
  protected totalDeposits: number;
  protected crypto: any;
  protected avgPrice: any;
  protected depositsList = [];
  protected showForm = false;

  constructor(
    private firebaseService: FirebaseService,
    private currentAccountService: CurrentAccountService,
    private fb: FormBuilder,
  ) {
    this.addDepositForm = this.fb.group({
      account: [{ value: '', disabled: true }],
      date: [''],
      ticker: [''],
      crypto: [{ value: '', disabled: true }],
      totalDeposit: [''],
      fees: [''],
      quantity: [''],
      avgPrice: [{ value: '', disabled: true }],
    });

    this.addDepositForm.get('ticker').valueChanges.subscribe((data) => {
      if (!data) {
        this.crypto = '';
      } else {
        this.crypto = this.coinList.find(
          (coin) => coin.symbol === data.toLowerCase(),
        )
          ? this.coinList.find((coin) => coin.symbol === data.toLowerCase())
              .name
          : null;
      }
    });

    this.addDepositForm.get('quantity').valueChanges.subscribe((data) => {
      if (!data) {
        this.avgPrice = '';
      } else {
        if (
          this.addDepositForm.get('totalDeposit').value &&
          this.addDepositForm.get('quantity').value
        )
          this.avgPrice =
            (
              this.addDepositForm.get('totalDeposit').value /
              this.addDepositForm.get('quantity').value
            ).toFixed(2) + ' €';
      }
    });

    effect(() => {
      if (this.currentAccountService.currentAccount()) {
        this.currentAccount = this.currentAccountService.currentAccount();

        this.currentAccountText = this.currentAccountService
          .currentAccount()
          .toUpperCase();

        if (this.currentAccountService.currentAccount() === 'pac') {
          this.currentAccountText = 'PAC';
        }
      }
    });
  }

  onFormSubmit() {
    let deposit = this.addDepositForm.value;
    deposit.netDeposit = +deposit.totalDeposit - +deposit.fees;
    deposit.ticker = deposit.ticker.toLowerCase();
    deposit.date = deposit.date.replaceAll('-', '/');
    deposit.crypto = this.crypto;
    deposit.avgPrice = +this.avgPrice.replace(' €', '');

    this.firebaseService.addAccountDeposits(
      deposit,
      this.currentAccountService.currentAccount(),
    );

    this.showForm = false;
    this.addDepositForm.reset();
    this.addDepositForm
      .get('account')
      .setValue(this.currentAccountService.currentAccount().toUpperCase());
  }

  onShowFormClick() {
    this.showForm = true;
  }

  onCloseClick() {
    this.showForm = false;
    this.addDepositForm.reset();
    this.addDepositForm
      .get('account')
      .setValue(this.currentAccountService.currentAccount().toUpperCase());
  }
}
