import { Injectable } from '@angular/core';
import { concatMap, firstValueFrom, tap } from 'rxjs';
import { AuthService } from './auth/auth.service';
import { PortfolioBinanceAndreaHistory } from './data/binance-andrea-history.service';
import { PortfolioBinanceAndrea } from './data/binance-andrea.service';
import { PortfolioBinanceElisaHistory } from './data/binance-elisa-history.service';
import { PortfolioBinanceElisa } from './data/binance-elisa.service';
import { PortfolioBinanceFraHistory } from './data/binance-fra-history.service';
import { PortfolioBinanceFra } from './data/binance-fra.service';
import { PortfolioCoinbaseFraHistory } from './data/coinbase-fra-history.service';
import { PortfolioCoinbaseFra } from './data/coinbase-fra.service';
import { PortfolioPACHistory } from './data/pac-history.service';
import { PortfolioPAC } from './data/pac.service';
import { CoingeckoService } from './http/coingecko.service';
import { CryptopanicService } from './http/cryptopanic.service';
import { GoogleSheetService } from './http/google-sheet.service';
import { TokeninsightService } from './http/tokeninsight.service';

/**
 * PortfolioInitializerService
 *
 * This service is responsible for initializing the application's portfolio data at startup.
 * It's registered as an APP_INITIALIZER in app.module.ts and runs before the application starts.
 *
 * Key responsibilities:
 * - Authentication validation and session management
 * - ETF & FED data initialization from Google Sheets
 * - User-specific portfolio data loading based on email
 * - Crypto prices and ratings initialization (CRITICAL for app startup)
 * - News data state initialization (but NOT fetching - done on-demand in NewsComponent)
 *
 * Supported users:
 * - <EMAIL>: Full portfolio access (all 5 portfolios)
 * - <EMAIL>: Single portfolio access (portfolio5 only)
 */
@Injectable({
  providedIn: 'root',
})
export class PortfolioInitializerService {
  constructor(
    // Portfolio History Services (for historical data charts)
    private portfolioHistory: PortfolioCoinbaseFraHistory, // Fra's Coinbase history
    private portfolio2History: PortfolioBinanceFraHistory, // Fra's Binance history
    private portfolio3History: PortfolioBinanceAndreaHistory, // Andrea's Binance history
    private portfolio4History: PortfolioPACHistory, // PAC strategy history
    private portfolio5History: PortfolioBinanceElisaHistory, // Elisa's Binance history

    // Portfolio Current Data Services (for current holdings and stats)
    private portfolio: PortfolioCoinbaseFra, // Fra's Coinbase current data
    private portfolio2: PortfolioBinanceFra, // Fra's Binance current data
    private portfolio3: PortfolioBinanceAndrea, // Andrea's Binance current data
    private portfolio4: PortfolioPAC, // PAC strategy current data
    private portfolio5: PortfolioBinanceElisa, // Elisa's Binance current data

    // External API Services
    private coingeckoService: CoingeckoService, // Crypto prices and market data
    private tokeninsightService: TokeninsightService, // Crypto ratings and news
    private cryptopanicService: CryptopanicService, // Additional news source
    private authService: AuthService, // Authentication management
    private googleSheetService: GoogleSheetService, // ETF & FED data from Google Sheets
    // private firebaseService: FirebaseService,     // Deprecated - kept for reference
  ) {}

  /**
   * Main initialization method called by APP_INITIALIZER
   * This method runs before the Angular application starts
   *
   * @returns Promise<void> - Resolves when initialization is complete
   */
  initialize(): Promise<void> {
    // === DEBUGGING: Log current fetch status for all data sources ===
    console.log('=== PORTFOLIO INITIALIZER START ===');
    console.log('FetchData Price:', this.coingeckoService.shouldFetchData());
    console.log(
      'FetchData Ratings:',
      this.tokeninsightService.shouldFetchRating(),
    );
    console.log('FetchData News:', this.tokeninsightService.shouldFetchNews());
    console.log('FetchData Google:', this.googleSheetService.shouldFetchData());
    console.log(
      'FetchData Google ETF & FED:',
      this.googleSheetService.shouldFetchEtfFedData(),
    );
    console.log('Login Time Expired:', this.authService.loginTimeExpired());

    // === AUTHENTICATION CHECK ===
    // If user session has expired, clear all cached data and exit early
    if (this.authService.loginTimeExpired()) {
      console.log('Session expired - clearing localStorage');
      localStorage.clear();
      return Promise.resolve();
    }

    // === ETF & FED DATA INITIALIZATION ===
    // This data is used for market analysis and is fetched from Google Sheets
    if (this.googleSheetService.shouldFetchEtfFedData()) {
      console.log('Fetching fresh ETF & FED data from Google Sheets');
      firstValueFrom(this.googleSheetService.fetchEtfBtcEthHistoryData());
    } else {
      console.log('Loading ETF & FED data from localStorage');
      // Load cached ETF data from localStorage
      this.googleSheetService.etfBtcHistory.set(
        this.loadFromLocalStorage('etfBtcHistoryGoogle'),
      );
      this.googleSheetService.etfEthHistory.set(
        this.loadFromLocalStorage('etfEthHistoryGoogle'),
      );
    }

    // === USER-SPECIFIC PORTFOLIO INITIALIZATION ===
    // Determine which portfolios to initialize based on user email
    const userEmail = this.loadFromLocalStorage<{ email: string }>(
      'user',
    )?.email;
    console.log('Initializing portfolios for user:', userEmail);

    if (userEmail === '<EMAIL>') {
      // PAC user has access to all 5 portfolios
      return this.initPacPortfolio();
    } else if (userEmail === '<EMAIL>') {
      // Elisa user has access only to portfolio5
      return this.initElisaPortfolio();
    }

    // No recognized user - skip portfolio initialization
    console.log('No recognized user - skipping portfolio initialization');
    return Promise.resolve();
  }

  /**
   * Initialize PAC user portfolio (all 5 portfolios)
   * PAC user has access to:
   * - portfolio: Fra's Coinbase
   * - portfolio2: Fra's Binance
   * - portfolio3: Andrea's Binance
   * - portfolio4: PAC strategy
   * - portfolio5: Elisa's Binance
   */
  private initPacPortfolio(): Promise<void> {
    console.log('=== INITIALIZING PAC PORTFOLIO (ALL 5 PORTFOLIOS) ===');

    // === SCENARIO 1: Need to fetch both prices AND ratings ===
    if (
      this.coingeckoService.shouldFetchData() &&
      this.tokeninsightService.shouldFetchRating()
    ) {
      console.log('Fetching fresh crypto prices and ratings');
      this.coingeckoService
        .getCryptoStats()
        .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
        .subscribe();
    }

    // === SCENARIO 2: Need to fetch prices but ratings are cached ===
    if (
      this.coingeckoService.shouldFetchData() &&
      !this.tokeninsightService.shouldFetchRating()
    ) {
      console.log('Fetching fresh crypto prices, using cached ratings');
      this.coingeckoService
        .getCryptoStats()
        .pipe(
          tap(() =>
            this.tokeninsightService.setCoinsRating(
              this.loadFromLocalStorage('coinsRating'),
            ),
          ),
        )
        .subscribe();
    }

    // === SCENARIO 3: Prices are cached, load everything from localStorage ===
    if (!this.coingeckoService.shouldFetchData()) {
      console.log('Using cached crypto prices');

      // Check if we have portfolio data in localStorage
      if (
        localStorage.getItem('portfolioMonthly') ||
        localStorage.getItem('portfolio5Monthly')
      ) {
        console.log('Loading cached portfolio data');

        // === Load cached price data ===
        this.coingeckoService.btcPrice.set(
          this.loadFromLocalStorage('btcPrice'),
        );
        this.coingeckoService.ethPrice.set(
          this.loadFromLocalStorage('ethPrice'),
        );
        this.coingeckoService.btc24hChange.set(
          this.loadFromLocalStorage('btcPriceChange24h'),
        );
        this.coingeckoService.eth24hChange.set(
          this.loadFromLocalStorage('ethPriceChange24h'),
        );
        this.coingeckoService.eurusd.set(this.loadFromLocalStorage('eurusd'));
        this.coingeckoService.btcMarketD.set(
          this.loadFromLocalStorage('btcMarketD'),
        );
        this.coingeckoService.ethMarketD.set(
          this.loadFromLocalStorage('ethMarketD'),
        );

        // === Load cached portfolio historical data ===
        this.portfolioHistory.portfolioMonthly =
          this.loadFromLocalStorage('portfolioMonthly'); // Fra's Coinbase history
        this.portfolio2History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio2Monthly'); // Fra's Binance history
        this.portfolio3History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio3Monthly'); // Andrea's Binance history
        this.portfolio4History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio4Monthly'); // PAC strategy history
        this.portfolio5History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio5Monthly'); // Elisa's Binance history

        // === Load cached portfolio current data ===
        // Portfolio 1: Fra's Coinbase
        this.portfolio.coins = this.loadFromLocalStorage('coins');
        this.portfolio.portfolioStats =
          this.loadFromLocalStorage('portfolioStats');

        // Portfolio 2: Fra's Binance
        this.portfolio2.coins = this.loadFromLocalStorage('coins2');
        this.portfolio2.portfolioStats =
          this.loadFromLocalStorage('portfolio2Stats');

        // Portfolio 3: Andrea's Binance
        this.portfolio3.coins = this.loadFromLocalStorage('coins3');
        this.portfolio3.portfolioStats =
          this.loadFromLocalStorage('portfolio3Stats');

        // Portfolio 4: PAC Strategy
        this.portfolio4.coins = this.loadFromLocalStorage('coins4');
        this.portfolio4.portfolioStats =
          this.loadFromLocalStorage('portfolio4Stats');

        // Portfolio 5: Elisa's Binance
        this.portfolio5.coins = this.loadFromLocalStorage('coins5');
        this.portfolio5.portfolioStats =
          this.loadFromLocalStorage('portfolio5Stats');

        // === Handle ratings based on cache status ===
        if (!this.tokeninsightService.shouldFetchRating()) {
          console.log('Using cached crypto ratings');
          this.tokeninsightService.setCoinsRating(
            this.loadFromLocalStorage('coinsRating'),
          );
        }

        if (this.tokeninsightService.shouldFetchRating()) {
          console.log('Fetching fresh crypto ratings');
          this.tokeninsightService.getCryptoRatings().subscribe();
        }
      }

      // === Fallback: If no portfolio data in cache, fetch fresh data ===
      if (
        !localStorage.getItem('portfolioMonthly') ||
        !localStorage.getItem('portfolio5Monthly')
      ) {
        console.log('No portfolio data in cache - fetching fresh data');
        this.coingeckoService
          .getCryptoStats()
          .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
          .subscribe();
      }
    }

    // === NEWS INITIALIZATION (LAZY LOADING) ===
    // News are no longer fetched at startup to improve performance
    // They will be loaded on-demand when user navigates to /news
    console.log('Initializing news state (lazy loading)');
    this.tokeninsightService.addedNews.set([]);
    if (localStorage.getItem('cryptoNews')) {
      this.tokeninsightService.cryptoNews.set(
        this.loadFromLocalStorage('cryptoNews'),
      );
    }
    if (localStorage.getItem('cryptopanicNews')) {
      this.cryptopanicService.cryptopanicList.set(
        this.loadFromLocalStorage('cryptopanicNews'),
      );
    }
    // No longer need to emit 'ok News' since AppComponent doesn't wait for news anymore

    console.log('=== PAC PORTFOLIO INITIALIZATION COMPLETE ===');
    return Promise.resolve();
  }

  /**
   * Initialize Elisa user portfolio (single portfolio access)
   * Elisa user has access only to:
   * - portfolio5: Elisa's Binance account
   *
   * This method is similar to initPacPortfolio but optimized for single portfolio
   */
  private initElisaPortfolio(): Promise<void> {
    console.log('=== INITIALIZING ELISA PORTFOLIO (SINGLE PORTFOLIO) ===');

    // === SCENARIO 1: Need to fetch both prices AND ratings ===
    if (
      this.coingeckoService.shouldFetchData() &&
      this.tokeninsightService.shouldFetchRating()
    ) {
      console.log(
        'Fetching fresh crypto prices and ratings for single portfolio',
      );
      this.coingeckoService
        .getCryptoStatsSingleAccount('portfolio5') // Only fetch data for portfolio5
        .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
        .subscribe();
    }

    // === SCENARIO 2: Need to fetch prices but ratings are cached ===
    if (
      this.coingeckoService.shouldFetchData() &&
      !this.tokeninsightService.shouldFetchRating()
    ) {
      console.log(
        'Fetching fresh crypto prices for single portfolio, using cached ratings',
      );
      this.coingeckoService
        .getCryptoStatsSingleAccount('portfolio5') // Only fetch data for portfolio5
        .pipe(
          tap(() =>
            this.tokeninsightService.setCoinsRatingSinglePortfolio(
              this.loadFromLocalStorage('coinsRating'),
            ),
          ),
        )
        .subscribe();
    }

    // === SCENARIO 3: Prices are cached, load from localStorage ===
    if (!this.coingeckoService.shouldFetchData()) {
      console.log('Using cached crypto prices for single portfolio');

      // Check if we have Elisa's portfolio data in localStorage
      if (localStorage.getItem('portfolio5Monthly')) {
        console.log('Loading cached single portfolio data');

        // === Load cached price data ===
        this.coingeckoService.btcPrice.set(
          this.loadFromLocalStorage('btcPrice'),
        );
        this.coingeckoService.ethPrice.set(
          this.loadFromLocalStorage('ethPrice'),
        );
        this.coingeckoService.btc24hChange.set(
          this.loadFromLocalStorage('btcPriceChange24h'),
        );
        this.coingeckoService.eth24hChange.set(
          this.loadFromLocalStorage('ethPriceChange24h'),
        );
        this.coingeckoService.eurusd.set(this.loadFromLocalStorage('eurusd'));
        this.coingeckoService.btcMarketD.set(
          this.loadFromLocalStorage('btcMarketD'),
        );
        this.coingeckoService.ethMarketD.set(
          this.loadFromLocalStorage('ethMarketD'),
        );

        // === Load cached portfolio data (only portfolio5 for Elisa) ===
        this.portfolio5History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio5Monthly'); // Elisa's Binance history
        this.portfolio5.coins = this.loadFromLocalStorage('coins5');
        this.portfolio5.portfolioStats =
          this.loadFromLocalStorage('portfolio5Stats');

        // === Handle ratings based on cache status ===
        if (!this.tokeninsightService.shouldFetchRating()) {
          console.log('Using cached crypto ratings for single portfolio');
          this.tokeninsightService.setCoinsRatingSinglePortfolio(
            this.loadFromLocalStorage('coinsRating'),
          );
        }
        if (this.tokeninsightService.shouldFetchRating()) {
          console.log('Fetching fresh crypto ratings for single portfolio');
          this.tokeninsightService.getCryptoRatings().subscribe();
        }
      }
    }

    // === NEWS INITIALIZATION (LAZY LOADING) ===
    // News are no longer fetched at startup to improve performance
    // They will be loaded on-demand when user navigates to /news
    console.log('Initializing news state (lazy loading) for single portfolio');
    this.tokeninsightService.addedNews.set([]);
    if (localStorage.getItem('cryptoNews')) {
      this.tokeninsightService.cryptoNews.set(
        this.loadFromLocalStorage('cryptoNews'),
      );
    }
    if (localStorage.getItem('cryptopanicNews')) {
      this.cryptopanicService.cryptopanicList.set(
        this.loadFromLocalStorage('cryptopanicNews'),
      );
    }
    // No longer need to emit 'ok News' since AppComponent doesn't wait for news anymore

    console.log('=== ELISA PORTFOLIO INITIALIZATION COMPLETE ===');
    return Promise.resolve();
  }

  /**
   * Utility method to safely load and parse data from localStorage
   *
   * @param key - The localStorage key to retrieve
   * @returns Parsed data or null if key doesn't exist or parsing fails
   */
  private loadFromLocalStorage<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : null;
    } catch (error) {
      console.error(`Error parsing localStorage key "${key}":`, error);
      return null;
    }
  }
}

/*
 * ============================================================================
 * ARCHITECTURAL NOTES AND RECOMMENDATIONS
 * ============================================================================
 *
 * CURRENT STRUCTURE ANALYSIS:
 *
 * STRENGTHS:
 * ✅ Clear separation between PAC and Elisa user initialization
 * ✅ Proper caching strategy with shouldFetch* methods
 * ✅ Lazy loading implementation for news (performance improvement)
 * ✅ Comprehensive error handling and logging
 * ✅ Type safety with generic loadFromLocalStorage method
 *
 * AREAS FOR IMPROVEMENT:
 *
 * 1. CODE DUPLICATION:
 *    - initPacPortfolio() and initElisaPortfolio() share ~80% of logic
 *    - Consider extracting common initialization logic into shared methods
 *
 * 2. DEPENDENCY INJECTION:
 *    - Constructor has 18 dependencies (high coupling)
 *    - Consider using facade pattern or service aggregation
 *
 * 3. SINGLE RESPONSIBILITY:
 *    - Service handles authentication, data fetching, caching, and user routing
 *    - Consider splitting into smaller, focused services
 *
 * 4. CONFIGURATION:
 *    - User emails are hardcoded ('<EMAIL>', '<EMAIL>')
 *    - Consider moving to configuration file or environment variables
 *
 * 5. ERROR HANDLING:
 *    - Limited error handling for failed API calls
 *    - Consider implementing retry mechanisms and fallback strategies
 *
 * SUGGESTED REFACTORING (Future):
 *
 * 1. Create PortfolioConfigService for user-specific configurations
 * 2. Create DataCacheService for localStorage operations
 * 3. Create PortfolioDataService for portfolio-specific operations
 * 4. Implement proper error boundaries and retry logic
 * 5. Add unit tests for each initialization scenario
 *
 * PERFORMANCE NOTES:
 *
 * ✅ News lazy loading reduces initial load time
 * ✅ Proper caching prevents unnecessary API calls
 * ⚠️  Multiple portfolio loading could be optimized with parallel requests
 * ⚠️  Consider implementing progressive loading for large datasets
 */
//   portfolioHistory: PortfolioCoinbaseFraHistory,
//   portfolio2History: PortfolioBinanceFraHistory,
//   portfolio3History: PortfolioBinanceAndreaHistory,
//   portfolio4History: PortfolioPACHistory,
//   portfolio5History: PortfolioBinanceElisaHistory,
//   portfolio: PortfolioCoinbaseFra,
//   portfolio2: PortfolioBinanceFra,
//   portfolio3: PortfolioBinanceAndrea,
//   portfolio4: PortfolioPAC,
//   portfolio5: PortfolioBinanceElisa,
//   coingeckoService: CoingeckoService,
//   tokeninsightService: TokeninsightService,
//   cryptopanicService: CryptopanicService,
//   firebaseService: FirebaseService,
//   authService: AuthService,
//   googleSheetService: GoogleSheetService,
// ): () => Promise<void> {
//   return async () => {
//     console.log('FetchData Price:', coingeckoService.shouldFetchData());
//     console.log('FetchData Ratings:', tokeninsightService.shouldFetchRating());
//     console.log('FetchData News:', tokeninsightService.shouldFetchNews());
//     console.log('FetchData Google:', googleSheetService.shouldFetchData());
//     console.log(
//       'FetchData Google ETF & FED:',
//       googleSheetService.shouldFetchEtfFedData(),
//     );

//     console.log('Login Time Expired:', authService.loginTimeExpired());
//     // console.log('portfolio', portfolio);
//     // console.log('history', portfolioHistory);

//     if (authService.loginTimeExpired()) {
//       localStorage.clear();
//       return Promise.resolve();
//     }
//     if (!authService.loginTimeExpired()) {
//       // FETCH BTC ETF DATA - GOOGLE SHEET
//       // FETCH FED DATA - GOOGLE SHEET

//       // const google$ = [
//       //   googleSheetService.fetchEtfBtcEthHistoryData(),
//       //   googleSheetService.fetchFedProbabilty(),
//       // ];

//       if (googleSheetService.shouldFetchEtfFedData()) {
//         firstValueFrom(googleSheetService.fetchEtfBtcEthHistoryData());
//         // await lastValueFrom(forkJoin(google$));
//       }

//       if (!googleSheetService.shouldFetchEtfFedData()) {
//         googleSheetService.etfBtcHistory.set(
//           JSON.parse(localStorage.getItem('etfBtcHistoryGoogle')),
//         );
//         googleSheetService.etfEthHistory.set(
//           JSON.parse(localStorage.getItem('etfEthHistoryGoogle')),
//         );
//         // googleSheetService.fedProbabilty.set(
//         //   JSON.parse(localStorage.getItem('fedProbability')),
//         // );
//       }

//       if (
//         JSON.parse(localStorage.getItem('user'))?.email === '<EMAIL>'
//       ) {
//         // COIN DATA - Coingecko and Tokeninsight
//         if (
//           coingeckoService.shouldFetchData() &&
//           tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStats()
//             .pipe(concatMap(() => tokeninsightService.getCryptoRatings()))
//             .subscribe();
//         }

//         if (
//           coingeckoService.shouldFetchData() &&
//           !tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStats()
//             .pipe(
//               tap(() => {
//                 tokeninsightService.setCoinsRating(
//                   JSON.parse(localStorage.getItem('coinsRating')),
//                 );
//               }),
//             )
//             .subscribe();
//         }

//         if (!coingeckoService.shouldFetchData()) {
//           if (
//             localStorage.getItem('portfolioMonthly') ||
//             localStorage.getItem('portfolio5Monthly')
//           ) {
//             coingeckoService.btcPrice.set(
//               JSON.parse(localStorage.getItem('btcPrice')),
//             );

//             coingeckoService.ethPrice.set(
//               JSON.parse(localStorage.getItem('ethPrice')),
//             );

//             coingeckoService.btc24hChange.set(
//               JSON.parse(localStorage.getItem('btcPriceChange24h')),
//             );

//             coingeckoService.eth24hChange.set(
//               JSON.parse(localStorage.getItem('ethPriceChange24h')),
//             );

//             coingeckoService.eurusd.set(
//               JSON.parse(localStorage.getItem('eurusd')),
//             );

//             coingeckoService.btcMarketD.set(
//               JSON.parse(localStorage.getItem('btcMarketD')),
//             );

//             coingeckoService.ethMarketD.set(
//               JSON.parse(localStorage.getItem('ethMarketD')),
//             );

//             // Portfolios

//             portfolioHistory.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolioMonthly'),
//             );

//             portfolio2History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio2Monthly'),
//             );
//             portfolio3History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio3Monthly'),
//             );
//             portfolio4History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio4Monthly'),
//             );
//             portfolio5History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio5Monthly'),
//             );

//             portfolio.coins = JSON.parse(localStorage.getItem('coins'));
//             portfolio.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolioStats'),
//             );

//             portfolio2.coins = JSON.parse(localStorage.getItem('coins2'));
//             portfolio2.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio2Stats'),
//             );

//             portfolio3.coins = JSON.parse(localStorage.getItem('coins3'));
//             portfolio3.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio3Stats'),
//             );

//             portfolio4.coins = JSON.parse(localStorage.getItem('coins4'));
//             portfolio4.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio4Stats'),
//             );

//             portfolio5.coins = JSON.parse(localStorage.getItem('coins5'));
//             portfolio5.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio5Stats'),
//             );

//             if (!tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.setCoinsRating(
//                 JSON.parse(localStorage.getItem('coinsRating')),
//               );
//             }

//             if (tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.getCryptoRatings().subscribe(() => {});
//             }
//           }
//           if (
//             !localStorage.getItem('portfolioMonthly') ||
//             !localStorage.getItem('portfolio5Monthly')
//           ) {
//             coingeckoService
//               .getCryptoStats()
//               .pipe(concatMap(() => tokeninsightService.getCryptoRatings()))
//               .subscribe();
//           }
//         }

//         if (tokeninsightService.shouldFetchNews()) {
//           tokeninsightService
//             .getCryptoNews()
//             .pipe(
//               concatMap((data) => {
//                 // console.log('NEWS OK', tokeninsightService.cryptoNews());

//                 return cryptopanicService.getUpdates();
//               }),
//             )
//             .subscribe({
//               next: (data) => {
//                 if (localStorage.getItem('cryptoNews')) {
//                   const prevNews = [
//                     ...tokeninsightService.prevNews1,
//                     ...cryptopanicService.prevNews2,
//                   ];

//                   const actualNews = [
//                     ...tokeninsightService.cryptoNews(),
//                     ...cryptopanicService.cryptopanicList(),
//                   ];
//                   // console.log('PREV NEWS', prevNews);
//                   // console.log('ACTUAL NEWS', actualNews);

//                   function sonoOggettiUguali(objA, objB) {
//                     // Implementa il tuo metodo di confronto, ad esempio confronto di chiavi e valori
//                     // Questo esempio assume che gli oggetti abbiano gli stessi campi
//                     return JSON.stringify(objA) === JSON.stringify(objB);
//                   }

//                   let addedNews2 = [];

//                   actualNews.forEach((item) => {
//                     let oggettoOrigine = prevNews.find((oggetto) => {
//                       return oggetto.title === item.title;
//                     });

//                     if (
//                       !oggettoOrigine ||
//                       !sonoOggettiUguali(oggettoOrigine, item)
//                     ) {
//                       addedNews2.push(item);
//                     }
//                   });

//                   tokeninsightService.addedNews.set(addedNews2);
//                   tokeninsightService.loadingNews$.next('ok News');

//                   // console.log('ADDED NEWS', addedNews2);
//                 }
//               },

//               error: (error) => {
//                 console.error(error);
//               },
//             });
//         }

//         if (!tokeninsightService.shouldFetchNews()) {
//           tokeninsightService.addedNews.set([]);

//           tokeninsightService.cryptoNews.set(
//             JSON.parse(localStorage.getItem('cryptoNews')),
//           );

//           cryptopanicService.cryptopanicList.set(
//             JSON.parse(localStorage.getItem('cryptopanicNews')),
//           );

//           tokeninsightService.loadingNews$.next('ok News');

//           // cryptopanicService.loading$.next('ok');

//           // console.log('NEWS OK - localstorage', tokeninsightService.cryptoNews());
//         }
//         // await new Promise((resolve, reject) => {
//         //   console.log('4 E 5', portfolio4.coins, portfolio5.coins);
//         // });

//         return Promise.resolve();
//       } else if (
//         JSON.parse(localStorage.getItem('user'))?.email ===
//         '<EMAIL>'
//       ) {
//         // ETF DATA - Firestore
//         // await new Promise((resolve) => {
//         //   firebaseService.getEtfStats().subscribe({
//         //     next: (data: any) => {
//         //       // let newEtfHistoryWeekly = data[2].weeklyHistory.reverse();
//         //       let newEtfHistoryDaily = data[0].dailyHistory.reverse();
//         //       // console.log('ETF DATA', newEtfHistoryDaily);

//         //       if (localStorage.getItem('etfHistory')) {
//         //         if (
//         //           JSON.parse(localStorage.getItem('etfHistory'))[0].date ==
//         //           newEtfHistoryDaily[0].date
//         //         ) {
//         //           firebaseService.etfHistory.set(newEtfHistoryDaily);
//         //         } else {
//         //           firebaseService.etfHistory.set(newEtfHistoryDaily);
//         //           firebaseService.etfStatsNewDate.set(true);
//         //           localStorage.setItem(
//         //             'etfHistory',
//         //             JSON.stringify(newEtfHistoryDaily),
//         //           );
//         //         }
//         //         resolve(true);
//         //       } else {
//         //         firebaseService.etfHistory.set(newEtfHistoryDaily);
//         //         firebaseService.etfStatsNewDate.set(true);
//         //         localStorage.setItem(
//         //           'etfHistory',
//         //           JSON.stringify(newEtfHistoryDaily),
//         //         );

//         //         resolve(true);
//         //       }
//         //     },
//         //     error: (error) => {
//         //       console.log(error.message);
//         //       resolve(false);
//         //     },
//         //   });
//         // });

//         if (
//           coingeckoService.shouldFetchData() &&
//           tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStatsSingleAccount('portfolio5')
//             .pipe(concatMap(() => tokeninsightService.getCryptoRatings()))
//             .subscribe();
//         }

//         if (
//           coingeckoService.shouldFetchData() &&
//           !tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStatsSingleAccount('portfolio5')
//             .pipe(
//               tap(() => {
//                 tokeninsightService.setCoinsRatingSinglePortfolio(
//                   JSON.parse(localStorage.getItem('coinsRating')),
//                 );
//               }),
//             )
//             .subscribe();
//         }

//         if (!coingeckoService.shouldFetchData()) {
//           if (localStorage.getItem('portfolio5Monthly')) {
//             coingeckoService.btcPrice.set(
//               JSON.parse(localStorage.getItem('btcPrice')),
//             );

//             coingeckoService.ethPrice.set(
//               JSON.parse(localStorage.getItem('ethPrice')),
//             );

//             coingeckoService.btc24hChange.set(
//               JSON.parse(localStorage.getItem('btcPriceChange24h')),
//             );

//             coingeckoService.eth24hChange.set(
//               JSON.parse(localStorage.getItem('ethPriceChange24h')),
//             );

//             coingeckoService.eurusd.set(
//               JSON.parse(localStorage.getItem('eurusd')),
//             );

//             coingeckoService.btcMarketD.set(
//               JSON.parse(localStorage.getItem('btcMarketD')),
//             );

//             coingeckoService.ethMarketD.set(
//               JSON.parse(localStorage.getItem('ethMarketD')),
//             );

//             portfolio5History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio5Monthly'),
//             );

//             portfolio5.coins = JSON.parse(localStorage.getItem('coins5'));
//             portfolio5.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio5Stats'),
//             );

//             if (!tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.setCoinsRatingSinglePortfolio(
//                 JSON.parse(localStorage.getItem('coinsRating')),
//               );
//             }

//             if (tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.getCryptoRatings().subscribe(() => {});
//             }
//           }
//         }

//         if (tokeninsightService.shouldFetchNews()) {
//           tokeninsightService
//             .getCryptoNews()
//             .pipe(
//               concatMap((data) => {
//                 console.log(
//                   'TOKENINISGHT OK',
//                   tokeninsightService.cryptoNews(),
//                 );

//                 return cryptopanicService.getUpdates();
//               }),
//             )
//             .subscribe({
//               next: (data) => {
//                 if (localStorage.getItem('cryptoNews')) {
//                   const prevNews = [
//                     ...tokeninsightService.prevNews1,
//                     ...cryptopanicService.prevNews2,
//                   ];

//                   const actualNews = [
//                     ...tokeninsightService.cryptoNews(),
//                     ...cryptopanicService.cryptopanicList(),
//                   ];
//                   // console.log('PREV NEWS', prevNews);
//                   // console.log('ACTUAL NEWS', actualNews);

//                   function sonoOggettiUguali(objA, objB) {
//                     return JSON.stringify(objA) === JSON.stringify(objB);
//                   }

//                   let addedNews2 = [];

//                   actualNews.forEach((item) => {
//                     let oggettoOrigine = prevNews.find((oggetto) => {
//                       return oggetto.title === item.title;
//                     });

//                     if (
//                       !oggettoOrigine ||
//                       !sonoOggettiUguali(oggettoOrigine, item)
//                     ) {
//                       addedNews2.push(item);
//                     }
//                   });

//                   tokeninsightService.addedNews.set(addedNews2);

//                   tokeninsightService.loadingNews$.next('ok News');

//                   // cryptopanicService.loading$.next('ok');

//                   console.log('ADDED NEWS', addedNews2);
//                 }
//               },

//               error: (error) => {
//                 console.error(error);
//               },
//             });
//         }

//         if (!tokeninsightService.shouldFetchNews()) {
//           tokeninsightService.addedNews.set([]);

//           tokeninsightService.cryptoNews.set(
//             JSON.parse(localStorage.getItem('cryptoNews')),
//           );

//           cryptopanicService.cryptopanicList.set(
//             JSON.parse(localStorage.getItem('cryptopanicNews')),
//           );

//           tokeninsightService.loadingNews$.next('ok News');

//           // cryptopanicService.loading$.next('ok');

//           // console.log('NEWS OK - localstorage', tokeninsightService.cryptoNews());
//         }

//         return Promise.resolve();
//       } else {
//         return Promise.resolve();
//       }
//     }
//   };
// }
