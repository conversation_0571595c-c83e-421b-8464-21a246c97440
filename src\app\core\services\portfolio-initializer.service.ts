import { Injectable } from '@angular/core';
import { concatMap, firstValueFrom, tap } from 'rxjs';
import { AuthService } from './auth/auth.service';
import { PortfolioBinanceAndreaHistory } from './data/binance-andrea-history.service';
import { PortfolioBinanceAndrea } from './data/binance-andrea.service';
import { PortfolioBinanceElisaHistory } from './data/binance-elisa-history.service';
import { PortfolioBinanceElisa } from './data/binance-elisa.service';
import { PortfolioBinanceFraHistory } from './data/binance-fra-history.service';
import { PortfolioBinanceFra } from './data/binance-fra.service';
import { PortfolioCoinbaseFraHistory } from './data/coinbase-fra-history.service';
import { PortfolioCoinbaseFra } from './data/coinbase-fra.service';
import { PortfolioPACHistory } from './data/pac-history.service';
import { PortfolioPAC } from './data/pac.service';
import { CoingeckoService } from './http/coingecko.service';
import { CryptopanicService } from './http/cryptopanic.service';
import { GoogleSheetService } from './http/google-sheet.service';
import { TokeninsightService } from './http/tokeninsight.service';

@Injectable({
  providedIn: 'root',
})
export class PortfolioInitializerService {
  constructor(
    private portfolioHistory: PortfolioCoinbaseFraHistory,
    private portfolio2History: PortfolioBinanceFraHistory,
    private portfolio3History: PortfolioBinanceAndreaHistory,
    private portfolio4History: PortfolioPACHistory,
    private portfolio5History: PortfolioBinanceElisaHistory,
    private portfolio: PortfolioCoinbaseFra,
    private portfolio2: PortfolioBinanceFra,
    private portfolio3: PortfolioBinanceAndrea,
    private portfolio4: PortfolioPAC,
    private portfolio5: PortfolioBinanceElisa,
    private coingeckoService: CoingeckoService,
    private tokeninsightService: TokeninsightService,
    private cryptopanicService: CryptopanicService,
    // private firebaseService: FirebaseService,
    private authService: AuthService,
    private googleSheetService: GoogleSheetService,
  ) {}

  initialize(): Promise<void> {
    // Log fetch data status
    console.log('FetchData Price:', this.coingeckoService.shouldFetchData());
    console.log(
      'FetchData Ratings:',
      this.tokeninsightService.shouldFetchRating(),
    );
    console.log('FetchData News:', this.tokeninsightService.shouldFetchNews());
    console.log('FetchData Google:', this.googleSheetService.shouldFetchData());
    console.log(
      'FetchData Google ETF & FED:',
      this.googleSheetService.shouldFetchEtfFedData(),
    );

    console.log('Login Time Expired:', this.authService.loginTimeExpired());

    // If login time expired, clear local storage (User not logged in)
    if (this.authService.loginTimeExpired()) {
      localStorage.clear();
      return Promise.resolve();
    }

    // Fetch ETF & FED data from Google Sheet
    if (this.googleSheetService.shouldFetchEtfFedData()) {
      firstValueFrom(this.googleSheetService.fetchEtfBtcEthHistoryData());
    } else {
      // If ETF & FED data already fetched, load from local storage
      this.googleSheetService.etfBtcHistory.set(
        this.loadFromLocalStorage('etfBtcHistoryGoogle'),
      );
      this.googleSheetService.etfEthHistory.set(
        this.loadFromLocalStorage('etfEthHistoryGoogle'),
      );
    }

    const userEmail = this.loadFromLocalStorage<{ email: string }>(
      'user',
    )?.email;

    // Initialize portfolio data based on user email
    if (userEmail === '<EMAIL>') {
      return this.initPacPortfolio();
    } else if (userEmail === '<EMAIL>') {
      return this.initElisaPortfolio();
    }

    // If no user email, return promise
    return Promise.resolve();
  }

  private initPacPortfolio(): Promise<void> {
    if (
      this.coingeckoService.shouldFetchData() &&
      this.tokeninsightService.shouldFetchRating()
    ) {
      this.coingeckoService
        .getCryptoStats()
        .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
        .subscribe();
    }

    if (
      this.coingeckoService.shouldFetchData() &&
      !this.tokeninsightService.shouldFetchRating()
    ) {
      this.coingeckoService
        .getCryptoStats()
        .pipe(
          tap(() =>
            this.tokeninsightService.setCoinsRating(
              this.loadFromLocalStorage('coinsRating'),
            ),
          ),
        )
        .subscribe();
    }

    if (!this.coingeckoService.shouldFetchData()) {
      if (
        localStorage.getItem('portfolioMonthly') ||
        localStorage.getItem('portfolio5Monthly')
      ) {
        this.coingeckoService.btcPrice.set(
          this.loadFromLocalStorage('btcPrice'),
        );
        this.coingeckoService.ethPrice.set(
          this.loadFromLocalStorage('ethPrice'),
        );
        this.coingeckoService.btc24hChange.set(
          this.loadFromLocalStorage('btcPriceChange24h'),
        );
        this.coingeckoService.eth24hChange.set(
          this.loadFromLocalStorage('ethPriceChange24h'),
        );
        this.coingeckoService.eurusd.set(this.loadFromLocalStorage('eurusd'));
        this.coingeckoService.btcMarketD.set(
          this.loadFromLocalStorage('btcMarketD'),
        );
        this.coingeckoService.ethMarketD.set(
          this.loadFromLocalStorage('ethMarketD'),
        );

        this.portfolioHistory.portfolioMonthly =
          this.loadFromLocalStorage('portfolioMonthly');
        this.portfolio2History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio2Monthly');
        this.portfolio3History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio3Monthly');
        this.portfolio4History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio4Monthly');
        this.portfolio5History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio5Monthly');

        this.portfolio.coins = this.loadFromLocalStorage('coins');
        this.portfolio.portfolioStats =
          this.loadFromLocalStorage('portfolioStats');

        this.portfolio2.coins = this.loadFromLocalStorage('coins2');
        this.portfolio2.portfolioStats =
          this.loadFromLocalStorage('portfolio2Stats');

        this.portfolio3.coins = this.loadFromLocalStorage('coins3');
        this.portfolio3.portfolioStats =
          this.loadFromLocalStorage('portfolio3Stats');

        this.portfolio4.coins = this.loadFromLocalStorage('coins4');
        this.portfolio4.portfolioStats =
          this.loadFromLocalStorage('portfolio4Stats');

        this.portfolio5.coins = this.loadFromLocalStorage('coins5');
        this.portfolio5.portfolioStats =
          this.loadFromLocalStorage('portfolio5Stats');

        if (!this.tokeninsightService.shouldFetchRating()) {
          this.tokeninsightService.setCoinsRating(
            this.loadFromLocalStorage('coinsRating'),
          );
        }

        if (this.tokeninsightService.shouldFetchRating()) {
          this.tokeninsightService.getCryptoRatings().subscribe();
        }
      }
      if (
        !localStorage.getItem('portfolioMonthly') ||
        !localStorage.getItem('portfolio5Monthly')
      ) {
        this.coingeckoService
          .getCryptoStats()
          .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
          .subscribe();
      }
    }

    if (this.tokeninsightService.shouldFetchNews()) {
      this.tokeninsightService
        .getCryptoNews()
        .pipe(concatMap(() => this.cryptopanicService.getUpdates()))
        .subscribe({
          next: () => {
            if (localStorage.getItem('cryptoNews')) {
              const prevNews = [
                ...this.tokeninsightService.prevNews1,
                ...this.cryptopanicService.prevNews2,
              ];
              const actualNews = [
                ...this.tokeninsightService.cryptoNews(),
                ...this.cryptopanicService.cryptopanicList(),
              ];

              const addedNews2 = actualNews.filter((item) => {
                const origin = prevNews.find((o) => o.title === item.title);
                return (
                  !origin || JSON.stringify(origin) !== JSON.stringify(item)
                );
              });

              this.tokeninsightService.addedNews.set(addedNews2);
              this.tokeninsightService.loadingNews$.next('ok News');
            }
          },
          error: (error) => console.error(error),
        });
    }

    if (!this.tokeninsightService.shouldFetchNews()) {
      this.tokeninsightService.addedNews.set([]);
      this.tokeninsightService.cryptoNews.set(
        this.loadFromLocalStorage('cryptoNews'),
      );
      this.cryptopanicService.cryptopanicList.set(
        this.loadFromLocalStorage('cryptopanicNews'),
      );
      this.tokeninsightService.loadingNews$.next('ok News');
    }

    return Promise.resolve();
  }

  private initElisaPortfolio(): Promise<void> {
    if (
      this.coingeckoService.shouldFetchData() &&
      this.tokeninsightService.shouldFetchRating()
    ) {
      this.coingeckoService
        .getCryptoStatsSingleAccount('portfolio5')
        .pipe(concatMap(() => this.tokeninsightService.getCryptoRatings()))
        .subscribe();
    }

    if (
      this.coingeckoService.shouldFetchData() &&
      !this.tokeninsightService.shouldFetchRating()
    ) {
      this.coingeckoService
        .getCryptoStatsSingleAccount('portfolio5')
        .pipe(
          tap(() =>
            this.tokeninsightService.setCoinsRatingSinglePortfolio(
              this.loadFromLocalStorage('coinsRating'),
            ),
          ),
        )
        .subscribe();
    }

    if (!this.coingeckoService.shouldFetchData()) {
      if (localStorage.getItem('portfolio5Monthly')) {
        this.coingeckoService.btcPrice.set(
          this.loadFromLocalStorage('btcPrice'),
        );
        this.coingeckoService.ethPrice.set(
          this.loadFromLocalStorage('ethPrice'),
        );
        this.coingeckoService.btc24hChange.set(
          this.loadFromLocalStorage('btcPriceChange24h'),
        );
        this.coingeckoService.eth24hChange.set(
          this.loadFromLocalStorage('ethPriceChange24h'),
        );
        this.coingeckoService.eurusd.set(this.loadFromLocalStorage('eurusd'));
        this.coingeckoService.btcMarketD.set(
          this.loadFromLocalStorage('btcMarketD'),
        );
        this.coingeckoService.ethMarketD.set(
          this.loadFromLocalStorage('ethMarketD'),
        );

        this.portfolio5History.portfolioMonthly =
          this.loadFromLocalStorage('portfolio5Monthly');
        this.portfolio5.coins = this.loadFromLocalStorage('coins5');
        this.portfolio5.portfolioStats =
          this.loadFromLocalStorage('portfolio5Stats');

        if (!this.tokeninsightService.shouldFetchRating()) {
          this.tokeninsightService.setCoinsRatingSinglePortfolio(
            this.loadFromLocalStorage('coinsRating'),
          );
        }
        if (this.tokeninsightService.shouldFetchRating()) {
          this.tokeninsightService.getCryptoRatings().subscribe();
        }
      }
    }

    if (this.tokeninsightService.shouldFetchNews()) {
      this.tokeninsightService
        .getCryptoNews()
        .pipe(concatMap(() => this.cryptopanicService.getUpdates()))
        .subscribe({
          next: () => {
            if (localStorage.getItem('cryptoNews')) {
              const prevNews = [
                ...this.tokeninsightService.prevNews1,
                ...this.cryptopanicService.prevNews2,
              ];
              const actualNews = [
                ...this.tokeninsightService.cryptoNews(),
                ...this.cryptopanicService.cryptopanicList(),
              ];

              const addedNews2 = actualNews.filter((item) => {
                const origin = prevNews.find((o) => o.title === item.title);
                return (
                  !origin || JSON.stringify(origin) !== JSON.stringify(item)
                );
              });

              this.tokeninsightService.addedNews.set(addedNews2);
              this.tokeninsightService.loadingNews$.next('ok News');
            }
          },
          error: (error) => console.error(error),
        });
    }

    if (!this.tokeninsightService.shouldFetchNews()) {
      this.tokeninsightService.addedNews.set([]);
      this.tokeninsightService.cryptoNews.set(
        this.loadFromLocalStorage('cryptoNews'),
      );
      this.cryptopanicService.cryptopanicList.set(
        this.loadFromLocalStorage('cryptopanicNews'),
      );
      this.tokeninsightService.loadingNews$.next('ok News');
    }

    return Promise.resolve();
  }

  private loadFromLocalStorage<T>(key: string): T | null {
    const item = localStorage.getItem(key);
    return item ? (JSON.parse(item) as T) : null;
  }
}

// PREVIOUS VERSION

// export function initializePortfolio(
//   portfolioHistory: PortfolioCoinbaseFraHistory,
//   portfolio2History: PortfolioBinanceFraHistory,
//   portfolio3History: PortfolioBinanceAndreaHistory,
//   portfolio4History: PortfolioPACHistory,
//   portfolio5History: PortfolioBinanceElisaHistory,
//   portfolio: PortfolioCoinbaseFra,
//   portfolio2: PortfolioBinanceFra,
//   portfolio3: PortfolioBinanceAndrea,
//   portfolio4: PortfolioPAC,
//   portfolio5: PortfolioBinanceElisa,
//   coingeckoService: CoingeckoService,
//   tokeninsightService: TokeninsightService,
//   cryptopanicService: CryptopanicService,
//   firebaseService: FirebaseService,
//   authService: AuthService,
//   googleSheetService: GoogleSheetService,
// ): () => Promise<void> {
//   return async () => {
//     console.log('FetchData Price:', coingeckoService.shouldFetchData());
//     console.log('FetchData Ratings:', tokeninsightService.shouldFetchRating());
//     console.log('FetchData News:', tokeninsightService.shouldFetchNews());
//     console.log('FetchData Google:', googleSheetService.shouldFetchData());
//     console.log(
//       'FetchData Google ETF & FED:',
//       googleSheetService.shouldFetchEtfFedData(),
//     );

//     console.log('Login Time Expired:', authService.loginTimeExpired());
//     // console.log('portfolio', portfolio);
//     // console.log('history', portfolioHistory);

//     if (authService.loginTimeExpired()) {
//       localStorage.clear();
//       return Promise.resolve();
//     }
//     if (!authService.loginTimeExpired()) {
//       // FETCH BTC ETF DATA - GOOGLE SHEET
//       // FETCH FED DATA - GOOGLE SHEET

//       // const google$ = [
//       //   googleSheetService.fetchEtfBtcEthHistoryData(),
//       //   googleSheetService.fetchFedProbabilty(),
//       // ];

//       if (googleSheetService.shouldFetchEtfFedData()) {
//         firstValueFrom(googleSheetService.fetchEtfBtcEthHistoryData());
//         // await lastValueFrom(forkJoin(google$));
//       }

//       if (!googleSheetService.shouldFetchEtfFedData()) {
//         googleSheetService.etfBtcHistory.set(
//           JSON.parse(localStorage.getItem('etfBtcHistoryGoogle')),
//         );
//         googleSheetService.etfEthHistory.set(
//           JSON.parse(localStorage.getItem('etfEthHistoryGoogle')),
//         );
//         // googleSheetService.fedProbabilty.set(
//         //   JSON.parse(localStorage.getItem('fedProbability')),
//         // );
//       }

//       if (
//         JSON.parse(localStorage.getItem('user'))?.email === '<EMAIL>'
//       ) {
//         // COIN DATA - Coingecko and Tokeninsight
//         if (
//           coingeckoService.shouldFetchData() &&
//           tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStats()
//             .pipe(concatMap(() => tokeninsightService.getCryptoRatings()))
//             .subscribe();
//         }

//         if (
//           coingeckoService.shouldFetchData() &&
//           !tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStats()
//             .pipe(
//               tap(() => {
//                 tokeninsightService.setCoinsRating(
//                   JSON.parse(localStorage.getItem('coinsRating')),
//                 );
//               }),
//             )
//             .subscribe();
//         }

//         if (!coingeckoService.shouldFetchData()) {
//           if (
//             localStorage.getItem('portfolioMonthly') ||
//             localStorage.getItem('portfolio5Monthly')
//           ) {
//             coingeckoService.btcPrice.set(
//               JSON.parse(localStorage.getItem('btcPrice')),
//             );

//             coingeckoService.ethPrice.set(
//               JSON.parse(localStorage.getItem('ethPrice')),
//             );

//             coingeckoService.btc24hChange.set(
//               JSON.parse(localStorage.getItem('btcPriceChange24h')),
//             );

//             coingeckoService.eth24hChange.set(
//               JSON.parse(localStorage.getItem('ethPriceChange24h')),
//             );

//             coingeckoService.eurusd.set(
//               JSON.parse(localStorage.getItem('eurusd')),
//             );

//             coingeckoService.btcMarketD.set(
//               JSON.parse(localStorage.getItem('btcMarketD')),
//             );

//             coingeckoService.ethMarketD.set(
//               JSON.parse(localStorage.getItem('ethMarketD')),
//             );

//             // Portfolios

//             portfolioHistory.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolioMonthly'),
//             );

//             portfolio2History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio2Monthly'),
//             );
//             portfolio3History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio3Monthly'),
//             );
//             portfolio4History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio4Monthly'),
//             );
//             portfolio5History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio5Monthly'),
//             );

//             portfolio.coins = JSON.parse(localStorage.getItem('coins'));
//             portfolio.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolioStats'),
//             );

//             portfolio2.coins = JSON.parse(localStorage.getItem('coins2'));
//             portfolio2.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio2Stats'),
//             );

//             portfolio3.coins = JSON.parse(localStorage.getItem('coins3'));
//             portfolio3.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio3Stats'),
//             );

//             portfolio4.coins = JSON.parse(localStorage.getItem('coins4'));
//             portfolio4.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio4Stats'),
//             );

//             portfolio5.coins = JSON.parse(localStorage.getItem('coins5'));
//             portfolio5.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio5Stats'),
//             );

//             if (!tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.setCoinsRating(
//                 JSON.parse(localStorage.getItem('coinsRating')),
//               );
//             }

//             if (tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.getCryptoRatings().subscribe(() => {});
//             }
//           }
//           if (
//             !localStorage.getItem('portfolioMonthly') ||
//             !localStorage.getItem('portfolio5Monthly')
//           ) {
//             coingeckoService
//               .getCryptoStats()
//               .pipe(concatMap(() => tokeninsightService.getCryptoRatings()))
//               .subscribe();
//           }
//         }

//         if (tokeninsightService.shouldFetchNews()) {
//           tokeninsightService
//             .getCryptoNews()
//             .pipe(
//               concatMap((data) => {
//                 // console.log('NEWS OK', tokeninsightService.cryptoNews());

//                 return cryptopanicService.getUpdates();
//               }),
//             )
//             .subscribe({
//               next: (data) => {
//                 if (localStorage.getItem('cryptoNews')) {
//                   const prevNews = [
//                     ...tokeninsightService.prevNews1,
//                     ...cryptopanicService.prevNews2,
//                   ];

//                   const actualNews = [
//                     ...tokeninsightService.cryptoNews(),
//                     ...cryptopanicService.cryptopanicList(),
//                   ];
//                   // console.log('PREV NEWS', prevNews);
//                   // console.log('ACTUAL NEWS', actualNews);

//                   function sonoOggettiUguali(objA, objB) {
//                     // Implementa il tuo metodo di confronto, ad esempio confronto di chiavi e valori
//                     // Questo esempio assume che gli oggetti abbiano gli stessi campi
//                     return JSON.stringify(objA) === JSON.stringify(objB);
//                   }

//                   let addedNews2 = [];

//                   actualNews.forEach((item) => {
//                     let oggettoOrigine = prevNews.find((oggetto) => {
//                       return oggetto.title === item.title;
//                     });

//                     if (
//                       !oggettoOrigine ||
//                       !sonoOggettiUguali(oggettoOrigine, item)
//                     ) {
//                       addedNews2.push(item);
//                     }
//                   });

//                   tokeninsightService.addedNews.set(addedNews2);
//                   tokeninsightService.loadingNews$.next('ok News');

//                   // console.log('ADDED NEWS', addedNews2);
//                 }
//               },

//               error: (error) => {
//                 console.error(error);
//               },
//             });
//         }

//         if (!tokeninsightService.shouldFetchNews()) {
//           tokeninsightService.addedNews.set([]);

//           tokeninsightService.cryptoNews.set(
//             JSON.parse(localStorage.getItem('cryptoNews')),
//           );

//           cryptopanicService.cryptopanicList.set(
//             JSON.parse(localStorage.getItem('cryptopanicNews')),
//           );

//           tokeninsightService.loadingNews$.next('ok News');

//           // cryptopanicService.loading$.next('ok');

//           // console.log('NEWS OK - localstorage', tokeninsightService.cryptoNews());
//         }
//         // await new Promise((resolve, reject) => {
//         //   console.log('4 E 5', portfolio4.coins, portfolio5.coins);
//         // });

//         return Promise.resolve();
//       } else if (
//         JSON.parse(localStorage.getItem('user'))?.email ===
//         '<EMAIL>'
//       ) {
//         // ETF DATA - Firestore
//         // await new Promise((resolve) => {
//         //   firebaseService.getEtfStats().subscribe({
//         //     next: (data: any) => {
//         //       // let newEtfHistoryWeekly = data[2].weeklyHistory.reverse();
//         //       let newEtfHistoryDaily = data[0].dailyHistory.reverse();
//         //       // console.log('ETF DATA', newEtfHistoryDaily);

//         //       if (localStorage.getItem('etfHistory')) {
//         //         if (
//         //           JSON.parse(localStorage.getItem('etfHistory'))[0].date ==
//         //           newEtfHistoryDaily[0].date
//         //         ) {
//         //           firebaseService.etfHistory.set(newEtfHistoryDaily);
//         //         } else {
//         //           firebaseService.etfHistory.set(newEtfHistoryDaily);
//         //           firebaseService.etfStatsNewDate.set(true);
//         //           localStorage.setItem(
//         //             'etfHistory',
//         //             JSON.stringify(newEtfHistoryDaily),
//         //           );
//         //         }
//         //         resolve(true);
//         //       } else {
//         //         firebaseService.etfHistory.set(newEtfHistoryDaily);
//         //         firebaseService.etfStatsNewDate.set(true);
//         //         localStorage.setItem(
//         //           'etfHistory',
//         //           JSON.stringify(newEtfHistoryDaily),
//         //         );

//         //         resolve(true);
//         //       }
//         //     },
//         //     error: (error) => {
//         //       console.log(error.message);
//         //       resolve(false);
//         //     },
//         //   });
//         // });

//         if (
//           coingeckoService.shouldFetchData() &&
//           tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStatsSingleAccount('portfolio5')
//             .pipe(concatMap(() => tokeninsightService.getCryptoRatings()))
//             .subscribe();
//         }

//         if (
//           coingeckoService.shouldFetchData() &&
//           !tokeninsightService.shouldFetchRating()
//         ) {
//           coingeckoService
//             .getCryptoStatsSingleAccount('portfolio5')
//             .pipe(
//               tap(() => {
//                 tokeninsightService.setCoinsRatingSinglePortfolio(
//                   JSON.parse(localStorage.getItem('coinsRating')),
//                 );
//               }),
//             )
//             .subscribe();
//         }

//         if (!coingeckoService.shouldFetchData()) {
//           if (localStorage.getItem('portfolio5Monthly')) {
//             coingeckoService.btcPrice.set(
//               JSON.parse(localStorage.getItem('btcPrice')),
//             );

//             coingeckoService.ethPrice.set(
//               JSON.parse(localStorage.getItem('ethPrice')),
//             );

//             coingeckoService.btc24hChange.set(
//               JSON.parse(localStorage.getItem('btcPriceChange24h')),
//             );

//             coingeckoService.eth24hChange.set(
//               JSON.parse(localStorage.getItem('ethPriceChange24h')),
//             );

//             coingeckoService.eurusd.set(
//               JSON.parse(localStorage.getItem('eurusd')),
//             );

//             coingeckoService.btcMarketD.set(
//               JSON.parse(localStorage.getItem('btcMarketD')),
//             );

//             coingeckoService.ethMarketD.set(
//               JSON.parse(localStorage.getItem('ethMarketD')),
//             );

//             portfolio5History.portfolioMonthly = JSON.parse(
//               localStorage.getItem('portfolio5Monthly'),
//             );

//             portfolio5.coins = JSON.parse(localStorage.getItem('coins5'));
//             portfolio5.portfolioStats = JSON.parse(
//               localStorage.getItem('portfolio5Stats'),
//             );

//             if (!tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.setCoinsRatingSinglePortfolio(
//                 JSON.parse(localStorage.getItem('coinsRating')),
//               );
//             }

//             if (tokeninsightService.shouldFetchRating()) {
//               tokeninsightService.getCryptoRatings().subscribe(() => {});
//             }
//           }
//         }

//         if (tokeninsightService.shouldFetchNews()) {
//           tokeninsightService
//             .getCryptoNews()
//             .pipe(
//               concatMap((data) => {
//                 console.log(
//                   'TOKENINISGHT OK',
//                   tokeninsightService.cryptoNews(),
//                 );

//                 return cryptopanicService.getUpdates();
//               }),
//             )
//             .subscribe({
//               next: (data) => {
//                 if (localStorage.getItem('cryptoNews')) {
//                   const prevNews = [
//                     ...tokeninsightService.prevNews1,
//                     ...cryptopanicService.prevNews2,
//                   ];

//                   const actualNews = [
//                     ...tokeninsightService.cryptoNews(),
//                     ...cryptopanicService.cryptopanicList(),
//                   ];
//                   // console.log('PREV NEWS', prevNews);
//                   // console.log('ACTUAL NEWS', actualNews);

//                   function sonoOggettiUguali(objA, objB) {
//                     return JSON.stringify(objA) === JSON.stringify(objB);
//                   }

//                   let addedNews2 = [];

//                   actualNews.forEach((item) => {
//                     let oggettoOrigine = prevNews.find((oggetto) => {
//                       return oggetto.title === item.title;
//                     });

//                     if (
//                       !oggettoOrigine ||
//                       !sonoOggettiUguali(oggettoOrigine, item)
//                     ) {
//                       addedNews2.push(item);
//                     }
//                   });

//                   tokeninsightService.addedNews.set(addedNews2);

//                   tokeninsightService.loadingNews$.next('ok News');

//                   // cryptopanicService.loading$.next('ok');

//                   console.log('ADDED NEWS', addedNews2);
//                 }
//               },

//               error: (error) => {
//                 console.error(error);
//               },
//             });
//         }

//         if (!tokeninsightService.shouldFetchNews()) {
//           tokeninsightService.addedNews.set([]);

//           tokeninsightService.cryptoNews.set(
//             JSON.parse(localStorage.getItem('cryptoNews')),
//           );

//           cryptopanicService.cryptopanicList.set(
//             JSON.parse(localStorage.getItem('cryptopanicNews')),
//           );

//           tokeninsightService.loadingNews$.next('ok News');

//           // cryptopanicService.loading$.next('ok');

//           // console.log('NEWS OK - localstorage', tokeninsightService.cryptoNews());
//         }

//         return Promise.resolve();
//       } else {
//         return Promise.resolve();
//       }
//     }
//   };
// }
