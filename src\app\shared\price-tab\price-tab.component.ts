import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { CoingeckoService } from 'src/app/core/services/http/coingecko.service';

@Component({
  selector: 'app-price-tab',
  templateUrl: './price-tab.component.html',
  styleUrls: ['./price-tab.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PriceTabComponent {
  @Input('portfolio') portfolio: any;
  protected eurusd = this.coingeckoService.eurusd;
  protected btcPrice = this.coingeckoService.btcPrice;
  protected ethPrice = this.coingeckoService.ethPrice;
  protected btc24hChange = this.coingeckoService.btc24hChange;
  protected eth24hChange = this.coingeckoService.eth24hChange;
  protected btcMarketD = this.coingeckoService.btcMarketD;
  protected ethMarketD = this.coingeckoService.ethMarketD;

  constructor(
    private coingeckoService: CoingeckoService,
    protected currentAccountService: CurrentAccountService,
  ) {
    // console.log('ADDED', this.addedNews());
    // this.newsTimer = setInterval(() => {
    //   this.selectedNews.update((prev) => {
    //     switch (prev) {
    //       case 0:
    //         return 1;
    //       case 1:
    //         return 2;
    //       case 2:
    //       default:
    //         return 0;
    //     }
    //   });
    // }, 5000);
  }

  // ngOnDestroy(): void {
  //   clearInterval(this.newsTimer);
  // }
}
