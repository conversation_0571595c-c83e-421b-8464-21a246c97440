// Binance

import { Injectable } from '@angular/core';
import { IPortfolioMonthly, IPortfolioYearly } from '../../interfaces/coins';
import { InflationService } from '../utils/inflation.service';

@Injectable({
  providedIn: 'root',
})
export class PortfolioBinanceFraHistory {
  constructor(private inflationService: InflationService) {
    this.portfolioMonthly.forEach((month) => {
      const formatDate = this.formatDate(month.date);
      month.formatDate = formatDate;
    });
  }

  public portfolioMonthly: IPortfolioMonthly[] = [
    {
      title: 'June 2023',
      date: '06/01/2023',
      formatDate: '',
      deposits: 1236.1,
      current: 1002.04,
      profit: -234.04,
      profitPerc: -1839,
    },
    {
      title: 'July 2023',
      date: '07/01/2023',
      formatDate: '',
      deposits: 1376.1,
      current: 1185.62,
      profit: -190.46,
      profitPerc: -13.84,
    },
    {
      title: 'August 2023',
      date: '08/01/2023',
      formatDate: '',
      deposits: 1376.1,
      current: 1161.37,
      profit: -214.71,
      profitPerc: -15.6,
    },
    {
      title: 'September 2023',
      date: '09/01/2023',
      formatDate: '',
      deposits: 1376.1,
      current: 1169.17,
      profit: -406.91,
      profitPerc: -25.82,
    },
    {
      title: 'October 2023',
      date: '10/01/2023',
      formatDate: '',
      deposits: 1376.1,
      current: 1254.83,
      profit: -321.25,
      profitPerc: -20.38,
    },
    {
      title: 'November 2023',
      date: '11/01/2023',
      formatDate: '',
      deposits: 1376.1,
      current: 1509.58,
      profit: -66.5,
      profitPerc: -4.22,
    },
    {
      title: 'December 2023',
      date: '12/01/2023',
      formatDate: '',
      deposits: 6210.39,
      current: 4090,
      profit: -2120.39,
      profitPerc: -34.14,
    },
    {
      title: 'January 2024',
      date: '01/01/2024',
      formatDate: '',
      deposits: 7183,
      current: 5647,
      profit: -1536,
      profitPerc: -21,
    },
    {
      title: 'February 2024',
      date: '02/01/2024',
      formatDate: '',
      deposits: 7537,
      current: 5719,
      profit: -1818,
      profitPerc: -24,
    },
    {
      title: 'March 2024',
      date: '03/01/2024',
      formatDate: '',
      deposits: 7567,
      current: 8254,
      profit: 687,
      profitPerc: 9,
    },
    {
      title: 'April 2024',
      date: '04/01/2024',
      formatDate: '',
      deposits: 8897,
      current: 10625,
      profit: 1728,
      profitPerc: 19,
    },
    {
      title: 'May 2024',
      date: '05/01/2024',
      formatDate: '',
      deposits: 9097,
      current: 8378,
      profit: -719,
      profitPerc: -8,
    },
    {
      title: 'June 2024',
      date: '06/01/2024',
      formatDate: '',
      deposits: 9287,
      current: 9924,
      profit: 637,
      profitPerc: 7,
    },
    {
      title: 'July 2024',
      date: '07/01/2024',
      formatDate: '',
      deposits: 9387,
      current: 8989,
      profit: -398,
      profitPerc: -4,
    },
    {
      title: 'August 2024',
      date: '08/01/2024',
      formatDate: '',
      deposits: 9487,
      current: 8614,
      profit: -873,
      profitPerc: -9,
    },
    {
      title: 'September 2024',
      date: '09/01/2024',
      formatDate: '',
      deposits: 9587,
      current: 7490,
      profit: -2097,
      profitPerc: -22,
    },
    {
      title: 'October 2024',
      date: '10/01/2024',
      formatDate: '',
      deposits: 9587,
      current: 8657,
      profit: -930,
      profitPerc: -10,
    },
    {
      title: 'November 2024',
      date: '11/01/2024',
      formatDate: '',
      deposits: 9687,
      current: 8814,
      profit: -873,
      profitPerc: -9,
    },
    {
      title: 'December 2024',
      date: '12/01/2024',
      formatDate: '',
      deposits: 9687,
      current: 15445,
      profit: 5758,
      profitPerc: 59,
    },
    {
      title: 'January 2025',
      date: '01/01/2025',
      formatDate: '',
      deposits: 10787,
      current: 14468,
      profit: 3681,
      profitPerc: 34,
    },
    {
      title: 'February 2025',
      date: '02/01/2025',
      formatDate: '',
      deposits: 11087,
      current: 14851,
      profit: 3764,
      profitPerc: 34,
    },
    {
      title: 'March 2025',
      date: '03/01/2025',
      formatDate: '',
      deposits: 11487,
      current: 10871,
      profit: -616,
      profitPerc: -5,
    },
    {
      title: 'April 2025',
      date: '04/01/2025',
      formatDate: '',
      deposits: 3164,
      current: 1073,
      profit: -2091,
      profitPerc: -66,
    },
    {
      title: 'May 2025',
      date: '05/01/2025',
      formatDate: '',
      deposits: 3164,
      current: 1076,
      profit: -2088,
      profitPerc: -66,
    },
    {
      title: 'June 2025',
      date: '06/01/2025',
      formatDate: '',
      deposits: 4064,
      current: 1857,
      profit: -2207,
      profitPerc: -54,
    },
    {
      title: 'July 2025',
      date: '07/01/2025',
      formatDate: '',
      deposits: 4064,
      current: 1679,
      profit: -2384,
      profitPerc: -59,
    },
    {
      title: 'August 2025',
      date: '08/01/2025',
      formatDate: '',
      deposits: 4064,
      current: 2115,
      profit: -1948,
      profitPerc: -48,
    },
    {
      title: 'September 2025',
      date: '09/01/2025',
      formatDate: '',
      deposits: 4064,
      current: 2488,
      profit: -1575,
      profitPerc: -39,
    },
  ];

  //Binance + crypto.com
  public portfolioYearly: IPortfolioYearly[] = [
    {
      year: 2021,
      start: 0,
      deposits: 502.04 + 3561.93,
      startAndDeposits: 502.04 + 3561.93,
      fees: -10.04 - 60.41,
      withdraw: 0,
      closedTrades: 0,
      openTrades: -502.04 - 222.93,
      end: 315 + 3339,
      profit: -187.04 - 222.93,
      taxes: 0,
      netProfit: -187.04 - 222.93,
      netProfitPerc: -10,
    },
    {
      year: 2022,
      start: 315 + 3339,
      deposits: 100 + 1464.11,
      startAndDeposits: 5218.11,
      fees: -2 - 42.39,
      withdraw: 0,
      closedTrades: 0,
      openTrades: -312 - 3452.5,
      end: 1498,
      profit: -3720.11,
      taxes: 0,
      netProfit: -3720.11,
      netProfitPerc: -72,
    },
    {
      year: 2023,
      start: 1498,
      deposits: 1544.04,
      startAndDeposits: 3042.04,
      fees: -32.42,
      withdraw: 0,
      closedTrades: 0,
      openTrades: 2572.54,
      end: 5647,
      profit: 2572.54,
      taxes: 0,
      netProfit: 2572.54,
      netProfitPerc: 84,
    },
    {
      year: 2024,
      start: 5647,
      deposits: 3766,
      startAndDeposits: 9413,
      fees: -70.4,
      withdraw: 0,
      closedTrades: 900,
      openTrades: 4084.6,
      end: 14468,
      profit: 5055,
      taxes: 0,
      netProfit: 5055,
      netProfitPerc: 53.7,
    },
  ];

  public getTotalInflation() {
    return this.inflationService.calculateEffectiveInflation(
      this.portfolioMonthly,
    );
  }

  formatDate(date: string) {
    let year: string | number = new Date(date)
      .getUTCFullYear()
      .toString()
      .substring(-2);
    let month = '' + (new Date(date).getMonth() + 1);
    let day = '' + new Date(date).getDate();

    year = date.startsWith('01/01') ? (+year + 1).toString() : year;

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return `${month}/${year}`;
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('date');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
