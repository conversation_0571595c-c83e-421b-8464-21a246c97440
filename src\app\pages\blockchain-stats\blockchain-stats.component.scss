.chart {
  height: 330px;
  margin-left: 1rem;
  margin-bottom: 2rem;
}

.crypto-list {
  padding: 0.5rem 1.3rem;
  margin-bottom: 0.5rem;
  overflow: hidden;

  .info {
    display: flex;
    width: 100%;
    // background-color: #005382;
    border-radius: 10px;
    // border: 1px solid gray;

    &-text {
      display: flex;
      justify-content: space-between;
      font-size: 1.6rem;
      width: 100%;
      // padding: 0 1.3rem;

      & .deposits {
        display: flex;
        justify-content: start;
        align-items: center;
        padding: 1rem 0;
        border-radius: 10px;
        width: auto;
        letter-spacing: 0.2px;
      }
    }

    & select {
      font-size: 1.6rem;
      padding: 1rem 0.5rem;
      border-radius: 10px;
      border: none;
      background-color: black;
      color: #fff;
      width: 122px;
      text-align: center;
      margin-right: 1rem;
      cursor: pointer;
    }
  }

  &-title {
    grid-row: 1/2;
    grid-column: 1/-1;
    align-self: center;
    font-size: 1.8rem;
    font-weight: 500;
    color: #fff;
    margin-bottom: 0.5rem;
  }

  &-buttons {
    display: -webkit-inline-box;
    cursor: pointer;
    margin-bottom: 0.5rem;
    overflow-x: auto;
    width: 109%;
    margin-left: -1.5rem;
    padding-left: 1.5rem;
    padding-right: 3rem;

    &-tvl,
    &-fees {
      color: #c4c4c4;
      background-color: #282828;
      width: auto;
      padding: 0.7rem 1.2rem;
      border-radius: 5px;
      margin-right: 0.5rem;
      font-size: 1.4rem;
      white-space: nowrap;
      // text-wrap: nowrap;

      &.selected {
        background-color: #4b4b4b;
        color: white;
      }
    }

    & .extra {
      width: 1rem;
    }
  }
}
.tabs {
  margin-bottom: 1rem;
}

@media (min-width: 900px) {
  .crypto-list,
  .chart {
    background-color: rgb(10, 10, 10);
    border-radius: 15px;
    padding-top: 1rem;

    margin: 0 12%;
  }
}

.crypto {
  display: flex;
  flex-direction: column;
}
