import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { CryptoInfoModule } from 'src/app/shared/crypto-list/portfolio/crypto-info/crypto-info.module';
import { CryptoListUserComponent } from './crypto-list-user.component';

@NgModule({
  declarations: [CryptoListUserComponent],
  imports: [CommonModule, PipesModule, CryptoInfoModule, FormsModule],
  exports: [CryptoListUserComponent],
})
export class CryptoListUserModule {}
