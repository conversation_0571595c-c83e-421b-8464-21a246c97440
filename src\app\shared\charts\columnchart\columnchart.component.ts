import {
  AfterViewInit,
  Component,
  computed,
  effect,
  viewChild,
} from '@angular/core';
import { ChartComponent } from 'ng-apexcharts';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-columnchart',
  templateUrl: './columnchart.component.html',
  styleUrls: ['./columnchart.component.scss'],
})
export class ColumnchartComponent implements AfterViewInit {
  protected portfolioHistory =
    this.currentAccountService.currentPortfolioHistory;

  protected portfolioMonthly = computed(() => {
    const history = this.currentAccountService
      .currentPortfolioHistory()
      .portfolioMonthly.slice(0);

    // console.log(
    //   'history',
    //   history
    //     .sort((a, b) => {
    //       return new Date(b.date).getTime() - new Date(a.date).getTime();
    //     })
    //     .map((history, i) => {
    //       if (
    //         i == 0 ||
    //         i ==
    //           this.currentAccountService.currentPortfolioHistory()
    //             .portfolioMonthly.length -
    //             1
    //       ) {
    //         return {
    //           ...history,
    //           monthlyProfit: 0,
    //           monthlyProfitPerc: (history.profit / history.deposits) * 100,
    //         };
    //       } else {
    //         return {
    //           ...history,
    //           monthlyProfit:
    //             +history.profit -
    //             this.currentAccountService
    //               .currentPortfolioHistory()
    //               .portfolioMonthly[i - 1].profit.toFixed(0),
    //           monthlyProfitPerc: (history.profit / history.deposits) * 100,
    //         };
    //       }
    //     })
    //     .map((history, i, arr) => {
    //       let profitPercCurrent = history.profit;

    //       return {
    //         ...history,
    //         monthlyProfitPerc:
    //           (profitPercCurrent - arr[i - 1]?.profit / arr[i - 1]?.current) /
    //             100 || 0,
    //       };
    //     }),
    // );

    return history
      .sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      })
      .map((curr, i) => {
        if (i == 0 || i == history.length - 1) return 0;
        else
          return {
            ...curr,
            monthlyProfit: +(curr.profit - history[i + 1].profit).toFixed(0),
          };
      })
      .map((curr, i, arr) => {
        let profitPercCurrent = curr.profit;

        return {
          ...curr,
          monthlyProfitPerc:
            (profitPercCurrent - arr[i - 1]?.profit / arr[i - 1]?.current) /
              100 || 0,
        };
      });
  });

  chart = viewChild<ChartComponent>('chart');
  public chartOptions: any;

  constructor(private currentAccountService: CurrentAccountService) {
    this.createColumnChart();

    // effect(() => {
    //   this.currentAccountService.currentPortfolioHistory().portfolioMonthly
    //     ? this.createColumnChart()
    //     : null;
    // });

    effect(() => {
      // console.log('current account', this.portfolioMonthly());

      this.portfolioMonthly();
      // this.chart().updateOptions(this.chartOptions);
      this.createColumnChart();
    });
  }

  createColumnChart() {
    this.chartOptions = {
      series: [
        // {
        //   name: 'Deposits',
        //   data: this.portfolioHistory.portfolioMonthly.map(
        //     (history) => history.deposits
        //   ),
        // },
        {
          name: 'Net Profit',
          data: this.portfolioHistory().portfolioMonthly.map((history, i) =>
            i == 0 || i == this.portfolioHistory().portfolioMonthly.length - 1
              ? 0
              : +(
                  history.profit -
                  this.portfolioHistory().portfolioMonthly[i - 1].profit
                ).toFixed(0),
          ),
        },
        // {
        //   name: 'Net Profit %',
        //   data: this.portfolioHistory().portfolioMonthly.map(
        //     (history) => history.profitPerc
        //   ),
        // },
      ],
      chart: {
        type: 'bar',
        height: 350,
        foreColor: '#fff',
        zoom: { autoScaleYaxis: true },
        toolbar: {
          show: false,
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          opacity: 0.2,
        },
        animations: {
          enabled: false,
        },
        fontFamily: 'Roboto, Arial, sans-serif',
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '100%',
          endingShape: 'rounded',
          dataLabels: {
            position: 'top',
          },
          colors: {
            ranges: [
              {
                from: -10000,
                to: 0,
                color: '#F15B46',
              },
              {
                from: 0,
                to: 100000,
                color: 'var(--green-profit)',
              },
            ],
          },
        },
      },

      dataLabels: {
        enabled: false,
        offsetY: -20,
        formatter: (value) => {
          return '€ ' + value;
        },
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent'],
      },
      xaxis: {
        type: 'datetime',
        categories: this.portfolioHistory().portfolioMonthly.map((history) => {
          let newDate = new Date(history.date);
          const year = newDate.getFullYear();
          const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
          const day = newDate.getDate().toString().padStart(2, '0');
          return `${year}/${month}/${day}`;
        }),
        // categories: [
        //   'Feb',
        //   'Mar',
        //   'Apr',
        //   'May',
        //   'Jun',
        //   'Jul',
        //   'Aug',
        //   'Sep',
        //   'Oct',
        // ],
        labels: {
          datetimeUTC: true,
          datetimeFormatter: {
            year: 'yyyy',
            month: '',
            // month: "MMM 'yy",
            // day: 'dd MMM',
            // hour: 'HH:mm',
          },
        },
      },
      yaxis: {
        labels: {
          offsetX: -15,
          formatter: (value) => {
            return '€ ' + value;
          },
        },
        forceNiceScale: true,
        formatter: (value) => {
          return '€ ' + value;
        },
      },
      fill: {
        opacity: 1,
      },
      grid: {
        show: true,
        borderColor: '#424754',
        padding: {
          bottom: 0,
          right: 20,
          left: 0,
        },
        xaxis: {
          type: 'datetime',
          forceNiceScale: true,
          lines: {
            show: false,
          },
          tooltip: {
            enabled: true,
          },
        },
      },
      tooltip: {
        // y: {
        //   formatter: function (val) {
        //     return '$ ' + val + ' thousands';
        //   },
        // },
        enabled: true,
        theme: 'dark',
      },
    };
  }

  ngAfterViewInit() {}
}
