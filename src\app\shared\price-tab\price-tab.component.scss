.title {
  font-size: 1.6rem;
  font-weight: 500;
  color: #f0efef;
  margin-bottom: 1.5rem;
}

.news {
  display: grid;
  grid-template-columns: 95% 5%;
  justify-content: space-between;
  padding: 0.5rem;
  overflow: hidden;

  & .text {
    display: flex;
    align-items: center;
    width: max-content;
    height: 30px;
    font-style: italic;
    font-weight: 400;
    font-size: 1.4rem;
    // animation: scroll-left 30s linear infinite;
    & .ellipsis {
      display: inline-block;
      max-width: 55%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 0.5rem;
    }
  }
  & .icon {
    font-size: 1.6rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }

  5% {
    transform: (translateX(0));
  }

  100% {
    transform: translateX(-100%);
  }
}

.home-h24-mobile,
.home-h24-desktop {
  display: flex;
  flex-direction: column;
  font-size: 1.2rem;
  background-color: var(--extra-bg);
  border-color: gray;
  padding: 1.5rem 1rem;
  border-radius: 10px;
  margin: 0rem 1rem 2rem 1rem;
  width: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 10px #000c;

  &-container {
    display: flex;
    align-content: center;
    width: 100%;
    justify-content: space-between;
  }

  & .home-h24-coin {
    display: grid;
    grid-template-columns: 29px auto auto;
    column-gap: 2px;
    justify-content: left;

    & .home-h24-coin-logo {
      grid-row: 1/3;
      grid-column: 1/2;
      justify-self: center;
      align-self: center;
      display: flex;
      align-self: center;
      width: 100%;
      padding: 0.3rem;
      padding-left: 0;

      & img {
        width: 100%;
        height: 100%;
      }
    }

    & .home-h24-coin-ticker {
      grid-row: 1/3;
      grid-column: 2/3;
      font-size: 1.2rem;
      justify-self: start;
      align-self: center;
      font-weight: 500;
      color: #b5b5b5;
    }

    & .home-h24-coin-24h {
      grid-row: 1/3;
      grid-column: 3/4;
      font-size: 1.2rem;
      margin-left: 4px;
      align-self: center;
      display: flex;
      flex-direction: row;
      font-weight: 500;

      &-icon {
        margin-right: 0.4rem;
        display: flex;
        align-items: center;
        font-size: 1.2rem;
      }
    }
  }
}

.summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  padding: 1rem;
  // border-bottom: 1px solid gray;

  & .btc,
  .eth {
    display: grid;
    grid-template-columns: 50% 50%;
    grid-template-rows: 50% 50%;
    width: 48%;
    padding: 0.7rem 0 0.7rem 0;

    & .logo {
      grid-row: 1/-1;
      align-self: center;
      display: flex;
      align-items: center;

      & img {
        width: 26px;
        margin-right: 0.7rem;
      }
    }

    & .ticker {
      font-size: 1.4rem;
    }

    & .dominance {
      color: #5b5b5b;
      font-size: 1.2rem;
    }

    & .price {
      display: flex;
      flex-direction: column;
      justify-self: end;
      align-self: center;
      grid-row: 1/-1;
      font-size: 1.4rem;

      & .change {
        display: flex;
        justify-content: end;
        align-items: center;
        font-size: 1.2rem;

        & i {
          margin-right: 0.5rem;
        }
      }
    }
  }

  & .btc {
    & .logo {
      color: #f7931a;
    }
  }
  & .eth {
    & .logo {
      color: #627eea;
    }
  }
}

@keyframes move {
  to {
    transform: translateX(-100%);
  }
}

@media screen and (min-width: 900px) {
  .home-h24-mobile {
    display: none;
    margin: 0 8%;
  }

  .summary {
    margin: 0 2%;
  }

  .home-h24-desktop {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(126px, 1fr));
    overflow: hidden;
    height: 50px;
    // justify-content: start;
    // animation: move 60s linear infinite;
    background-color: transparent;
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 0.5rem 0 0 0rem;
    // transform: translateX(50%);

    & .home-h24-coin {
      margin: 0;
      padding-left: 0.2rem;
      height: 40px;
      // max-width: 10rem;
    }
  }
}

@media screen and (max-width: 899px) {
  .home-h24-desktop {
    display: none;
    margin: 1% 8%;
  }
}

// @media (min-width: 1000px) {
//   .home-h24 {
//     margin: 1% 12%;
//   }
// }

@media screen and (min-width: 1200px) {
  .summary {
    // margin: 0% 12%;
  }

  .home-h24-desktop {
    // margin: 0% 12%;
    width: auto;
  }
}
