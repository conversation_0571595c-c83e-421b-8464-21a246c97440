import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DirectivesModule } from 'src/app/core/utils/directives.module';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { CryptoInfoModule } from '../crypto-info/crypto-info.module';
import { RatingComponent } from './rating.component';

@NgModule({
  declarations: [RatingComponent],
  imports: [
    CommonModule,
    PipesModule,
    FormsModule,
    CryptoInfoModule,
    DirectivesModule,
  ],
  exports: [RatingComponent],
})
export class RatingModule {}
