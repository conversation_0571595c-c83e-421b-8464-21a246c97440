import { Injectable, NgZone } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/compat/auth';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { USER_LIST, User } from '../../interfaces/user';
import { CurrentAccountService } from '../data/current-account.service';
import { PortfolioPAC } from '../data/pac.service';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private userData: any; // Save logged in user data
  public userData$ = new Subject<{ email: string } | null>();

  constructor(
    public afs: AngularFirestore,
    public afAuth: AngularFireAuth,
    public router: Router,
    public ngZone: NgZone, // NgZone service to remove outside scope warning
    private currentAccount: CurrentAccountService,
    private portfolioPac: PortfolioPAC,
  ) {
    /* Saving user data in localstorage when
        logged in and setting up null when logged out */
    // this.afAuth.authState.subscribe((user) => {
    //   if (user) {
    //     this.userData = user;
    //     localStorage.setItem('user', JSON.stringify(this.userData));
    //     this.userData$.next({ email: user.email });
    //     console.log('UTENTE LOGGATO OK LOCALSTORAGE');
    //   } else {
    //     localStorage.setItem('user', null);
    //     this.currentAccount.currentUser.set(null);
    //     this.router.navigate(['login']);
    //     // console.log('UTENTE NON LOGGATO - LOCALSTORAGE');
    //   }
    // });
  }

  // Sign in with email/password
  SignIn(username: string, password: string) {
    let email: string;

    if (USER_LIST.find((user) => user.username == username)) {
      email = USER_LIST.find((user) => user.username == username).email;
    } else {
      email = username;
    }

    return this.afAuth
      .signInWithEmailAndPassword(email, password)
      .then((result) => {
        this.SetUserData(result.user);
        this.currentAccount.currentUser.set(
          USER_LIST.find((user) => user.username == username),
        );

        // this.userData = result.user;
        this.userData$.next({ email: result.user.email });

        return 'LOGIN OK';

        // this.ngZone.run(() => {
        //   this.router.navigate(['']);
        // });
      })
      .catch((error) => {
        // console.log('ERROR', error.message);
        // window.alert(error.message);
        // this.userData$.next(null);
        return 'ERROR';
      });
  }

  // Sign up with email/password
  // SignUp(email, password) {
  //   return this.afAuth
  //     .createUserWithEmailAndPassword(email, password)
  //     .then((result) => {
  //       /* Call the SendVerificaitonMail() function when new user sign
  //               up and returns promise */
  //       this.SendVerificationMail();
  //       this.SetUserData(result.user);
  //     })
  //     .catch((error) => {
  //       window.alert(error.message);
  //     });
  // }

  // // Send email verfificaiton when new user sign up
  // SendVerificationMail() {
  //   return this.afAuth.currentUser
  //     .then((user) => {
  //       return user.sendEmailVerification();
  //     })
  //     .then(() => {
  //       this.router.navigate(['login']);
  //     });
  // }

  // Reset Forggot password
  ForgotPassword(passwordResetEmail) {
    return this.afAuth
      .sendPasswordResetEmail(passwordResetEmail)
      .then(() => {
        window.alert('Password reset email sent, check your inbox.');
      })
      .catch((error) => {
        window.alert(error);
      });
  }

  // Returns true when user is looged in
  get isLoggedIn(): boolean {
    const user = JSON.parse(localStorage.getItem('user'));
    return user !== null ? true : false;
  }

  // Auth logic to run auth providers
  // AuthLogin(provider) {
  //   return this.afAuth
  //     .signInWithPopup(provider)
  //     .then((result) => {
  //       this.ngZone.run(() => {
  //         this.router.navigate(['user-panel']);
  //       });
  //       this.SetUserData(result.user);
  //     })
  //     .catch((error) => {
  //       window.alert(error);
  //     });
  // }

  /* Setting up user data when sign in with username/password,
    sign up with username/password and sign in with social auth
    provider in Firestore database using AngularFirestore + AngularFirestoreDocument service */
  SetUserData(user) {
    // const userRef: AngularFirestoreDocument<any> = this.afs.doc(
    //   `users/${user.uid}`
    // );
    const userData: User = {
      uid: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      emailVerified: user.emailVerified,
      loginTime: new Date(),
    };

    localStorage.setItem('user', JSON.stringify(userData));

    // return userRef.set(userData, {
    //   merge: true,
    // });
  }

  // Sign out
  SignOut() {
    return this.afAuth.signOut().then(() => {
      this.router.navigate(['login']);
      if (this.currentAccount.currentUser().username === 'pac') {
        this.portfolioPac.coins = [];
        this.portfolioPac.coinsTest = [];
        // this.portfolioPac.portfolioStats = {
        //   ...this.portfolioPac.portfolioStats,
        //   current: null,
        //   profits: null,
        //   profitsPerc: null,
        //   totalDeposits: null,
        // };
      }

      console.log('PAC', this.portfolioPac);
      localStorage.setItem('user', null);

      this.currentAccount.currentUser.set(null);

      this.userData$.next(null);

      this.clearLocalStorage();
    });
  }

  clearLocalStorage() {
    const keysToKeep = ['cryptoNews', 'dateNews', 'cryptopanicNews', 'user'];
    const localStorageKeys = Object.keys(localStorage);

    localStorageKeys.forEach((key) => {
      if (!keysToKeep.includes(key)) {
        localStorage.removeItem(key);
      }
    });
  }

  loginTimeExpired() {
    const storedDate = JSON.parse(localStorage.getItem('user'))?.loginTime
      ? JSON.parse(localStorage.getItem('user')).loginTime
      : null;

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 10080) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
