.container {
  margin: 1rem;
  margin-bottom: 2rem;
  overflow: hidden;
  & .phase {
    padding: 1rem;
    background-color: var(--card-bg);
    border-radius: 10px;
    font-size: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    letter-spacing: 1px;
    margin-bottom: 2rem;
  }
  & .grid {
    display: grid;
    grid-template-columns: 30px 100px auto auto;
    grid-template-rows: 30px 23px 23px 23px 23px;
    font-size: 1.4rem;
    margin-bottom: 2rem;
    border: 1px solid gray;
    padding: 0.9rem 1.4rem;
    border-radius: 10px;

    & .header {
      // background: #4b4b4b;
      display: flex;
      align-items: center;
      justify-content: start;
      height: 100%;
      color: gray;

      &-7d,
      &-30d {
        justify-self: end;
        align-self: center;
      }
    }

    & .subheader {
      color: gray;
      grid-column: 1/3;
      justify-self: start;
      align-self: center;
    }

    &-7d-btc {
      grid-column: 3/4;
      grid-row: 2/3;
      justify-self: end;
      align-self: center;
    }
    &-30d-btc {
      grid-column: 4/5;
      grid-row: 2/3;
      justify-self: end;
      align-self: center;
    }

    &-7d-eth {
      grid-column: 3/4;
      grid-row: 3/4;
      justify-self: end;
      align-self: center;
    }
    &-30d-eth {
      grid-column: 4/5;
      grid-row: 3/4;
      justify-self: end;
      align-self: center;
    }

    &-7d-top {
      grid-column: 3/4;
      grid-row: 3/4;
      justify-self: end;
      align-self: center;
    }
    &-30d-top {
      grid-column: 4/5;
      grid-row: 3/4;
      justify-self: end;
      align-self: center;
    }

    &-7d {
      grid-column: 3/4;
      grid-row: 4/5;
      justify-self: end;
      align-self: center;
    }

    &-30d {
      grid-column: 4/5;
      grid-row: 4/5;
      justify-self: end;
      align-self: center;
    }

    &-7d-number {
      grid-column: 3/4;
      grid-row: 5/6;
      justify-self: end;
      align-self: center;
    }

    &-30d-number {
      grid-column: 4/5;
      grid-row: 5/6;
      justify-self: end;
      align-self: center;
    }

    &.eth {
      grid-template-rows: 32px 23px 23px 23px;

      & .header-logo {
        width: 100%;
        margin-left: -0.3rem;
        & img {
          width: 100%;
          scale: 0.8;
        }
      }

      & .header-name {
        justify-content: start;
        align-self: center;
        font-weight: 500;
      }

      & .header-7d,
      & .header-30d {
        font-weight: 500;
        color: gray;
      }
    }

    &.top {
      & .header-name {
        grid-column: 1/3;
        align-self: center;
        font-weight: 500;
      }

      & .header-7d,
      & .header-30d {
        font-weight: 500;
        color: gray;
      }
    }
  }

  & .crypto-list-buttons {
    display: -webkit-inline-box;
    cursor: pointer;
    margin-bottom: 0.5rem;
    overflow-x: auto;
    width: 109%;
    margin-left: -1.5rem;
    padding-left: 1.5rem;
    padding-right: 3rem;
    margin-bottom: 2rem;
    margin-top: 1.5rem;

    &-tvl,
    &-fees {
      color: #c4c4c4;
      background-color: #282828;
      width: auto;
      padding: 0.7rem 1.2rem;
      border-radius: 5px;
      margin-right: 0.5rem;
      font-size: 1.4rem;
      white-space: nowrap;
      // text-wrap: nowrap;

      &.selected {
        background-color: #4b4b4b;
        color: white;
      }
    }

    & .extra {
      width: 1rem;
    }
  }

  .crypto-list {
    margin-bottom: 0.5rem;
    margin-top: 0;
    overflow: hidden;

    .crypto-list-header {
      display: grid;
      grid-template-rows: auto;
      grid-template-columns: 34px 40% 30% auto;
      width: 100%;
      font-size: 1.4rem;
      color: #5b5b5b;
      // border-bottom: 1px solid #282828;

      &-name,
      &-price,
      &-profit,
      &-tvl {
        align-self: center;
      }

      &-name {
        grid-column: 2/3;
        padding-left: 0.7rem;
      }

      &-price {
        grid-column: 4/5;
        justify-self: end;
        margin-right: 1rem;
      }

      &-category {
        align-self: start;
        justify-self: center;
      }

      &-tvl {
        grid-column: 3/4;
        justify-self: end;
        margin-right: 1rem;
        cursor: pointer;
      }
    }

    .crypto-list {
      padding: 0.5rem 1.3rem;
      margin-bottom: 0.5rem;
      overflow: hidden;

      .info {
        display: flex;
        width: 100%;
        // background-color: #005382;
        border-radius: 10px;
        // border: 1px solid gray;

        &-text {
          display: flex;
          justify-content: space-between;
          font-size: 1.6rem;
          width: 100%;
          // padding: 0 1.3rem;

          & .deposits {
            display: flex;
            justify-content: start;
            align-items: center;
            padding: 1rem 0;
            border-radius: 10px;
            width: auto;
            letter-spacing: 0.2px;
          }
        }

        & select {
          font-size: 1.6rem;
          padding: 1rem 0.5rem;
          border-radius: 10px;
          border: none;
          background-color: black;
          color: #fff;
          width: 122px;
          text-align: center;
          margin-right: 1rem;
          cursor: pointer;
        }
      }

      &-title {
        grid-row: 1/2;
        grid-column: 1/-1;
        align-self: center;
        font-size: 1.8rem;
        font-weight: 500;
        color: #fff;
        margin-bottom: 0.5rem;
      }

      &-buttons {
        display: -webkit-inline-box;
        cursor: pointer;
        margin-bottom: 0.5rem;
        overflow-x: auto;
        width: 109%;
        margin-left: -1.5rem;
        padding-left: 1.5rem;
        padding-right: 3rem;

        &-tvl,
        &-fees {
          color: #c4c4c4;
          background-color: #282828;
          width: auto;
          padding: 0.7rem 1.2rem;
          border-radius: 5px;
          margin-right: 0.5rem;
          font-size: 1.4rem;
          white-space: nowrap;
          // text-wrap: nowrap;

          &.selected {
            background-color: #4b4b4b;
            color: white;
          }
        }

        & .extra {
          width: 1rem;
        }
      }
    }
    .crypto {
      & .crypto-list-table {
        display: grid;
        grid-template-rows: 17px 17px;
        grid-template-columns: 34px 40% 30% auto;
        margin-top: 1.2rem;
        width: 100%;
        row-gap: 0.3rem;
        // border-bottom: 1px solid #1b1b1b;
        padding-bottom: 0.6rem;
        // border-radius: 15px;
        // background-color: rgb(17, 17, 17);
        // padding: 1rem;

        &-logo {
          grid-row: 1/3;
          grid-column: 1/2;
          justify-self: center;
          align-self: center;
          width: 100%;
          // height: 100%;
          margin-top: 0.3rem;
          margin-left: 0.2rem;
          cursor: pointer;
          overflow: hidden;

          & img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }

        &-name {
          grid-row: 1/2;
          grid-column: 2/3;
          justify-self: start;
          align-self: center;
          font-size: 1.6rem;
          padding-left: 0.7rem;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 100%;
          cursor: pointer;
        }

        &-ticker {
          grid-row: 2/3;
          grid-column: 2/3;
          justify-self: start;
          align-self: center;
          font-size: 1.4rem;
          padding-left: 0.7rem;
          color: #5b5b5b;
          cursor: pointer;
        }

        &-tvl {
          grid-row: 1/2;
          grid-column: 3/4;
          align-self: center;
          justify-self: end;
          font-size: 1.4rem;
        }

        &-price {
          grid-row: 1/2;
          grid-column: 4/5;
          justify-self: end;
          align-self: center;
          font-size: 1.4rem;
        }

        &-category {
          grid-row: 1/3;
          grid-column: 3/4;
          align-self: start;
          justify-self: center;
          font-size: 1.4rem;
          text-align: center;
        }

        &-gain {
          grid-row: 1/2;
          grid-column: 5/6;
          justify-self: end;
          align-self: center;
          font-size: 1.6rem;
        }

        &-gainPercent7d {
          grid-row: 1/2;
          grid-column: 3/4;
          align-self: center;
          justify-self: end;
          display: flex;
          flex-direction: row;
          background-color: rgba(0, 100, 0, 0.6);
          color: #04dc00;
          border-radius: 15px;
          padding: 0.15rem 0.8rem;

          &-icon {
            margin-right: 0.45rem;

            & i {
              padding-top: 0.25rem;
              font-size: 1rem;
            }
          }

          &-number {
            font-size: 1.2rem;
          }
        }

        &-gainPercent30d {
          grid-row: 1/2;
          grid-column: 4/5;
          align-self: center;
          justify-self: end;
          display: flex;
          flex-direction: row;
          background-color: rgba(0, 100, 0, 0.6);
          color: #04dc00;
          border-radius: 15px;
          padding: 0.15rem 0.8rem;

          &-icon {
            margin-right: 0.45rem;

            & i {
              padding-top: 0.25rem;
              font-size: 1rem;
            }
          }

          &-number {
            font-size: 1.2rem;
          }
        }
      }
    }
  }
}

@media (min-width: 900px) {
  .container {
    margin: 5% 20%;
  }
}
