<div class="crypto-list-header">
  <div class="crypto-list-header-name">Blockchain</div>
  <div class="crypto-list-header-tvl">
    Volume
    @if (currentSorting == 'deposits') {
      <i
        class="fa-solid fa-chevron-down"
      ></i>
    }
  </div>
  <div class="crypto-list-header-price">Volume %</div>
</div>

@if(date == '30d') {
  @for (chain of chainsVolume; track chain) {
    <div class="crypto">
      @if(!!chain.totalVolume30d && chain.totalVolume30d > 9999 &&
        !!chain.tokenSymbol){
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="chain?.img" />
          </div>
          <div class="crypto-list-table-name">
            {{ chain?.name }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ chain?.tokenSymbol }}
          </div>
          <div class="crypto-list-table-tvl">
            {{ chain?.totalVolume30d | shortNumber }}
          </div>
          <div class="crypto-list-table-price">
            <div>
              {{ (chain?.totalVolume30d / chainsTotalVolume) * 100 | profitsPerc }}
            </div>
          </div>
        </div>
      }
    </div>
  }
  } @if(date == '7d') {
  @for (chain of chainsVolume; track chain) {
    <div class="crypto">
      @if(!!chain.totalVolume7d && chain.totalVolume7d > 9999 &&
        !!chain.tokenSymbol){
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="chain?.img" />
          </div>
          <div class="crypto-list-table-name">
            {{ chain?.name }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ chain?.tokenSymbol }}
          </div>
          <div class="crypto-list-table-tvl">
            {{ chain?.totalVolume7d | shortNumber }}
          </div>
          <div class="crypto-list-table-price">
            <div>
              {{ (chain?.totalVolume7d / chainsTotalVolume) * 100 | profitsPerc }}
            </div>
          </div>
        </div>
      }
    </div>
  }
}
