:host {
  width: 48%;
}
.container {
  // background: #001515;
  padding: 1.5rem 1rem;
  margin: 1rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.8);
  background-color: var(--extra-bg);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 1.6rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #f0efef;
}

.etf-loader {
  height: 43px;
  background-color: #001515;
  border-radius: 12px;
  margin: 1rem;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.8);
}

.etf {
  display: grid;
  grid-template-columns: 42% 53% 5%;
  position: relative;
  padding: 1rem 0;

  // &:nth-child(2) {
  //   border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  // }

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
  }

  & .notification {
    position: absolute;
    top: -0.6rem;
    right: -0.6rem;
    background-color: darkorange;
    border-radius: 50%;
    font-size: 1.4rem;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .bitcoinEtfTitle {
    display: flex;
    font-size: 1.4rem;
    font-weight: 500;
    // color: #f7931a;
    text-align: center;
    align-self: flex-start;

    & .logo {
      align-self: center;
      display: flex;
      align-items: center;

      & img {
        width: 26px;
        margin-right: 0.7rem;
      }
    }

    .price {
      display: flex;
      align-items: center;
      color: #b5b5b5;

      .change {
        margin-left: 1rem;
        font-size: 1.2rem;
      }
    }

    &.eth {
      // color: #627eea;

      .price {
        margin-left: 1.8rem;
      }
    }

    & .date {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #cececece;
      font-size: 1.2rem;
    }
  }
  .bitcoinEtf-info {
    display: grid;
    grid-template-columns: auto auto;
    font-size: 1.4rem;
    // border-bottom: 1px solid gray;
    letter-spacing: 0.5px;

    &-totalBtc,
    &-totalInflow,
    &-totalInflowDaily {
      display: flex;
      align-items: center;
      justify-self: center;

      & .title {
        color: #cececece;
        margin-right: 0.5rem;
      }

      & .value {
        color: #b5b5b5;
      }
    }

    // &-totalInflow {
    //   justify-self: center;
    // }

    // &-totalInflowDaily {
    //   justify-self: end;
    // }

    & i {
      font-size: 1rem;
      color: rgb(84, 84, 84);
      margin-left: 0.5rem;
    }
  }

  & .moreInfo {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.4rem;
    cursor: pointer;
    color: #b5b5b5;

    & i {
      font-size: 1.6rem;
    }
  }

  & .data {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
    // background-color: #002020;
    background-color: var(--extra-bg);
    font-size: 1.4rem;
    padding: 0.5rem 0;
    margin: 1rem 1rem 0 1rem;
    // font-weight: 500;
    // border: 1px solid gray;

    & .header {
      grid-row: 1/2;
      justify-self: center;
      align-self: center;
      display: flex;
      justify-content: center;
      text-align: center;
      align-items: center;
      padding: 0.5rem;
      // border: 1px solid gray;
      font-weight: 500;
      width: 100%;
      height: 100%;
      background-color: #4b4b4b;

      &.weeklyTitle {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }

      &.totalBtcTitle {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    }

    @mixin base {
      justify-self: center;
      align-self: center;
      display: flex;
      justify-content: center;
      text-align: center;
      align-items: center;
      padding: 0.8rem;
      // border: 1px solid gray;
      width: 100%;
      height: 100%;
      font-size: 1.4rem;
    }
    & .week {
      @include base;
      grid-column: 1/2;
    }

    & .weeklyInflow {
      @include base;
      grid-column: 2/3;
    }

    & .totalInflow {
      @include base;
      grid-column: 3/4;
    }

    & .totalBtc {
      @include base;
      grid-column: 4/5;
    }
  }

  & .data-chart {
    width: 100%;
    min-height: 244px;
    padding: 0 1rem;
    margin-top: 1rem;
    position: relative;
    grid-column: 1/-1;

    img {
      border-radius: 15px;
    }

    & .crypto-list-buttons {
      display: flex;
      cursor: pointer;
      align-items: center;
      width: 100%;
      margin-top: 0.5rem;

      .date {
        text-align: end;
        flex-grow: 1;
        font-size: 1.2rem;
        color: #cececece;
      }

      &-btc,
      &-dollar {
        color: #c4c4c4;
        background-color: #282828;
        width: auto;
        padding: 0.7rem 1.2rem;
        border-radius: 5px;
        margin-right: 0.5rem;
        font-size: 1.4rem;
        white-space: nowrap;
        // text-wrap: nowrap
        min-width: 50px;
        text-align: center;

        &.selected {
          background-color: #4b4b4b;
          color: white;
        }
      }
    }
  }
}
@media (min-width: 900px) {
  .container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 0;
    width: 100%;
    padding-bottom: 2rem;

    .etf {
      margin: 0;
      &:first-child {
        margin-bottom: 0;
      }
    }

    .bitcoinEtf {
      margin: 0 2%;
    }
  }
}

@media (min-width: 1200px) {
  .container {
    width: 100%;
    gap: unset;

    .etf {
      margin: 0;
      width: 100%;
      &:first-child {
        margin-bottom: 1rem;
      }
    }

    .bitcoinEtf {
      width: 36vw;
    }
  }
}
