<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto</div>
  <div class="crypto-list-header-price">Price</div>
  <div class="crypto-list-header-profit">Change 24h</div>
</div>

@for (coin of portfolio.sortH24; track coin) {
  <div class="crypto">
    <div class="crypto-list-table">
      <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
        <img src="{{ coin?.logo }}" />
      </div>
      <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
        {{ coin?.ticker }}
      </div>
      <div class="crypto-list-table-name" (click)="onInfoClick($event)">
        {{ coin?.name }}
      </div>
      <div class="crypto-list-table-price">{{ coin?.price | deposits }}</div>
      <div
        class="crypto-list-table-gainPercent"
      [ngStyle]="{
        backgroundColor:
          coin?.change24h > 0 ? 'rgba(0, 100, 0, 0.4)' : 'rgba(100, 0, 0, 0.4)'
      }"
        >
        <div class="crypto-list-table-gainPercent-icon">
          @if (coin?.change24h > 0) {
            <i class="fa-solid fa-caret-up"></i>
          }
          @if (coin?.change24h < 0) {
            <i
              class="fa-solid fa-caret-down"
          [ngStyle]="{
            color: 'red'
          }"
            ></i>
          }
        </div>
        <div
          class="crypto-list-table-gainPercent-number"
        [ngStyle]="{
          color: coin?.change24h > 0 ? '#04dc00' : 'red'
        }"
          >
          {{ coin?.change24h | profitsPerc }}
        </div>
      </div>
    </div>
    <app-crypto-info
      [coin]="coin"
      [showInfo]="showInfo"
      [currentCoin]="currentCoin"
    ></app-crypto-info>
  </div>
}
