.crypto-list {
  padding: 0.5rem 1.3rem;
  margin-bottom: 0.5rem;
  overflow: hidden;

  &-title {
    grid-row: 1/2;
    grid-column: 1/-1;
    align-self: center;
    font-size: 1.8rem;
    font-weight: 500;
    color: #fff;
    margin-bottom: 0.5rem;
  }

  &-buttons {
    display: -webkit-inline-box;
    cursor: pointer;
    margin-bottom: 0.5rem;
    overflow-x: auto;
    width: 109%;
    margin-left: -1.5rem;
    padding-left: 1.5rem;
    padding-right: 3rem;

    @media (max-width: 768px) {
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }

    &-profits,
    &-current,
    &-deposits,
    &-ath,
    &-h24,
    &-rank,
    &-quantity,
    &-category,
    &-rating,
    &-ratingGroup,
    &-info,
    &-earn {
      color: #c4c4c4;
      background-color: #282828;
      width: auto;
      padding: 0.7rem 1.2rem;
      border-radius: 5px;
      margin-right: 0.5rem;
      font-size: 1.4rem;
      white-space: nowrap;
      // text-wrap: nowrap;

      &.selected {
        background-color: #4b4b4b;
        color: white;
      }
    }

    & .extra {
      width: 1rem;
    }
  }
}

@media (min-width: 900px) {
  .crypto-list {
    background-color: rgb(10, 10, 10);
    border-radius: 15px;
    padding-top: 1rem;
  }
}
