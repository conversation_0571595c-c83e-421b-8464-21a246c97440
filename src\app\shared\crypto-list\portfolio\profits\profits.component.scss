.crypto-list-header {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 32px 24% 20% 23.5% auto;
  width: 100%;
  margin: 1rem 0 0rem 0;
  font-size: 1.4rem;
  color: #5b5b5b;
  border-bottom: 1px solid #121212;
  padding-bottom: 0.8rem;

  &-name,
  &-price,
  &-profit,
  &-deposit,
  &-name {
    grid-column: 2/3;
    padding-left: 0.7rem;
  }

  &-price {
    grid-column: 4/5;
    text-align: end;
    justify-self: end;
  }

  &-deposit {
    grid-column: 3/4;
    justify-self: end;
    text-align: end;
    cursor: pointer;
  }

  &-profit {
    grid-column: 5/6;
    justify-self: end;
    text-align: end;
    cursor: pointer;
  }

  i {
    color: #fff;
    margin-left: 0.2rem;
  }
}

.crypto-list-table {
  display: grid;
  grid-template-rows: 17px 17px;
  grid-template-columns: 32px 24% 20% 23.5% auto;
  // margin-top: 1.2rem;
  width: 100%;
  row-gap: 0.2rem;
  // border-bottom: 1px solid #1b1b1b;
  // padding-bottom: 0.6rem;
  padding: 0.8rem 0;
  // border-radius: 15px;
  // background-color: rgb(17, 17, 17);
  // padding: 1rem;
  border-bottom: 1px solid #121212;

  &.last {
    border-bottom: none;
  }

  &-logo {
    grid-row: 1/3;
    grid-column: 1/2;
    justify-self: center;
    align-self: center;
    width: 100%;
    // height: 100%;
    margin-top: 0.1rem;
    margin-left: 0.2rem;
    cursor: pointer;

    & img {
      width: 100%;
      height: 100%;
    }
  }

  &-name {
    grid-row: 2/3;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.2rem;
    padding-left: 0.7rem;
    color: #5b5b5b;
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
  }

  &-ticker {
    grid-row: 1/2;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.6rem;
    padding-left: 0.7rem;
    cursor: pointer;
  }

  &-deposit {
    grid-row: 2/3;
    grid-column: 3/4;
    align-self: center;
    justify-self: end;
    font-size: 1.4rem;
    color: #5b5b5b;
  }

  &-current {
    grid-row: 1/2;
    grid-column: 3/4;
    align-self: center;
    justify-self: end;
    font-size: 1.4rem;
  }

  &-price {
    grid-row: 1/2;
    grid-column: 4/5;
    justify-self: end;
    align-self: center;
    font-size: 1.4rem;
  }

  &-avgPrice {
    grid-row: 2/3;
    grid-column: 4/5;
    justify-self: end;
    align-self: center;
    font-size: 1.4rem;
    // color: #008fe4;
    color: #5b5b5b;
  }

  &-gain {
    grid-row: 1/2;
    grid-column: 5/6;
    justify-self: end;
    align-self: center;
    font-size: 1.6rem;
  }

  &-gainPercent {
    grid-row: 2/3;
    grid-column: 5/6;
    align-self: end;
    justify-self: end;
    display: flex;
    flex-direction: row;
    background-color: rgba(0, 100, 0, 0.6);
    color: #04dc00;
    border-radius: 15px;
    padding: 0.15rem 0.8rem;

    &-icon {
      margin-right: 0.45rem;

      & i {
        padding-top: 0.25rem;
        font-size: 1rem;
      }
    }

    &-number {
      font-size: 1.2rem;
    }
  }
}

.crypto {
  display: flex;
  flex-direction: column;
}
