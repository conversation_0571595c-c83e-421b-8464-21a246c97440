<!-- Mobile START -->
<!-- <div class="news">
<div class="text">
  <b>NEWS:</b>
  <div class="ellipsis">{{ " " + newsList$()[0]?.title }}</div>
</div>

<div class="icon"><i class="fa-solid fa-angles-down"></i></div>
</div> -->
<div class="home-h24-mobile">
  <span class="title">Top Movers</span>
  <div class="home-h24-mobile-container">
    @for (coin of portfolio?.sortH24Top3; track coin) {
      <div class="home-h24-coin">
        <div class="home-h24-coin-logo">
          <img src="{{ coin?.logo }}" alt="" />
        </div>
        <div class="home-h24-coin-ticker">{{ coin?.ticker }}</div>
        <div
          class="home-h24-coin-24h"
          [ngStyle]="{ color: coin?.change24h > 0 ? '#04dc00' : 'red' }"
          >
          <div class="home-h24-coin-24h-icon">
            @if (coin?.change24h > 0) {
              <i class="fa-solid fa-caret-up"></i>
            }
            <div class="home-balance-gain-icon-down">
              @if (coin?.change24h < 0) {
                <i
                  class="fa-solid fa-caret-down"
              [ngStyle]="{
                color: 'red'
              }"
                ></i>
              }
            </div>
          </div>
          {{ coin?.change24h | profitsPerc }}
        </div>
      </div>
    }
  </div>
</div>
<!-- Mobile END -->

<!-- Desktop START -->
<div class="home-h24-desktop">
  @for (coin of portfolio.sortH24; track coin) {
    <div class="home-h24-coin">
      <div class="home-h24-coin-logo">
        <img src="{{ coin?.logo }}" alt="" />
      </div>
      <div class="home-h24-coin-ticker">{{ coin?.ticker }}</div>
      <div
        class="home-h24-coin-24h"
        [ngStyle]="{ color: coin?.change24h > 0 ? '#04dc00' : 'red' }"
        >
        <div class="home-h24-coin-24h-icon">
          @if (coin?.change24h > 0) {
            <i class="fa-solid fa-caret-up"></i>
          }
          <div class="home-balance-gain-icon-down">
            @if (coin?.change24h < 0) {
              <i
                class="fa-solid fa-caret-down"
            [ngStyle]="{
              color: 'red'
            }"
              ></i>
            }
          </div>
        </div>
        {{ coin?.change24h | profitsPerc }}
      </div>
    </div>
  }
</div>
<!-- Desktop END -->

<!-- BTC / ETH Market Dominance -->
<!-- <div
class="summary"
  [ngStyle]="{
    'padding-bottom':
      currentAccountService.currentUser()?.username === 'elisa' ? 0 : null
  }"
>
<div class="btc">
  <div class="logo">
    <img src="../../../assets/img/logo/btc.png" alt="" />
    <div class="ticker">
      Bitcoin
      <div class="dominance">D: {{ btcMarketD() | profitsPerc }}</div>
    </div>
  </div>

  <div class="price">
    {{ btcPrice() / eurusd() | $depositsNoDecimal }}
    <div
      class="change"
      [ngStyle]="{ color: btc24hChange() > 0 ? '#04dc00' : 'red' }"
      >
      @if(btc24hChange()>0) { <i class="fa-solid fa-caret-up"></i>} @else {<i
      class="fa-solid fa-caret-down"
      ></i
      >}
      {{ btc24hChange() | profitsPerc }}
    </div>
  </div>
</div>

<div class="eth">
  <div class="logo">
    <img src="../../../assets/img/logo/eth.png" alt="" />
    <div class="ticker">
      Ethereum
      <div class="dominance">D:{{ ethMarketD() | profitsPerc }}</div>
    </div>
  </div>
  <div class="price">
    {{ ethPrice() / eurusd() | $depositsNoDecimal }}
    <div
      class="change"
      [ngStyle]="{ color: eth24hChange() > 0 ? '#04dc00' : 'red' }"
      >
      @if(eth24hChange()>0) { <i class="fa-solid fa-caret-up"></i>} @else {<i
      class="fa-solid fa-caret-down"
      ></i
      >}
      {{ eth24hChange() | profitsPerc }}
    </div>
  </div>
</div>
</div> -->
