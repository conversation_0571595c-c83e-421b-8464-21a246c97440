import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from '../../shared/loader-spinner/loader-spinner.module';
import { CryptoVolumesRoutingModule } from './crypto-volumes-routing.module';
import { CryptoVolumesComponent } from './crypto-volumes.component';

@NgModule({
  declarations: [CryptoVolumesComponent],
  exports: [CryptoVolumesComponent],
  imports: [
    CommonModule,
    CryptoVolumesRoutingModule,
    PipesModule,
    LoaderSpinnerModule,
  ],
})
export class CryptoVolumesModule {}
