import { formatCurrency } from '@angular/common';
import { Component, Input, effect } from '@angular/core';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-quantity',
  templateUrl: './quantity.component.html',
  styleUrls: ['./quantity.component.scss'],
})
export class QuantityComponent {
  @Input('portfolio') portfolio: any;
  protected showInfo: boolean = false;
  protected priceInput: string;
  protected priceInputNumber: number | string;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected currentCoin: string = '';
  protected profits: string;

  constructor(private currentAccountService: CurrentAccountService) {
    effect(() => {
      this.currentAccountService.currentAccount()
        ? (this.showInfo = false)
        : null;
    });
  }

  onInfoClick(value) {
    if (!this.currentCoin) this.showInfo = true;

    let currentCoinValue = (
      value.target.parentElement.children[1]
        ? value.target.parentElement.children[1].textContent
        : value.target.parentElement.parentElement.children[1].textContent
    ).trim();

    this.currentCoin == currentCoinValue
      ? (this.showInfo = !this.showInfo)
      : (this.showInfo = true);

    this.currentCoin = currentCoinValue;

    this.depositsInput = '';
    this.priceInput = '';
    this.priceInputNumber = '';
  }

  onInputChange(element) {
    let elementNumber: any;
    if (element.includes(',')) {
      this.priceInputNumber = element.replaceAll('.', '').replace(',', '.');

      return;
    }

    elementNumber = element.replaceAll('.', '');
    this.priceInputNumber = elementNumber;

    let formattedValue = elementNumber.replaceAll('.', '');

    this.priceInput = formatCurrency(
      formattedValue,
      'it-IT',
      '',
      'EUR',
      '0.0-2',
    );
  }

  onInputCompleted(element) {
    this.priceInput = '€ ' + element.target.value;
  }

  coinQuantity(quantity) {
    return formatCurrency(quantity, 'it-IT', '', 'EUR', '0.2-2');
  }
}
