import {
  ChangeDetectionStrategy,
  Component,
  computed,
  signal,
} from '@angular/core';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { CryptopanicService } from 'src/app/core/services/http/cryptopanic.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

@Component({
  selector: 'app-widgets',
  templateUrl: './widgets.component.html',
  styleUrl: './widgets.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class WidgetsComponent {
  protected newsList$ = computed(() => {
    return this.tokeninsightService.cryptoNews()
      ? [
          ...this.tokeninsightService.cryptoNews(),
          ...this.cryptopanicService.cryptopanicList(),
        ].sort((a, b) => +new Date(b.timestamp) - +new Date(a.timestamp))
      : [];
  });
  protected newsList = this.tokeninsightService.cryptoNews;
  protected addedNews = this.tokeninsightService
    .addedNews()
    .sort((a, b) => +new Date(b.timestamp) - +new Date(a.timestamp));

  protected selectedNews = signal<number>(0);
  protected newsTimer: any;
  constructor(
    private tokeninsightService: TokeninsightService,
    protected currentAccountService: CurrentAccountService,
    private cryptopanicService: CryptopanicService,
  ) {}
}
