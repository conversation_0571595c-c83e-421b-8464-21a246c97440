.container {
  height: 100vh;
  width: 100vw;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
}

.exchange {
  padding: 1rem 0.5rem;
  font-size: 2rem;
  position: sticky;
  top: 0;
  left: 0;
  background: #000;
  z-index: 1;
  // background: linear-gradient(to bottom, rgb(14, 20, 53), black);
}

@media (min-width: 900px) {
  .main-content {
    display: grid;
    width: 100dvw;
    height: 100dvh;
    grid-template-columns: 280px auto;
    overflow: hidden;

    .side-menu {
      grid-column: 1/2;
      height: 100%;
      // margin: 0% 2%;
    }

    .inner-content {
      grid-column: 2/3;
      width: 100%;
      display: flex;
      flex-direction: column;
      overflow: auto;
      margin: 0;
      padding: 0 3rem;
    }
  }
}

@media (min-width: 1200px) {
  .exchange {
    padding: 0;
    // margin: 0% 12%;
  }
}
