a {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.news {
  margin: 0.5rem 1rem;

  & .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 3rem;
    font-weight: 500;
    padding: 1rem 0;
    margin-bottom: 2rem;

    & i {
      font-size: 2rem;
      margin-top: 0.2rem;
      cursor: pointer;
    }
  }
  & .tabs {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 2rem;

    & select {
      width: 100%;
      height: 35px;
      padding: 0 0.5rem;
      cursor: pointer;
      font-size: 1.6rem;
      color: black;
      border-radius: 5px;
    }

    & option {
      cursor: pointer;
    }
  }

  & .list {
    display: grid;
    grid-template-columns: 25% 75%;
    grid-template-rows: 20% 60% 20%;
    width: 100%;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid gray;

    &:last-child {
      border-bottom: none;
    }

    & .image {
      grid-column: 1/2;
      grid-row: 1/-1;
      align-self: center;
      justify-self: center;
      border-radius: 10px;
      height: 100%;
      max-height: 90px;
      min-width: 89px;
      position: relative;

      & .notification {
        position: absolute;
        top: -1.2rem;
        right: -1.2rem;
        background-color: green;
        border-radius: 50%;
        font-size: 1.4rem;
        width: 25px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      & img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 10px;
      }
    }

    & .tags {
      justify-self: start;
      display: flex;
      grid-column: 2/3;
      grid-row: 1/2;
      font-size: 1.2rem;
      margin-left: 1rem;

      & .tag {
        display: flex;
        justify-content: center;
        align-items: center;
        color: gray;
        background-color: rgb(30, 30, 30);
        border-radius: 5px;
        padding: 0.7rem;
        margin-right: 0.5rem;
        cursor: pointer;
      }
    }

    & .title {
      justify-self: start;
      align-self: center;
      grid-column: 2/3;
      grid-row: 2/3;
      font-size: 1.6rem;
      font-weight: 500;
      margin: 1rem 0 1rem 1rem;
    }

    & .date {
      justify-content: start;
      grid-column: 2/3;
      grid-row: 3/4;
      font-size: 1.2rem;
      color: gray;
      margin-left: 1rem;
    }
  }

  & .content {
    font-size: 1.4rem;
    line-height: 20px;
    text-align: justify;
    hyphens: auto;
    color: #d0d0d0;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid gray;
  }
}

@media screen and (min-width: 768px) {
  .news {
    // margin: 0 20%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    grid-template-rows: 50px auto;
    gap: 0 1.5rem;

    .tabs {
      grid-column: 1/2;
    }

    .empty {
      grid-column: 2/-1;
    }

    .list {
      border-radius: 10px;
      border: 1px solid rgb(67, 67, 67);
      // border-bottom: none;
      padding: 1rem;

      .title {
        align-self: start;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        line-clamp: 3; /* Specify the number of lines */
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;

        &.mobile {
          display: none;
        }
      }

      .image {
        min-width: unset;
      }

      .date {
        align-self: center;
        justify-self: end;
      }
    }
    .content {
      display: none;
    }
  }
}
