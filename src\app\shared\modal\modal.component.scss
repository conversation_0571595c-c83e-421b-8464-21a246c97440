.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100dvw;
  height: 100dvh;
  background-color: rgba(0, 0, 0, 0.75);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .modal-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 300px;
    background-color: #323232;
    border-color: gray;
    border: 1px solid #2d2d2d;
    box-shadow: 0 4px 10px #000c;
    padding: 1.5rem;
    font-size: 16px;
    border-radius: 10px;

    .modal-header {
      margin-bottom: 1.5rem;
    }

    .modal-body {
      .modal-text {
        margin-top: 1.5rem;
      }
      .loader-container {
        height: 50px;
        position: relative;
        transform: scale(0.7);
      }
    }
  }

  .button-container {
    display: flex;
    justify-content: center;

    .update-button {
      // margin-left: 50%;
      // transform: translate(-50%, 0);
      text-decoration: none;
      padding: 1rem 1.5rem;
      font-size: 16px;
      min-width: 118.38px;
      border-radius: 8px;
      cursor: pointer;
      background-color: #fff;
      outline: none;
      color: #000;
      // border: 1px solid #2d2d2d;
      // box-shadow: 0 3px 5px #000c;
      border: none;

      // background: #555;
      box-shadow:
        0 4px 6px rgba(0, 0, 0, 0.3),
        0 10px 20px rgba(0, 0, 0, 0.2);
      transition:
        box-shadow var(--transition-speed),
        transform var(--transition-speed);

      &:disabled {
        opacity: 0.7;
      }

      &-content {
        display: flex;
        width: 100%;
      }
    }
  }
}
