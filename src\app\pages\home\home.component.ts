import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  Input,
  TemplateRef,
  ViewChild,
  computed,
  effect,
} from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('flipCard', [
      state('front', style({ transform: 'rotateY(0deg)' })),
      state('back', style({ transform: 'rotateY(-180deg)' })),
      transition('front => back', animate('0.8s ease-out')),
      transition('back => front', animate('0.8s ease-in')),
    ]),
  ],
})
export class HomeComponent {
  url: string = 'https://www.criptovaluta.it/';
  urlSafe: SafeResourceUrl;

  protected currentPortfolio = this.currentAccountService.currentPortfolio;

  protected currentPortfolioHistory =
    this.currentAccountService.currentPortfolioHistory;

  protected currentExchange = this.currentAccountService.currentAccount;

  protected currentAccount = this.currentAccountService.currentAccount;

  protected currentUser = this.currentAccountService.currentUser;

  protected ytd = computed(() => {
    const index = this.currentPortfolioHistory().portfolioMonthly.findIndex(
      (item) => item.formatDate === '01/2025',
    );

    return (
      this.currentPortfolio()?.portfolioStats?.profits -
      this.currentPortfolioHistory()?.portfolioMonthly[index]?.profit
    );
  });

  protected last3Months = computed(() => {
    const index = this.currentPortfolioHistory()?.portfolioMonthly?.length - 5;

    return (
      this.currentPortfolio()?.portfolioStats?.profits -
      this.currentPortfolioHistory()?.portfolioMonthly[index]?.profit
    );
  });

  protected last1Month = computed(() => {
    const index = this.currentPortfolioHistory()?.portfolioMonthly?.length - 2;

    // console.log(this.currentPortfolioHistory()?.portfolioMonthly[index]);
    return (
      this.currentPortfolio()?.portfolioStats?.profits -
      this.currentPortfolioHistory()?.portfolioMonthly[index]?.profit
    );
  });

  protected ratingsLoaded = false;

  protected shouldFetchRatings = this.tokeninsightService.shouldFetchRating();

  @Input() accountSelector!: TemplateRef<any>;

  protected flipState: string = 'front';
  private startX: number;
  private scrollThreshold: number = 40;

  @ViewChild('card', { static: true }) cardElement: ElementRef;

  onTouchStart(event: TouchEvent) {
    this.startX = event.touches[0].clientX;
  }

  onTouchMove(event: TouchEvent) {
    const currentX = event.touches[0].clientX;
    const deltaX = this.startX - currentX;

    if (deltaX > this.scrollThreshold) {
      // Swipe verso sinistra con una distanza maggiore della soglia
      this.flipState = 'back';
    } else if (deltaX < -this.scrollThreshold) {
      // Swipe verso destra con una distanza maggiore della soglia
      this.flipState = 'front';
    }
  }

  constructor(
    private currentAccountService: CurrentAccountService,
    private tokeninsightService: TokeninsightService,
    public sanitizer: DomSanitizer,
  ) {
    this.urlSafe = this.sanitizer.bypassSecurityTrustResourceUrl(this.url);
    effect(() => {
      if (this.currentAccountService.currentAccount()) this.flipState = 'front';
    });

    // if (this.currentUser()?.username === 'pac') {
    //   this.currentAccountService.getCurrentPortfolio('portfolio4');
    // }
  }

  getStartDate() {
    return this.currentPortfolioHistory().portfolioMonthly[0].date;
  }

  ratingBackground(rating: string) {
    switch (rating) {
      case 'AAA':
        return 'green';
      case 'AA':
        return 'green';
      case 'A':
        return 'green';
      case 'BBB':
        return 'rgb(19, 108, 110)';
      case 'BB':
        return 'rgb(19, 108, 110)';
      case 'B':
        return 'rgb(19, 108, 110)';
      case 'CCC':
        return '#ee974d';
      case 'CC':
        return '#ee974d';
      case 'C':
        return '#ee974d';
      case 'DDD':
        return '#f8685f;';
      case 'DD':
        return '#f8685f;';
      case 'D':
        return '#f8685f;';

      default:
        return '#f8685f';
    }
  }
}
