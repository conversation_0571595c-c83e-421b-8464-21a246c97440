import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BlockchainTvlComponent } from './blockchain-tvl.component';

describe('BlockchainTvlComponent', () => {
  let component: BlockchainTvlComponent;
  let fixture: ComponentFixture<BlockchainTvlComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BlockchainTvlComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(BlockchainTvlComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
