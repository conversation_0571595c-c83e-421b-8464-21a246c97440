import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth/auth.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { GoogleSheetService } from 'src/app/core/services/http/google-sheet.service';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginComponent {
  protected username = signal<string>('');
  protected password = signal<string>('');
  protected userInput = signal<string>('');
  protected pageLoading = signal(true);
  protected loading = signal(false);
  protected authLoading = signal(false);
  protected authError = signal(false);

  constructor(
    private authService: AuthService,
    private router: Router,
    private currentAccountService: CurrentAccountService,
    private googleSheetService: GoogleSheetService,
  ) {
    console.log('LOGGED IN?', this.authService.isLoggedIn);

    if (
      this.authService.isLoggedIn &&
      this.currentAccountService.currentUser() !== null
    ) {
      this.router.navigate(['']);
    }
    if (
      !this.authService.isLoggedIn &&
      !this.currentAccountService.currentUser() !== null
    ) {
      authService.userData$.pipe(takeUntilDestroyed()).subscribe((data) => {
        if (
          (data !== null || data !== undefined) &&
          this.authService.isLoggedIn
        ) {
          // console.log('ONLOGIN', data);
          this.currentAccountService.loadPortfolios(data.email);
          // this.router.navigate(['']);
        }
      });
    }

    // this.router.navigateByUrl('/');

    // this.authService.userData$.subscribe((data) => {
    //   if (data !== null && this.authService.isLoggedIn) {
    //     console.log('USER ?', data);

    //   }
    // });
  }

  onLogin() {
    if (this.authLoading()) {
      return;
    }

    this.authLoading.set(true);
    this.authError.set(false);

    this.authService.SignIn(this.username(), this.password()).then((data) => {
      // console.log('DATA', data);
      if (data == 'ERROR') {
        this.authLoading.set(false);
        this.authError.set(true);
        setTimeout(() => {
          this.authError.set(false);
        }, 5000);
      } else {
        this.googleSheetService.fetchEtfBtcEthHistoryData().subscribe();
        // this.googleSheetService.fetchFedProbabilty().subscribe();
      }
    });
  }
}
