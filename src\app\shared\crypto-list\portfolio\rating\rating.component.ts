import { CurrencyPipe } from '@angular/common';
import { Component, Input, effect, signal } from '@angular/core';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

@Component({
  selector: 'app-rating',
  templateUrl: './rating.component.html',
  styleUrl: './rating.component.scss',
  providers: [CurrencyPipe],
})
export class RatingComponent {
  @Input('portfolio') portfolio!: any;

  protected showInfo: boolean = false;
  protected priceInput: string;
  protected priceInputNumber: number | string;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected currentCoin: string = '';
  protected profits: string;
  protected currentSorting: string = 'score';
  protected loading$ = signal(true);
  protected sortScore =
    this.currentAccountService.currentPortfolio().sortRatingScore;
  protected sortTime =
    this.currentAccountService.currentPortfolio().sortRatingReviewTime;

  constructor(
    private currentAccountService: CurrentAccountService,
    private tokeninisghtService: TokeninsightService
  ) {
    effect(() => {
      this.currentAccountService.currentAccount()
        ? (this.showInfo = false)
        : null;
    });

    if (!this.tokeninisghtService.shouldFetchRating()) {
      this.loading$.set(false);
    } else {
      this.tokeninisghtService.loading$.subscribe((data) => {
        data == 'ok' ? this.loading$.set(false) : null;
      });
    }
  }

  onInfoClick(value) {
    if (!this.currentCoin) this.showInfo = true;

    let currentCoinValue = (
      value.target.parentElement.children[1]
        ? value.target.parentElement.children[1].textContent
        : value.target.parentElement.parentElement.children[1].textContent
    ).trim();

    this.currentCoin == currentCoinValue
      ? (this.showInfo = !this.showInfo)
      : (this.showInfo = true);

    this.currentCoin = currentCoinValue;

    this.depositsInput = '';
    this.priceInput = '';
    this.priceInputNumber = '';
  }

  onInputCompleted(element) {
    this.priceInput = '€ ' + element.target.value;
  }

  currentFilter() {
    if (this.currentSorting == 'score')
      return this.currentAccountService.currentPortfolio()?.sortRatingScore;
    if (this.currentSorting == 'reviewTime')
      return this.currentAccountService.currentPortfolio()
        ?.sortRaitingReviewTime;
  }
}
