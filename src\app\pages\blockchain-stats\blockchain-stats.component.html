<app-loader-spinner [loading]="loading()"></app-loader-spinner>
@if (!loading()) {
  <div class="crypto-list" style="margin-top: 0; margin-bottom: 0">
    <div class="crypto-list-title" style="margin-bottom: 1rem">
      Total Value Locked
    </div>

    <!-- Buttons -->
    <div class="crypto-list-buttons" style="margin-bottom: 0">
      <div
        class="crypto-list-buttons-tvl"
        (click)="onChartFilterChange('all')"
        [class]="{ selected: chartFilter() == 'all' }"
      >
        All
      </div>
      <div
        class="crypto-list-buttons-tvl"
        (click)="onChartFilterChange('2y')"
        [class]="{ selected: chartFilter() == '2y' }"
      >
        2Y
      </div>
      <div
        class="crypto-list-buttons-fees"
        (click)="onChartFilterChange('1y')"
        [class]="{ selected: chartFilter() == '1y' }"
      >
        1Y
      </div>
      <div
        class="crypto-list-buttons-fees"
        (click)="onChartFilterChange('30d')"
        [class]="{ selected: chartFilter() == '30d' }"
      >
        30D
      </div>

      <div class="extra"></div>
    </div>
    <!-- Buttons END -->
  </div>
  <div class="chart">
    @if (chainsTvlHistory()) {
      <app-blockchain-linechart
        [timeInterval]="chartFilter()"
      ></app-blockchain-linechart>
    }
  </div>

  <div class="crypto-list">
    <div class="crypto-list-title" style="margin-bottom: 0">Stats</div>

    <div class="info">
      <div class="info-text">
        @switch (currentFilter) {
          @case ("tvl") {
            <div class="deposits">
              <span style="color: #a2a2a2; margin-right: 0.5rem"
                >Total Value Locked:</span
              >
              {{ chainsTotalTvl | shortNumber }}
            </div>
          }
          @case ("protocols") {
            <div class="deposits">
              <span style="color: #a2a2a2; margin-right: 0.5rem"
                >Total Protocols:</span
              >
              {{
                (chainsTotalProtocols | number: "1.0-0").replaceAll(",", ".")
              }}
            </div>
          }
          @case ("fees1y") {
            <div class="deposits">
              <span style="color: #a2a2a2; margin-right: 0.5rem"
                >Total Fees 1Y:</span
              >
              {{ chainsTotalFees1y | shortNumber }}
            </div>
          }
          @case ("fees30d") {
            <div class="deposits">
              <span style="color: #a2a2a2; margin-right: 0.5rem"
                >Total Fees 30D:</span
              >
              {{ chainsTotalFees30d | shortNumber }}
            </div>
          }
          @case ("fees7d") {
            <div class="deposits">
              <span style="color: #a2a2a2; margin-right: 0.5rem"
                >Total Fees 7D:</span
              >
              {{ chainsTotalFees7d | shortNumber }}
            </div>
          }
        }
      </div>
    </div>

    <!-- Buttons -->
    <div class="crypto-list-buttons">
      <div
        class="crypto-list-buttons-tvl"
        (click)="currentFilter = 'tvl'"
        [class]="{ selected: currentFilter == 'tvl' }"
      >
        TVL
      </div>
      <div
        class="crypto-list-buttons-tvl"
        (click)="currentFilter = 'protocols'"
        [class]="{ selected: currentFilter == 'protocols' }"
      >
        Protocols
      </div>
      <div
        class="crypto-list-buttons-fees"
        (click)="currentFilter = 'fees1y'"
        [class]="{ selected: currentFilter == 'fees1y' }"
      >
        Fees 1Y
      </div>
      <div
        class="crypto-list-buttons-fees"
        (click)="currentFilter = 'fees30d'"
        [class]="{ selected: currentFilter == 'fees30d' }"
      >
        Fees 30D
      </div>
      <div
        class="crypto-list-buttons-fees"
        (click)="currentFilter = 'fees7d'"
        [class]="{ selected: currentFilter == 'fees7d' }"
      >
        Fees 7D
      </div>
      <!-- <div
        class="crypto-list-buttons-fees"
        (click)="currentFilter = 'volume30d'"
        [class]="{ selected: currentFilter == 'volume30d' }"
      >
        Volume 30D
      </div>
      <div
        class="crypto-list-buttons-fees"
        (click)="currentFilter = 'volume7d'"
        [class]="{ selected: currentFilter == 'volume7d' }"
      >
        Volume 7D
      </div> -->

      <div class="extra"></div>
    </div>
    <!-- Buttons END -->

    <!-- List filters -->
    <div class="tabs">
      @if (currentFilter == "tvl") {
        <app-blockchain-tvl
          [chainsTvl]="chainsTvl()"
          [chainsTotalTvl]="chainsTotalTvl"
        ></app-blockchain-tvl>
      }
      @if (currentFilter == "protocols") {
        <app-blockchain-protocols
          [chainsProtocols]="chainsProtocols"
          [chainsTotalProtocols]="chainsTotalProtocols"
        ></app-blockchain-protocols>
      }
      @if (currentFilter == "fees1y") {
        <app-blockchain-fees
          [chainsFees]="chainsFees1y"
          [chainsTotalFees]="chainsTotalFees1y"
          [date]="'1y'"
        ></app-blockchain-fees>
      }
      @if (currentFilter == "fees30d") {
        <app-blockchain-fees
          [chainsFees]="chainsFees30d"
          [chainsTotalFees]="chainsTotalFees30d"
          [date]="'30d'"
        ></app-blockchain-fees>
      }
      @if (currentFilter == "fees7d") {
        <app-blockchain-fees
          [chainsFees]="chainsFees7d"
          [chainsTotalFees]="chainsTotalFees7d"
          [date]="'7d'"
        ></app-blockchain-fees>
      }
    </div>

    <!-- @if(currentFilter == 'volume30d'){
    <app-blockchain-volume
      [chainsVolume]="chainsVolume30d"
      [chainsTotalVolume]="chainsTotalVolume30d"
      [date]="'30d'"
    ></app-blockchain-volume
    >} @if(currentFilter == 'volume7d'){
    <app-blockchain-volume
      [chainsVolume]="chainsVolume7d"
      [chainsTotalVolume]="chainsTotalVolume7d"
      [date]="'7d'"
    ></app-blockchain-volume>
    } -->
  </div>

  <!-- List filters END -->
}
