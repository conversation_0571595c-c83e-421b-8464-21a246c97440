/* You can add global styles to this file, and also import other style files */
@import "./theme/fonts/font-awesome/fontawesome.css";
@import "./theme/fonts/font-awesome/brands.css";
@import "./theme/fonts/font-awesome/solid.css";

@import url("https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;1,700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,700&family=Roboto:wght@100;300;400;500;700;900&display=swap");

*,
*::after,
*::before {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: "Roboto", arial, serif;
}

// @media (min-width: 900px) {
//   body {
//     margin: 0 25%;
//   }
// }

html {
  font-size: 62.5%;
}

body {
  background-color: black;
  color: white;
}

:root {
  --swiper-pagination-color: #d8d6d6;
  --swiper-pagination-bullet-inactive-color: #c4c4c4;
}

select {
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.8) !important;
}

button {
  appearance: none;
  -webkit-appearance: none;
}

:root {
  --extra-bg: #061310;
  --green-profit: rgb(4, 220, 0);
  --dark-orange: #d17300;
  --card-bg: #141414;
  --border: #141414;
}
