.crypto-list-header {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 30px 34% 15% 19% auto;
  width: 100%;
  margin: 1rem 0 0rem 0;
  font-size: 1.4rem;
  color: #5b5b5b;
  // border-bottom: 1px solid #282828;

  &-name,
  &-ath-date,
  &-ath {
    align-self: start;
  }

  &-name {
    grid-column: 2/3;
    padding-left: 0.7rem;
  }

  &-ath-date {
    grid-column: 3/4;
    justify-self: center;
    cursor: pointer;
  }

  &-ath {
    grid-column: 4/5;
    justify-self: end;
    cursor: pointer;
  }
  &-athProfit {
    grid-column: 5/6;
    justify-self: end;
    cursor: pointer;

    & .totalProfit {
      display: flex;
      justify-content: flex-end;
      color: rgb(0, 100, 0);
    }
  }
}

.crypto-list-table {
  display: grid;
  grid-template-rows: 17px 17px;
  grid-template-columns: 30px 34% 15% 19% auto;
  margin-top: 1.2rem;
  width: 100%;
  row-gap: 0.3rem;
  // border-bottom: 1px solid #282828;
  padding-bottom: 0.6rem;

  &-logo {
    grid-row: 1/3;
    grid-column: 1/2;
    justify-self: center;
    align-self: center;
    width: 100%;
    margin-top: 0.3rem;
    margin-left: 0.2rem;
    cursor: pointer;

    & img {
      width: 100%;
      height: 100%;
    }
  }

  &-name {
    grid-row: 2/3;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.2rem;
    padding-left: 0.7rem;
    color: #5b5b5b;
    cursor: pointer;
  }

  &-ticker {
    grid-row: 1/2;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.6rem;
    padding-left: 0.7rem;
    cursor: pointer;
  }

  &-price {
    grid-row: 1/3;
    grid-column: 3/4;
    justify-self: end;
    align-self: center;
    font-size: 1.4rem;
  }

  &-date {
    grid-row: 1/2;
    grid-column: 3/4;
    justify-self: center;
    align-self: center;
    font-size: 1.4rem;
  }

  &-ath {
    grid-row: 1/2;
    grid-column: 4/5;
    justify-self: end;
    align-self: center;
    font-size: 1.4rem;
  }

  &-gainPercent {
    grid-row: 2/3;
    grid-column: 4/5;
    align-self: end;
    justify-self: end;
    display: flex;
    flex-direction: row;
    background-color: rgba(0, 100, 0, 0.6);
    color: #04dc00;
    border-radius: 15px;
    padding: 0.15rem 0.8rem;

    &-icon {
      margin-right: 0.4rem;

      & i {
        padding-top: 0.3rem;
        font-size: 0.9rem;
      }
    }

    &-number {
      font-size: 1.2rem;
    }
  }

  &-athProfit {
    grid-row: 1/2;
    grid-column: 5/6;
    justify-self: end;
    align-self: center;
    font-size: 1.4rem;
    color: #04dc00;
  }

  &-athProfitPercent {
    grid-row: 2/3;
    grid-column: 5/6;
    align-self: end;
    justify-self: end;
    display: flex;
    flex-direction: row;
    background-color: rgba(0, 100, 0, 0.6);
    color: #04dc00;
    border-radius: 15px;
    padding: 0.15rem 0.8rem;

    &-icon {
      margin-right: 0.4rem;

      & i {
        padding-top: 0.3rem;
        font-size: 0.9rem;
      }
    }

    &-number {
      font-size: 1.2rem;
    }
  }
}

@media (min-width: 900px) {
  .crypto-list-table-date {
    justify-self: end;
  }

  .crypto-list-header-ath-date {
    justify-self: end;
  }
}
