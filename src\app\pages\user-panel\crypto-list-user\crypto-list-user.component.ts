import { Component, effect } from '@angular/core';
import { ICoin } from 'src/app/core/interfaces/coins';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-crypto-list-user',
  templateUrl: './crypto-list-user.component.html',
  styleUrl: './crypto-list-user.component.scss',
})
export class CryptoListUserComponent {
  protected currentPortfolio = this.currentAccountService.currentPortfolio;
  protected editMode = false;
  protected currentCoin = 'start';
  protected coinQuantity: number;
  protected coinDeposits: number;
  protected confirmDelete: boolean = false;

  constructor(private currentAccountService: CurrentAccountService) {
    effect(() => {
      if (this.currentAccountService.currentAccount()) {
        this.editMode = false;
        this.currentCoin = '';
      }
    });
  }

  onEditClick(coin: string) {
    console.log('CURRENTCOIN', this.currentCoin);

    this.editMode = true;
    this.currentCoin = coin;
  }

  onCloseEditClick() {
    this.editMode = false;
    this.currentCoin = '';
    this.coinDeposits = null;
    this.coinQuantity = null;
    this.confirmDelete = false;
  }

  onSaveClick(coin: ICoin) {
    console.log('DATI', this.coinDeposits, this.coinQuantity);

    let editCoin = this.currentPortfolio().sortDeposits;

    editCoin.forEach((item) => {
      if (item.name == coin.name)
        (item.quantity = this.coinQuantity),
          (item.deposits = this.coinDeposits);
    });

    // this.firebaseService.editCoins(
    //   editCoin,
    //   this.currentAccountService.currentAccount(),
    //   'portfolios'
    // );

    this.onCloseEditClick();
  }
}
