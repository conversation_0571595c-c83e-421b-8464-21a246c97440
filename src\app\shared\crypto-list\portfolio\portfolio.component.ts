import {
  ChangeDetectionStrategy,
  Component,
  effect,
  input,
  signal,
  untracked,
} from '@angular/core';
import { portoflioFilters } from 'src/app/core/interfaces/portoflioFilters';
import { PortfolioCoinbaseFra } from 'src/app/core/services/data/coinbase-fra.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-portfolio',
  templateUrl: './portfolio.component.html',
  styleUrl: './portfolio.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PortfolioComponent {
  protected portoflioFilters = portoflioFilters;
  protected currentFilter = signal<portoflioFilters>(portoflioFilters.PROFIT);
  portfolio = input<PortfolioCoinbaseFra>();

  constructor(private currentAccount: CurrentAccountService) {
    effect(() => {
      if (this.currentAccount.currentPortfolio())
        untracked(() => this.currentFilter.set(portoflioFilters.PROFIT));
    });
  }

  onCurrentFilterClick(filter: portoflioFilters) {
    this.currentFilter.set(filter);
  }
}
