<div class="crypto-list">
  <!-- Buttons -->
  <div class="crypto-list-buttons">
    <div
      class="crypto-list-buttons-profits"
      (click)="onCurrentFilterClick(portoflioFilters.PROFIT)"
      [class]="{ selected: currentFilter() == portoflioFilters.PROFIT }"
      >
      Profit
    </div>
    <div
      class="crypto-list-buttons-current"
      (click)="onCurrentFilterClick(portoflioFilters.CURRENT)"
      [class]="{ selected: currentFilter() == portoflioFilters.CURRENT }"
      >
      Current
    </div>
    <div
      class="crypto-list-buttons-deposits"
      (click)="onCurrentFilterClick(portoflioFilters.DEPOSITS)"
      [class]="{ selected: currentFilter() == portoflioFilters.DEPOSITS }"
      >
      Deposits
    </div>
    <div
      class="crypto-list-buttons-ath"
      (click)="onCurrentFilterClick(portoflioFilters.ATH)"
      [class]="{ selected: currentFilter() == portoflioFilters.ATH }"
      >
      ATH
    </div>
    <div
      class="crypto-list-buttons-h24"
      (click)="onCurrentFilterClick(portoflioFilters.H24)"
      [class]="{ selected: currentFilter() == portoflioFilters.H24 }"
      >
      24h
    </div>
    <div
      class="crypto-list-buttons-rank"
      (click)="onCurrentFilterClick(portoflioFilters.RANK)"
      [class]="{ selected: currentFilter() == portoflioFilters.RANK }"
      >
      Rank
    </div>
    <div
      class="crypto-list-buttons-quantity"
      (click)="onCurrentFilterClick(portoflioFilters.QUANTITY)"
      [class]="{ selected: currentFilter() == portoflioFilters.QUANTITY }"
      >
      Quantity
    </div>
    <!-- @if (portfolio()?.sortEarn.length > 0) {
    <div
      class="crypto-list-buttons-earn"
      (click)="onCurrentFilterClick(portoflioFilters.EARN)"
      [class]="{ selected: currentFilter() == portoflioFilters.EARN }"
      >
      Earn
    </div>
    } -->
    <div
      class="crypto-list-buttons-category"
      (click)="onCurrentFilterClick(portoflioFilters.CATEGORY)"
      [class]="{ selected: currentFilter() == portoflioFilters.CATEGORY }"
      >
      Category
    </div>
    <div
      class="crypto-list-buttons-rating"
      (click)="onCurrentFilterClick(portoflioFilters.RATING)"
      [class]="{ selected: currentFilter() == portoflioFilters.RATING }"
      >
      Rating
    </div>
    <div
      class="crypto-list-buttons-ratingGroup"
      (click)="onCurrentFilterClick(portoflioFilters.RATING_GROUP)"
      [class]="{ selected: currentFilter() == portoflioFilters.RATING_GROUP }"
      >
      Rating Group
    </div>
    <div
      class="crypto-list-buttons-info"
      (click)="onCurrentFilterClick(portoflioFilters.INFO)"
      [class]="{ selected: currentFilter() == portoflioFilters.INFO }"
      >
      Info
    </div>
    <div class="extra"></div>
  </div>
  <!-- Buttons END -->

  <!-- List filters -->
  @if (currentFilter() == portoflioFilters.PROFIT) {
    <app-profits
      [portfolio]="portfolio()"
    ></app-profits>
  }
  @if (currentFilter() == portoflioFilters.CURRENT) {
    <app-current
      [portfolio]="portfolio()"
    ></app-current>
  }
  @if (currentFilter() == portoflioFilters.DEPOSITS) {
    <app-deposits
      [portfolio]="portfolio()"
    ></app-deposits>
  }
  @if (currentFilter() == portoflioFilters.ATH) {
    <app-ath
      [portfolio]="portfolio()"
    ></app-ath>
  }
  @if (currentFilter() == portoflioFilters.H24) {
    <app-h24
      [portfolio]="portfolio()"
    ></app-h24>
  }
  @if (currentFilter() == portoflioFilters.RANK) {
    <app-rank
      [portfolio]="portfolio()"
    ></app-rank>
  }
  @if (currentFilter() == portoflioFilters.QUANTITY) {
    <app-quantity
      [portfolio]="portfolio()"
    ></app-quantity>
  }
  @if (portfolio()?.sortEarn.length > 0) {
    @if (currentFilter() == portoflioFilters.EARN) {
      <app-earn
        [portfolio]="portfolio()"
      ></app-earn>
    }
  }

  @if (currentFilter() == portoflioFilters.CATEGORY) {
    <app-category
      [portfolio]="portfolio()"
    ></app-category>
  }
  @if (currentFilter() == portoflioFilters.RATING) {
    <app-rating
      [portfolio]="portfolio()"
    ></app-rating>
  }
  @if (currentFilter() == portoflioFilters.RATING_GROUP) {
    <app-rating-group
      [portfolio]="portfolio()"
    ></app-rating-group>
  }
  @if (currentFilter() == portoflioFilters.INFO) {
    <app-info
      [portfolio]="portfolio()"
    ></app-info>
  }
  <!-- List filters END -->
</div>
