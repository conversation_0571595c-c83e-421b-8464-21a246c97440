import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from '../../shared/loader-spinner/loader-spinner.module';
import { TokenStatsPriceComponent } from './token-stats-price/token-stats-price.component';
import { TokenStatsRoutingModule } from './token-stats-routing.module';
import { TokenStatsComponent } from './token-stats.component';

@NgModule({
  declarations: [TokenStatsComponent, TokenStatsPriceComponent],
  imports: [
    CommonModule,
    TokenStatsRoutingModule,
    PipesModule,
    LoaderSpinnerModule,
  ],
  exports: [TokenStatsComponent, TokenStatsPriceComponent],
})
export class TokenStatsModule {}
