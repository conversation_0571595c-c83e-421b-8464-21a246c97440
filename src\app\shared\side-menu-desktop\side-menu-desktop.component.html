<div class="menu-container">
  <div class="side-bar" #sidebar>
    <div class="menu-header">
      <img src="/assets/img/logo/btc.png" alt="logo" />
      <div>Cripto Tracker</div>
    </div>
    <div class="menu">
      <div
        class="item"
        (click)="handleMenuClick(menuType.HOME)"
        [ngClass]="{
          selected: currentMenuSelected() === menuType.HOME
        }"
      >
        <a
          ><div class="icon"><i class="fa-solid fa-house"></i></div>
          Dashboard</a
        >
      </div>
      <div
        class="item"
        (click)="handleMenuClick(menuType.NEWS)"
        [ngClass]="{
          selected: currentMenuSelected() === menuType.NEWS
        }"
      >
        <a>
          <div class="icon"><i class="fa-regular fa-bullhorn"></i></div>
          News
          @if (addedNews().length > 0) {
            <div class="notificationNews">
              {{ addedNews().length > 9 ? "!" : addedNews().length }}
            </div>
          }
        </a>
      </div>
      <div
        class="item"
        (click)="handleMenuClick(menuType.ANNUALREPORT)"
        [ngClass]="{
          selected: currentMenuSelected() === menuType.ANNUALREPORT
        }"
      >
        <a>
          <div class="icon"><i class="fa-solid fa-chart-line"></i></div>
          Annual Report
          <!-- <i class="fas fa-angle-right dropdown"></i> -->
        </a>
        <!-- <div class="sub-menu" #subMenu>
            <a href="#" class="sub-item">Sub Item 01</a>
            <a href="#" class="sub-item">Sub Item 02</a>
          </div> -->
      </div>
      <div
        class="item"
        (click)="handleMenuClick(menuType.DEPOSITHISTORY)"
        [ngClass]="{
          selected: currentMenuSelected() === menuType.DEPOSITHISTORY
        }"
      >
        <a>
          <div class="icon">
            <i class="fa-solid fa-money-bill-transfer"></i>
          </div>
          Deposit History
        </a>
      </div>
      @if (currentUser()?.username !== "elisa") {
        <div
          class="item"
          (click)="handleMenuClick(menuType.CRYPTOVOLUMES)"
          [ngClass]="{
            selected: currentMenuSelected() === menuType.CRYPTOVOLUMES
          }"
        >
          <a>
            <div class="icon">
              <i class="fa-solid fa-magnifying-glass-chart"></i>
            </div>
            Crypto Volumes
          </a>
        </div>

        <div
          class="item"
          (click)="handleMenuClick(menuType.ALTCOINSEASON)"
          [ngClass]="{
            selected: currentMenuSelected() === menuType.ALTCOINSEASON
          }"
        >
          <a>
            <div class="icon">
              <i class="fa-solid fa-rocket"></i>
            </div>
            Altcoin Season
          </a>
        </div>
        <div
          class="item"
          (click)="handleMenuClick(menuType.BLOCKCHAINSTATS)"
          [ngClass]="{
            selected: currentMenuSelected() === menuType.BLOCKCHAINSTATS
          }"
        >
          <a>
            <div class="icon">
              <i class="fa-solid fa-chart-pie"></i>
            </div>
            Blockchain Stats
          </a>
        </div>
        <div
          class="item"
          (click)="handleMenuClick(menuType.TOKENSTATS)"
          [ngClass]="{
            selected: currentMenuSelected() === menuType.TOKENSTATS
          }"
        >
          <a>
            <div class="icon">
              <i class="fa-solid fa-magnifying-glass-dollar"></i>
            </div>
            Token Stats
          </a>
        </div>
      }

      @if (currentUser()?.username !== "elisa") {
        <div class="item stock-tracker">
          <a href="https://stocktracker24.netlify.app/"
            ><div class="icon">
              <img src="/assets/img/logo/tsla.svg" alt="Stock Tracker" />
            </div>
            Stock Tracker
            <i class="fa-solid fa-arrow-up-right-from-square fa-xs"></i>
          </a>
        </div>
      }
      <div class="item logout" (click)="handleMenuClick(menuType.LOGOUT)">
        <a [routerLink]="'/login'" style="color: red">
          <div class="icon">
            <i class="fa-solid fa-right-from-bracket"></i>
          </div>
          Logout</a
        >
      </div>
    </div>
  </div>
  <!-- <section class="main">
      <h1>Sidebar Menu With<br />Sub-Menus</h1>
    </section> -->
</div>
