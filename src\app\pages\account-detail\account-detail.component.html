<div class="home-desktop">
  <div class="home">
    <div class="home-desktop-1">
      <app-culumnchart-yearly></app-culumnchart-yearly>

      <div class="container">
        <div class="taxesInfo">
          <div
            class="taxesInfo-title"
            [ngClass]="{ open: showTaxesInfo }"
            (click)="showTaxesInfo = !showTaxesInfo"
          >
            Info dichiarazione
            @if (showTaxesInfo) {
              <i class="fa-solid fa-chevron-up"></i>
            } @else {
              <i class="fa-solid fa-chevron-down"></i>
            }
          </div>
          @if (showTaxesInfo) {
            <div class="taxesInfo-text">
              <div class="otherTaxes">
                <i class="fa-solid fa-circle"></i>Imposta di bollo:
              </div>
              Si paga il 0,20% sul valore delle cripto detenute alla fine
              dell'anno e si calcola in base al controvalore in euro al 31
              dicembre (alcuni exchange fanno da sostituto d'imposta).
              <br />
              <br />
              <br />
              <div class="rw">
                <i class="fa-solid fa-circle"></i> Quadro RW:
              </div>
              <div style="width: 100%">
                Valore iniziale = Valore al 01/01
                <br />
                Valore finale = Valore al 31/12
              </div>
              <br /><br />

              <div class="rt">
                <i class="fa-solid fa-circle"></i> Quadro RT:
              </div>
              Si paga il 26% se ci sono trades chiusi superiori a 2.000 € e solo
              sulla parte eccedente i 2.000€.
              <br /><br />
              Per trades chiusi si intendono conversioni di cripto in valuta
              fiat (ad esempio USDT), mentre non sono sono considerati trades
              chiusi le conversioni tra cripto. <br />
              Nel conteggio si dovranno sommare anche i guadagni derivanti dallo
              staking e anche in questo caso c'è la soglia minima di 2.000 €.
              <br />
              <br />
              Eventuali minusvalenze degli anni precedenti possono essere
              portate in detrazione fino a 4 anni successivi (ad esempio il
              31/12/2023 scadono le minsuvalenze maturate nel 2019).
              <br /><br />
              <span
                ><strong>ESEMPIO 1:</strong>
                <br />
                Se il guadagno complessivo annuale (trades chiusi e guadagni
                derivanti da staking) ammonta a 10.000€ l'imposta del 26% andrà
                calcolata su € 8.000 (€ 10.000 - €2.000) e quindi ammonterà a €
                2.080.</span
              >
              <span
                ><br />
                <strong>ESEMPIO 2:</strong>
                <br />
                Se il guadagno complessivo annuale (trades chiusi e guadagni
                derivanti da staking) ammonta a 10.000€ e ci sono minsuvalenze
                degli anni passati di 5.000 l'imposta del 26% andrà calcolata su
                3.000 € (10.000 € - 5.000 € - 2.000€ ) e quindi ammonterà
                780€.</span
              >
            </div>
          }
        </div>

        @for (
          item of currentPortfolioHistory().portfolioYearly?.toReversed();
          track item.year
        ) {
          <div
            class="yearTitle"
            [ngClass]="{ open: showDetail && currentYear == item?.year }"
            (click)="onShowDetailClick(item.year)"
          >
            {{ item?.year }}
            @if (showDetail && currentYear == item?.year) {
              <i class="fa-solid fa-chevron-up"></i>
            } @else {
              <i class="fa-solid fa-chevron-down"></i>
            }
          </div>

          <div
            class="table-header"
            [ngClass]="{ open: showDetail && currentYear == item?.year }"
            (click)="onShowDetailClick(item.year)"
          >
            <div class="startTitle">INIZIO</div>
            <div class="depositsTitle">DEPOSITI</div>
            <div class="endTitle">FINE</div>
            <div class="profitTitle">UTILE</div>
            <div class="profitPercTitle">UTILE %</div>

            <div class="start">{{ item?.start | depositsNoDecimal }}</div>
            <div class="deposits">{{ item?.deposits | depositsNoDecimal }}</div>
            <div class="end">{{ item?.end | depositsNoDecimal }}</div>
            <div
              class="profit"
              [ngStyle]="{
                color: colorValue(item?.netProfit)
              }"
            >
              {{ item?.netProfit | profitsNoDecimal }}
            </div>
            <div
              class="profitPerc"
              [ngStyle]="{
                color: colorValue(item?.netProfit)
              }"
            >
              {{ item?.netProfitPerc | profitsPercSimulator }}
            </div>
          </div>

          @if (showDetail && currentYear == item?.year) {
            <div class="table">
              <div class="start">Valore al 01/01</div>
              <div class="start-value">
                {{ item?.start | depositsNoDecimal }}
              </div>
              <div class="deposits">Depositi</div>
              <div class="deposits-value">
                {{ item?.deposits | depositsNoDecimal }}
              </div>
              <div class="deposits-start">Depositi + Valore iniziale</div>
              <div class="deposits-start-value">
                {{ item?.startAndDeposits | depositsNoDecimal }}
              </div>
              <div class="fees">Commissioni</div>
              <div class="fees-value">{{ item?.fees | profitsNoDecimal }}</div>
              <div class="withdraw">Prelievi</div>
              <div class="withdraw-value">
                {{ item?.withdraw | depositsNoDecimal }}
              </div>
              <div class="openTrades">Trades in corso</div>
              <div
                class="openTrades-value"
                [ngStyle]="{
                  color: colorValue(item?.openTrades)
                }"
              >
                {{ item?.openTrades | profitsNoDecimal }}
              </div>
              <div class="closedTrades">Trades chiusi</div>
              <div
                class="closedTrades-value"
                [ngStyle]="{
                  color: colorValue(item?.closedTrades)
                }"
              >
                {{ item?.closedTrades | profitsNoDecimal }}
              </div>
              <div class="final">Valore al 31/12</div>
              <div class="final-value">{{ item?.end | depositsNoDecimal }}</div>
              <div class="profit">Utile</div>
              <div
                class="profit-value"
                [ngStyle]="{
                  color: colorValue(item?.netProfit)
                }"
              >
                {{ item?.profit | profitsNoDecimal }}
              </div>
              <div class="taxes">Tasse</div>
              <div class="taxes-value">
                {{ item?.taxes | depositsNoDecimal }}
              </div>
              <div class="netProfit">Utile Netto</div>
              <div
                class="netProfit-value"
                [ngStyle]="{
                  color: colorValue(item?.netProfit)
                }"
              >
                {{ item?.netProfit | profitsNoDecimal }}
              </div>
              <div class="netProfitPerc">Utile Netto %</div>
              <div
                class="netProfitPerc-value"
                [ngStyle]="{
                  color: colorValue(item?.netProfit)
                }"
              >
                {{ item?.netProfitPerc | profitsPercSimulator }}
              </div>
            </div>
          }
        }
      </div>
    </div>
  </div>
</div>
