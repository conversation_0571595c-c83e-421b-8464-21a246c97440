import { Component, input } from '@angular/core';

@Component({
  selector: 'app-modal',
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.scss',
})
export class ModalComponent {
  modalContentTitle = input.required<string>();
  modalContentText = input.required<string>();
  modalCounter = input.required<number>();
  // onButtonClick = output<boolean>();

  constructor() {}
  // buttonClick() {
  //   this.onButtonClick.emit(true);
  // }
}
