<div class="container">
  @if (!loading()) {
    @if (!isProduction && currentAccount !== "pac") {
      <app-add-deposit></app-add-deposit>
    }

    <div class="info">
      <div class="info-text">
        <div class="deposits">
          <span style="color: #a2a2a2; margin-right: 0.5rem">Deposits:</span>
          {{ filteredListItemFull.length }}
        </div>
        <div class="deposits total">
          <span style="color: #a2a2a2; margin-right: 0.5rem">Total:</span>
          {{ totalDeposits | depositsNoDecimal }}
        </div>
      </div>

      <select name="yearList" id="" (change)="onSelectYear($event)">
        @for (year of yearsList; track year) {
          <option [value]="year">{{ year }}</option>
        }
      </select>
    </div>

    <div class="search">
      <select [(ngModel)]="selectCoin" (ngModelChange)="coinFilter($event)">
        <option value="all">All Crypto ({{ filteredListItem.length }})</option>
        @for (item of filteredListItem; track item) {
          <option [value]="item">
            {{ item }}
          </option>
        }
      </select>
    </div>

    @for (item of filteredList; track item) {
      <div class="card">
        <div class="card-date">{{ item?.date | date }}</div>
        @for (coin of item.value; track coin.ticker) {
          <div class="card-grid">
            <div class="logo">
              <img [src]="findCoinImg(coin?.ticker)" />
            </div>
            <div class="name">{{ coin?.crypto }}</div>
            <div class="ticker">{{ coin?.ticker | uppercase }}</div>
            <div class="deposit">
              {{ "+ " + (coin?.totalDeposit | depositsNoDecimal) }}
            </div>
            <div class="fees">{{ coin?.fees | depositsNoDecimal }}</div>
            <div class="quantity">
              {{ "+ " + (coin?.quantity | number: "0.2-5" : "it-IT") }}
            </div>
            @if (coin?.ticker === "btc" || coin?.ticker == "eth") {
              <div class="avgPrice">
                {{ coin?.avgPrice | depositsNoDecimal }}
              </div>
            } @else {
              <div class="avgPrice">{{ coin?.avgPrice | deposits }}</div>
            }
          </div>
        }
      </div>
    }
  }
</div>
