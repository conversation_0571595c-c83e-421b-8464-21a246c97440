import { formatCurrency } from '@angular/common';
import { Component, effect, input, signal, untracked } from '@angular/core';
import { PortfolioCoinbaseFra } from 'src/app/core/services/data/coinbase-fra.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-ath',
  templateUrl: './ath.component.html',
  styleUrls: ['./ath.component.scss'],
})
export class AthComponent {
  protected showInfo = signal<boolean>(false);
  protected priceInput: string;
  protected priceInputNumber: number | string;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected currentCoin = signal<string>('');
  protected profits: string;
  protected currentSorting = signal<string>('athPerc');
  protected totalProfitATH: number;

  portfolio = input<PortfolioCoinbaseFra>();

  constructor(private currentAccountService: CurrentAccountService) {
    effect(() => {
      this.currentAccountService.currentAccount()
        ? untracked(() => this.showInfo.set(false))
        : null;
    });
  }

  onInfoClick(value) {
    if (!this.currentCoin()) this.showInfo.set(true);

    let currentCoinValue = (
      value.target.parentElement.children[1]
        ? value.target.parentElement.children[1].textContent
        : value.target.parentElement.parentElement.children[1].textContent
    ).trim();

    this.currentCoin() == currentCoinValue
      ? this.showInfo.update((prev) => !prev)
      : this.showInfo.set(true);

    this.currentCoin.set(currentCoinValue);

    this.depositsInput = '';
    this.priceInput = '';
    this.priceInputNumber = '';
  }

  onInputChange(element) {
    let elementNumber: any;
    if (element.includes(',')) {
      this.priceInputNumber = element.replaceAll('.', '').replace(',', '.');

      return;
    }

    elementNumber = element.replaceAll('.', '');
    this.priceInputNumber = elementNumber;

    let formattedValue = elementNumber.replaceAll('.', '');

    this.priceInput = formatCurrency(
      formattedValue,
      'it-IT',
      '',
      'EUR',
      '0.0-2',
    );
  }

  onInputCompleted(element) {
    this.priceInput = '€ ' + element.target.value;
  }

  currentFilter() {
    if (this.currentSorting() == 'athPerc')
      return this.portfolio()?.sortAthPerc;
    if (this.currentSorting() == 'athProfit')
      return this.portfolio()?.sortAthProfit;
    if (this.currentSorting() == 'athDate')
      return this.portfolio()?.sortAthDate;
  }
}
