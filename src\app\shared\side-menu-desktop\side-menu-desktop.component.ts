import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/core/services/auth/auth.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

export enum MenuType {
  HOME = 'home',
  NEWS = 'news',
  ANNUALREPORT = 'annualReport',
  DEPOSITHISTORY = 'depositHistory',
  CRYPTOVOLUMES = 'cryptoVolumes',
  ALTCOINSEASON = 'altcoinSeason',
  BLOCKCHAINSTATS = 'blockchainStats',
  TOKENSTATS = 'tokenStats',
  LOGOUT = 'logout',
}

export enum UrlToMenuType {
  '/' = MenuType.HOME,
  '/news' = MenuType.NEWS,
  '/account-detail' = MenuType.ANNUALREPORT,
  '/account-deposits' = MenuType.DEPOSITHISTORY,
  '/crypto-volumes' = MenuType.CRYPTOVOLUMES,
  '/altcoin-season' = MenuType.ALTCOINSEASON,
  '/blockchain-stats' = MenuType.BLOCKCHAINSTATS,
  '/token-stats' = MenuType.TOKENSTATS,
}

@Component({
  selector: 'app-side-menu-desktop',
  templateUrl: './side-menu-desktop.component.html',
  styleUrl: './side-menu-desktop.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SideMenuDesktopComponent {
  protected addedNews = this.tokeninsightService.addedNews;
  protected currentPage = signal<string>('');
  protected currentUser = this.currentAccountService.currentUser;
  protected menuType = MenuType;
  protected currentMenuSelected = signal<MenuType | undefined>(undefined);

  constructor(
    private tokeninsightService: TokeninsightService,
    private currentAccountService: CurrentAccountService,
    private router: Router,
    private activatedRoute: Router,
    private authService: AuthService,
  ) {}

  ngOnInit(): void {
    console.log('url', this.activatedRoute.url);
    this.currentMenuSelected.set(
      UrlToMenuType[this.activatedRoute.url.toString()],
    );
  }

  handleMenuClick(page: MenuType) {
    this.currentMenuSelected.set(page);
    switch (page) {
      case MenuType.HOME:
        this.router.navigateByUrl('/');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.NEWS:
        this.router.navigateByUrl('/news');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.ANNUALREPORT:
        this.router.navigateByUrl('/account-detail');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.DEPOSITHISTORY:
        this.router.navigateByUrl('/account-deposits');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.CRYPTOVOLUMES:
        this.router.navigateByUrl('/crypto-volumes');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.ALTCOINSEASON:
        this.router.navigateByUrl('/altcoin-season');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.BLOCKCHAINSTATS:
        this.router.navigateByUrl('/blockchain-stats');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.TOKENSTATS:
        this.router.navigateByUrl('/token-stats');
        this.tokeninsightService.addedNews.set([]);
        break;
      case MenuType.LOGOUT:
        this.tokeninsightService.loadingNews$.next(null);
        this.tokeninsightService.loading$.next(null);
        this.authService.SignOut();
        setTimeout(() => {
          location.reload();
        }, 1000);
        break;
      default:
        break;
    }
  }
}
