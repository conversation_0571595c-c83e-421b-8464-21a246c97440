<div class="header-buttons">
  <div
    class="crypto-list-title"
    [class]="{
      selected: currentheaderFilter() === 'portfolio'
    }"
    (click)="currentheaderFilter.set('portfolio')"
  >
    Portfolio
  </div>
  <div
    class="crypto-list-title"
    [class]="{
      selected: currentheaderFilter() === 'closedTrades'
    }"
    (click)="currentheaderFilter.set('closedTrades')"
  >
    Closed Trades
  </div>
</div>

@if (currentheaderFilter() === "portfolio") {
  <app-portfolio [portfolio]="portfolio()"></app-portfolio>
} @else {
  <app-closed-trades></app-closed-trades>
}
