select.selectCoin {
  width: 100%;
  height: 40px;
  font-size: 1.6rem;
  border: none;
  border-radius: 5px;
  padding: 0 1rem;
  padding-right: 1rem;
  margin-top: 1rem;
  color: black;
}

.crypto-list-table {
  display: grid;
  grid-template-columns: 37px 54% auto;
  grid-template-rows: auto 17px 17px auto;
  margin-top: 1.2rem;
  width: 100%;
  // border-bottom: 1px solid #282828;
  padding-bottom: 0.6rem;
  border: 1px solid #505050;
  padding: 1rem;
  border-radius: 10px;
  background-color: rgb(16, 16, 16);

  &-logo {
    grid-row: 1/3;
    grid-column: 1/2;
    justify-self: center;
    align-self: center;
    width: 100%;
    height: 37px;

    & img {
      width: 100%;
      height: 100%;
    }
  }

  &-name {
    grid-row: 1/2;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.6rem;
    padding-left: 0.7rem;
    padding-right: 0.7rem;
    font-weight: 500;
    display: flex;
    align-items: center;

    &-rating {
      font-size: 1.4rem;
      border-radius: 10px;
      font-weight: 500;
      width: 50px;
      margin-left: 0.5rem;
      text-align: center;
      letter-spacing: 1px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    & select {
      width: 100%;
      font-size: 1.6rem;
      padding: 0.5rem;
    }
  }

  &-ticker {
    grid-row: 2/3;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.2rem;
    padding-left: 0.7rem;
    color: #5b5b5b;
    font-weight: 500;
    padding-bottom: 0.3rem;
  }

  &-info {
    grid-row: 3/-1;
    grid-column: 1/-1;
    margin-top: 1rem;
    font-size: 1.6rem;
    font-weight: 300;
    line-height: 23px;
    text-align: justify;
    hyphens: auto;
    color: #d0d0d0;

    &-rating {
      display: grid;
      grid-template-columns: 54% auto;
      grid-template-rows: auto auto auto;
      color: white;
      font-weight: 500;
      background-color: #262626;
      padding: 1rem;
      border-radius: 5px;
      font-size: 1.4rem;
      margin-bottom: 1rem;

      & .security,
      .economics,
      .progress,
      .performance {
        display: flex;
        justify-self: start;
        align-self: center;

        & .icon {
          width: 14px;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 1rem;
        }
      }
      & .ratingDescription {
        grid-column: 1/-1;
        grid-row: 3/4;
        margin-top: 1rem;
        line-height: 19px;
      }
    }
  }

  &-category {
    grid-row: 1/3;
    grid-column: 3/4;
    justify-self: end;
    align-self: start;
    font-size: 1.6rem;
    text-align: center;
    font-weight: 500;
  }

  &-marketCap {
    grid-row: 2/3;
    grid-column: 3/4;
    justify-self: end;
    align-self: start;
    font-size: 1.4rem;
    font-weight: 500;
    color: #5b5b5b;
  }
}
