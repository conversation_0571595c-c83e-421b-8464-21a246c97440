@if (!showForm && currentAccount !== "pac") {
  <div class="addDeposits" (click)="onShowFormClick()">
    ADD DEPOSIT<i class="fa-solid fa-plus"></i>
  </div>
}
@if (showForm && currentAccount !== "pac") {
  <form
    [formGroup]="addDepositForm"
    class="depositsForm"
    (ngSubmit)="onFormSubmit()"
  >
    <div class="input-group">
      <label for="account">Account</label>
      <input
        [(ngModel)]="currentAccountText"
        type="text"
        formControlName="account"
        id="account"
      />
    </div>

    <div class="input-group">
      <label for="date">Date</label>
      <input type="date" formControlName="date" id="date" />
    </div>

    <div class="input-group">
      <label for="ticker">Ticker</label>
      <input type="text" formControlName="ticker" id="ticker" />
    </div>

    <div class="input-group">
      <label for="crypto">Crypto</label>
      <input
        type="text"
        formControlName="crypto"
        id="crypto"
        [value]="crypto"
      />
    </div>

    <div class="input-group">
      <label for="deposit">Deposit</label>
      <input type="number" formControlName="totalDeposit" id="totalDeposit" />
    </div>

    <div class="input-group">
      <label for="fees">Fees</label>
      <input type="number" formControlName="fees" id="fees" />
    </div>

    <div class="input-group">
      <label for="quantity">Quantity</label>
      <input type="number" formControlName="quantity" id="quantity" />
    </div>

    <div class="input-group">
      <label for="avgPrice">Average Price</label>
      <input
        type="text"
        formControlName="avgPrice"
        id="avgPrice"
        [value]="avgPrice"
      />
    </div>

    <div class="buttons">
      <button class="cancel" (click)="onCloseClick()">CLOSE</button>
      <button class="confirm" type="submit">CONFIRM</button>
    </div>
  </form>
}
