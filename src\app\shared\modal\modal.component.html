<div class="container">
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">
        {{ modalContentTitle() }}
      </h3>
    </div>
    <div class="modal-body">
      <div class="loader-container">
        <app-loader-spinner [showBg]="false"></app-loader-spinner>
      </div>
      <p class="modal-text">
        {{ modalContentText() }}
      </p>

      <!-- <div class="button-container">
        <button
          class="update-button"
          (click)="buttonClick()"
          [disabled]="modalCounter() === 0"
        >
          <span
            >Aggiorna
            {{ modalCounter() > 0 ? "(" + modalCounter() + ")" : "" }}</span
          >
        </button>
      </div> -->
    </div>
  </div>
</div>
