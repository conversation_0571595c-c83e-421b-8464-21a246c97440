import { Component, effect, signal, untracked } from '@angular/core';
import { ApexOptions } from 'apexcharts';
import { GoogleSheetService } from 'src/app/core/services/http/google-sheet.service';

@Component({
  selector: 'app-fed-probability',
  templateUrl: './fed-probability.component.html',
  styleUrl: './fed-probability.component.scss',
})
export class FedProbabilityComponent {
  protected fedProbabilty = this.googleSheetService.fedProbabilty;
  protected chartOptions: Partial<ApexOptions>;
  protected showCart = signal<boolean>(false);
  protected createChartEffect = effect(() => {
    if (!!this.fedProbabilty()) {
      untracked(() => this.createChart());
    }
  });

  constructor(private googleSheetService: GoogleSheetService) {}

  createChart() {
    this.chartOptions = {
      series: [
        {
          name: this.fedProbabilty().rate1.rateTarget,
          data: [
            {
              x: this.fedProbabilty().rate1.rateTarget,
              y: this.fedProbabilty().rate1.probabilityCurrent.slice(0, -1),
              goals: [
                {
                  name: '1 Week ago',
                  value: this.fedProbabilty()?.rate1.probabilty1Week.slice(
                    0,
                    -1,
                  ),
                  strokeWidth: 10,
                  strokeHeight: 10,
                  strokeColor: '#775DD0',
                },
              ],
            },
            {
              x: this.fedProbabilty().rate2.rateTarget,
              y: this.fedProbabilty().rate2.probabilityCurrent.slice(0, -1),
              goals: [
                {
                  name: '1 Week ago',
                  value: this.fedProbabilty()?.rate2.probabilty1Week.slice(
                    0,
                    -1,
                  ),
                  strokeWidth: 10,
                  strokeHeight: 10,
                  strokeColor: '#775DD0',
                },
              ],
            },
          ],
        },
      ],

      chart: {
        height: 130,
        width: '100%',
        type: 'bar',
        toolbar: {
          show: false,
        },
        animations: {
          enabled: false,
        },
      },
      plotOptions: {
        bar: {
          horizontal: true,
          barHeight: '65%',
          dataLabels: {
            position: 'top',
          },
        },
      },
      grid: {
        show: false,
        padding: {
          bottom: -5,
          right: 10,
          left: 14,
          top: -15,
        },
        position: 'back',
      },
      xaxis: {
        max: 100,
        stepSize: 50,
        labels: {
          show: true,
          formatter: (value) => `${value}%`,
        },
      },
      yaxis: {
        labels: {
          style: {
            // colors: ['#fff'],
            colors: ['green', 'red'],
            fontWeight: 600,
          },
          offsetY: 4,
        },
      },
      title: {
        text: `FED Meeting (${this.fedProbabilty().date})`,
        align: 'center',
        offsetX: 0,
        offsetY: 0,
        floating: false,
        style: {
          fontSize: '14px',
          fontWeight: 'bold',
          fontFamily: undefined,
          color: '#fff',
        },
      },
      colors: ['#00E396'],
      dataLabels: {
        formatter: function (val, opt): string {
          const goals =
            opt.w.config.series[opt.seriesIndex].data[opt.dataPointIndex].goals;
          // if (goals && goals.length) {
          //   return `${val} / ${goals[0].value} %`;
          // }

          if (goals && goals.length) {
            return `${val} %`;
          }
          return val.toString();
        },
        textAnchor: 'start',
        offsetX: 12,
        // background: {
        //   enabled: true,
        //   borderColor: '#775DD0',
        //   foreColor: '#000',
        // },
      },
      legend: {
        position: 'bottom',
        show: true,
        fontSize: '10px',
        fontFamily: 'Roboto',
        showForSingleSeries: true,
        labels: {
          colors: '#fff',
          useSeriesColors: false,
        },
        customLegendItems: ['Actual', '1 week ago'],
        markers: {
          shape: 'circle',
          strokeWidth: 0,
          fillColors: ['#00E396', '#775DD0'],
          offsetX: -2,
        },
      },

      tooltip: {
        // x: {
        //   format: 'dd/MM/yy',
        // },
        theme: 'dark',
      },
    };
    this.showCart.set(true);
  }
}
