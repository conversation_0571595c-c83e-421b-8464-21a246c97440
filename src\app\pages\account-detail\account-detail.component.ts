import { Component, effect } from '@angular/core';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-account-detail',
  templateUrl: './account-detail.component.html',
  styleUrls: ['./account-detail.component.scss'],
})
export class AccountDetailComponent {
  protected currentPortfolioHistory =
    this.currentAccountService.currentPortfolioHistory;
  protected showDetail = false;
  protected currentYear: number;
  protected showTaxesInfo = false;

  constructor(private currentAccountService: CurrentAccountService) {
    console.log(this.currentPortfolioHistory());
    effect(() =>
      this.currentAccountService.currentPortfolio()
        ? (this.showDetail = false)
        : null,
    );
  }

  onShowDetailClick(year) {
    if (this.currentYear == year) {
      this.showDetail = !this.showDetail;
    } else {
      this.currentYear = year;
      this.showDetail = true;
    }
  }

  colorValue(value) {
    if (value == 0) return null;
    if (value > 0) return 'var(--green-profit)';
    if (value < 0) return 'red';
  }
}
