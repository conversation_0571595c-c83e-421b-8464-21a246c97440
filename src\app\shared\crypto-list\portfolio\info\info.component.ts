import { Component, Input, effect, signal } from '@angular/core';
import { PortfolioBinanceAndrea } from 'src/app/core/services/data/binance-andrea.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { TokeninsightService } from 'src/app/core/services/http/tokeninsight.service';

@Component({
  selector: 'app-info',
  templateUrl: './info.component.html',
  styleUrl: './info.component.scss',
})
export class InfoComponent {
  @Input('portfolio') portfolio!: any;
  protected searchCoin = signal(null);
  protected loading = true;
  protected ratingDescription = [
    {
      rating: 'AAA',
      description: `Dimostra un'eccezionale tecnologia di base e una solida sicurezza
        Misure. Il progetto ha vantato un ecosistema consistente e fiorente
        e ha attirato l'attenzione del settore. Più metriche automatizzate
                affermano l'eccezionale qualità del progetto e il minimo rischio.`,
    },
    {
      rating: 'AA',
      description: `Presenta una solida tecnologia di base e un elevato livello di sicurezza. Il progetto ha goduto di un ecosistema ampio e fiorente e ha attirato una notevole attenzione da parte del settore. Numerosi parametri automatizzati confermano la sana traiettoria di sviluppo del progetto con alta qualità e basso rischio.`,
    },
    {
      rating: 'A',
      description: `Presenta un'infrastruttura tecnologica sicura e affidabile. Il progetto ha raccolto una notevole attenzione da parte del settore e vanta un ecosistema sostanziale, stabile e diversificato. Molteplici metriche automatizzate indicano uno sviluppo sano del progetto con un rischio relativamente basso.`,
    },
    {
      rating: 'BBB',
      description: `Dimostra un'elevata fattibilità tecnologica e scenari applicativi relativamente ricchi, con i principali prodotti già lanciati e continuamente iterati, contribuendo a un ecosistema stabile. Numerosi parametri automatizzati riflettono l'attenzione e il supporto significativi del settore, uno sviluppo costante e rischi altamente gestibili.`,
    },
    {
      rating: 'BB',
      description: `Presenta una buona fattibilità tecnologica e diversi scenari applicativi, con prodotti importanti già lanciati e un ecosistema in fase iniziale in atto. Numerosi parametri automatizzati indicano un notevole livello di attenzione e supporto da parte del settore, uno sviluppo stabile e rischi gestibili relativamente elevati.`,
    },
    {
      rating: 'B',
      description: `Vanta un solido quadro tecnologico e scenari applicativi ibridi, e parziali grandi prodotti già lanciati. Numerosi parametri automatizzati indicano un certo livello di attenzione e supporto del settore, con rischi gestibili.`,
    },
    {
      rating: 'CCC',
      description: `Possiede un quadro tecnologico ragionevole, ma è ancora in fase di sviluppo o ha prodotti in fase iniziale di utilizzo, anche se con scenari applicativi limitati. Numerosi parametri automatizzati suggeriscono un’attenzione moderata da parte del mercato, mentre lo sviluppo sostenibile a lungo termine comporta incertezze, con conseguente livello di rischio moderatamente elevato.`,
    },
    {
      rating: 'CC',
      description: `Presenta progressi limitati nello sviluppo, problemi relativi al quadro tecnologico, scenari applicativi limitati e divulgazione inadeguata o poco chiara. Numerosi parametri automatizzati indicano una mancanza di attenzione del mercato/supporto della comunità, con un livello di rischio relativamente elevato.`,
    },
    {
      rating: 'C',
      description: `Si trova ad affrontare sfide significative legate al quadro tecnologico, scenari applicativi limitati o stagnazione a lungo termine nello sviluppo. Divulgazione insufficiente per dimostrare lo sviluppo corretto e sostenibile del progetto. Numerosi parametri automatizzati indicano una mancanza di attenzione del mercato, con conseguente elevato livello di rischio.`,
    },
    {
      rating: 'D',
      description: `Numerosi parametri automatizzati indicano che i servizi del progetto sono terminati o che i servizi non possono essere forniti correttamente, il che significa un livello di rischio estremamente elevato.`,
    },
  ];
  constructor(
    protected portfolio2: PortfolioBinanceAndrea,
    private currentAccountService: CurrentAccountService,
    private tokeninisghtService: TokeninsightService
  ) {
    this.searchCoin.set(
      this.currentAccountService.currentPortfolio().sortRank[0]
    );

    this.tokeninisghtService.loading$.subscribe((data) => {
      if (data == 'ok') {
        this.loading = false;
        console.log('LOADED INFO', this.loading);
      }
    });

    effect(
      () => {
        this.currentAccountService.currentPortfolio()
          ? this.searchCoin.set(
              this.currentAccountService.currentPortfolio().sortRank[0]
            )
          : null;
      },
      { allowSignalWrites: true }
    );
  }

  find() {
    return this.portfolio2.coins.indexOf(
      this.portfolio2.coins.find((item) => item.ticker == 'FET')
    );
  }

  findDescription(rating) {
    // console.log('RATING', rating);

    return this.ratingDescription.find((item) => item.rating === rating)
      ? this.ratingDescription.find((item) => item.rating === rating)
          .description
      : null;
  }

  onChangeCrypo(value) {
    this.searchCoin.set(
      this.currentAccountService
        .currentPortfolio()
        .coins.find((item) => item.name == value)
    );
  }
  ratingBackground(rating) {
    switch (rating) {
      case 'AAA':
        return 'green';
      case 'AA':
        return 'green';
      case 'A':
        return 'green';
      case 'BBB':
        return 'rgb(19, 108, 110)';
      case 'BB':
        return 'rgb(19, 108, 110)';
      case 'B':
        return 'rgb(19, 108, 110)';
      case 'CCC':
        return '#ee974d';
      case 'CC':
        return '#ee974d';
      case 'C':
        return '#ee974d';
      case 'DDD':
        return '#f8685f;';
      case 'DD':
        return '#f8685f;';
      case 'D':
        return '#f8685f;';

      default:
        return '';
    }
  }

  formatText(text: string): string {
    // Dividi il testo in frasi utilizzando il punto come delimitatore
    const sentences = text.split('. ');

    // Unisci le frasi aggiungendo <br><br> dopo ogni frase
    let formattedText = sentences.join('. <br><br>');

    // Rimuovi gli ultimi due <br>
    formattedText = formattedText.replace(/\. <br><br>$/, '');

    return formattedText;
  }
}
