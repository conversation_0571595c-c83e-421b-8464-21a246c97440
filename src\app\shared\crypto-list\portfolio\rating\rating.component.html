<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto</div>
  <div class="crypto-list-header-rating">Rating</div>
  <div class="crypto-list-header-score" (click)="currentSorting = 'score'">
    Score
    @if (currentSorting == 'score') {
      <i class="fa-solid fa-caret-down"></i>
    }
  </div>
  <div
    class="crypto-list-header-review"
    (click)="currentSorting = 'reviewTime'"
    >
    Updated
    @if (currentSorting == 'reviewTime') {
      <i
        class="fa-solid fa-caret-down"
      ></i>
    }
  </div>
</div>

@for (coin of currentSorting == 'score' ? sortScore : sortTime; track coin) {
  <div
    class="crypto"
    >
    <div
      class="crypto-list-table"
    [ngStyle]="{
      borderBottom: currentCoin == coin.name && showInfo ? 'none' : ''
    }"
      >
      <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
        <img src="{{ coin.logo }}" />
      </div>
      <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
        {{ coin.ticker }}
      </div>
      <div class="crypto-list-table-name" (click)="onInfoClick($event)">
        {{ coin.name }}
      </div>
      <div
        class="crypto-list-table-rating"
        [appRatingBackground]
        [rating]="coin.rating.level"
        >
        {{ coin.rating?.level }}
      </div>
      <div class="crypto-list-table-score">
        <div>
          {{ coin.rating?.score | number: "1.0-0" }}
        </div>
      </div>
      <div class="crypto-list-table-review">
        {{ coin.rating?.reviewTime | date: "dd/MM/yy" }}
      </div>
    </div>
    <app-crypto-info
      [coin]="coin"
      [showInfo]="showInfo"
      [currentCoin]="currentCoin"
    ></app-crypto-info>
  </div>
}
