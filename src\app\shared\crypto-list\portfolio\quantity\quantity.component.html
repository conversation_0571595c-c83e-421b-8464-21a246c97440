<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto</div>
  <div class="crypto-list-header-category">Category</div>
  <div class="crypto-list-header-profit">Quantity</div>
</div>

@for (coin of portfolio.sortDeposits; track coin) {
  <div class="crypto">
    <div class="crypto-list-table">
      <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
        <img src="{{ coin.logo }}" />
      </div>
      <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
        {{ coin.ticker }}
      </div>
      <div class="crypto-list-table-name" (click)="onInfoClick($event)">
        {{ coin.name }}
      </div>
      <div class="crypto-list-table-category">
        {{ coin.category }}
      </div>
      <div class="crypto-list-table-quantity" [ngStyle]="{ color: white }">
        {{ coin.quantity | quantity }}
      </div>
      <!-- <div
      class="crypto-list-table-gainPercent"
    [ngStyle]="{
          backgroundColor: 'darkblue',
        }"
      > -->
      <!-- <div
      class="crypto-list-table-gainPercent-number"
      [ngStyle]="{
            color: '#cecece',
          }"
      >
      {{ coin.currentPerc | profitsPerc }}
    </div> -->
  <!-- </div> -->
</div>
<app-crypto-info
  [coin]="coin"
  [showInfo]="showInfo"
  [currentCoin]="currentCoin"
></app-crypto-info>
</div>
}
