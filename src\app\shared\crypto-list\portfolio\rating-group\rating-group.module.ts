import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DirectivesModule } from 'src/app/core/utils/directives.module';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { CryptoInfoModule } from '../crypto-info/crypto-info.module';
import { RatingGroupComponent } from './rating-group.component';

@NgModule({
  declarations: [RatingGroupComponent],
  imports: [CommonModule, PipesModule, CryptoInfoModule, DirectivesModule],
  exports: [RatingGroupComponent],
})
export class RatingGroupModule {}
