.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-color: rgb(0, 0, 0);
  transition: all 0.2s ease-out;
  height: 100dvh;

  &.mobile {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .login-content {
    height: 100vh;
    width: 100vw;
    display: flex;
    width: 70%;
    max-width: 700px;
    padding: 2rem;

    &.mobile {
      width: 100%;
    }

    .image-content {
      flex-grow: 1;
      display: flex;
      position: relative;
      border-radius: 1.25rem;
      padding-left: 1.5rem;
      background-size: cover;

      &.mobile {
        display: none;
      }
    }

    .content {
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgb(40, 40, 40);
      padding-right: 0;
      border-radius: 1.25rem;
      flex-grow: 1;
      position: relative;
      padding: 3rem;

      .container {
        display: flex;
        flex-direction: column;
        padding: 3rem;
        scroll-behavior: smooth;
        overflow-y: auto;
        position: relative;

        & .logo {
          display: flex;
          justify-content: center;
          flex-direction: column;
          font-size: 3rem;
          font-weight: 500;
          align-items: center;
          margin-bottom: 3rem;

          & img {
            margin-bottom: 3rem;
          }
        }

        & .title {
          padding: 1rem 0;
          font-size: 1.4rem;
          & i {
            margin-right: 1rem;
          }
        }

        & input {
          width: 300px;
          height: 35px;
          margin-bottom: 2rem;
          padding: 1rem;
          border-radius: 10px;
          border: none;
        }

        & button {
          height: 35px;
          background-color: rgb(0, 98, 202);
          border: none;
          border-radius: 10px;
          cursor: pointer;
          font-size: 1.6rem;
          font-weight: 500;
          margin-top: 3rem;
          color: white;
          display: flex;
          justify-content: center;
          align-items: center;

          &:hover {
            background-color: rgb(0, 72, 150);
          }
        }

        & .invalid {
          color: rgb(223, 0, 0);
          font-size: 1.6rem;
          font-weight: 500;
          margin-bottom: 1rem;

          &.auth {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

.loader {
  font-size: 4px;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: relative;
  text-indent: -9999em;
  animation: mulShdSpin 1.1s infinite ease;
  transform: translateZ(0);
  display: flex;
}
@keyframes mulShdSpin {
  0%,
  100% {
    box-shadow:
      0em -2.6em 0em 0em #ffffff,
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.5),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.7);
  }
  12.5% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.7),
      1.8em -1.8em 0 0em #ffffff,
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.5);
  }
  25% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.5),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.7),
      2.5em 0em 0 0em #ffffff,
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  37.5% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.5),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.7),
      1.75em 1.75em 0 0em #ffffff,
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.5),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.7),
      0em 2.5em 0 0em #ffffff,
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.2),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  62.5% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.5),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.7),
      -1.8em 1.8em 0 0em #ffffff,
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  75% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.5),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.7),
      -2.6em 0em 0 0em #ffffff,
      -1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2);
  }
  87.5% {
    box-shadow:
      0em -2.6em 0em 0em rgba(255, 255, 255, 0.2),
      1.8em -1.8em 0 0em rgba(255, 255, 255, 0.2),
      2.5em 0em 0 0em rgba(255, 255, 255, 0.2),
      1.75em 1.75em 0 0em rgba(255, 255, 255, 0.2),
      0em 2.5em 0 0em rgba(255, 255, 255, 0.2),
      -1.8em 1.8em 0 0em rgba(255, 255, 255, 0.5),
      -2.6em 0em 0 0em rgba(255, 255, 255, 0.7),
      -1.8em -1.8em 0 0em #ffffff;
  }
}

@media screen and (max-width: 768px) {
  .login-container {
    .login-content {
      width: 100%;

      & .content {
        padding: 1rem;
        align-items: flex-start;

        & .container {
          padding: 1rem;
          margin-top: 5rem;

          & input {
            width: 250px;
          }

          & .logo {
            & img {
              margin-bottom: 4rem;
            }
          }
        }
      }
    }
  }
}
