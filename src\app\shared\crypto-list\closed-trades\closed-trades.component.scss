.container {
  padding: 1rem;
  min-height: 500px;

  .info {
    display: flex;
    width: 100%;
    margin-bottom: 2rem;
    // background-color: #005382;
    border-radius: 10px;
    border: 1px solid gray;

    &-text {
      display: flex;
      justify-content: space-around;
      font-size: 1.6rem;
      width: 100%;
      margin-left: -10px;

      & .deposits {
        display: flex;
        justify-content: start;
        align-items: center;
        padding: 1rem 0.5rem;
        border-radius: 10px;
        width: auto;
        letter-spacing: 0.2px;
      }
    }

    & select {
      font-size: 1.6rem;
      padding: 1rem 0.5rem;
      border-radius: 10px;
      border: none;
      background-color: black;
      color: #fff;
      width: 122px;
      text-align: center;
      margin-right: 1rem;
      cursor: pointer;
    }
  }

  .card {
    margin-bottom: 2rem;

    &-date {
      font-size: 1.6rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      margin-left: 0.5rem;
      color: #c7c7c7;
      font-size: 1.6rem;
    }

    &-grid {
      padding: 1rem;
      display: grid;
      grid-template-columns: 35px 36% auto 26%;
      grid-template-rows: 50% 50%;
      width: 100%;
      background-color: #1f1f1f;
      border-radius: 10px;
      font-size: 1.6rem;
      margin-bottom: 1rem;

      & .logo {
        grid-column: 1/2;
        grid-row: 1/3;
        justify-self: center;
        align-self: center;
        width: 100%;

        & img {
          width: 100%;
          height: 100%;
          margin-top: 0.3rem;
          border-radius: 50%;
          // margin-left: 0.2rem;
        }
      }

      & .name {
        grid-row: 1/2;
        grid-column: 2/3;
        justify-self: start;
        align-self: end;
        padding-left: 0.7rem;
      }

      & .ticker {
        display: flex;
        grid-row: 2/3;
        grid-column: 2/3;
        justify-self: start;
        align-self: start;
        padding-left: 0.7rem;
        color: #676767;
        font-size: 1.4rem;

        .quantity {
          color: #676767;
          // grid-row: 2/3;
          // grid-column: 2/3;
          // padding-left: 0.7rem;
          // justify-self: start;
          // align-self: start;
          // font-size: 1.4rem;

          .leverage {
            color: lightblue;
            background: #4b4b4b;
            padding: 1.5px 3px;
            border-radius: 5px;
            font-weight: 500;
            margin-left: 2px;
          }
        }
      }

      & .profit {
        grid-column: 4/5;
        grid-row: 1/2;
        // color: rgb(0, 188, 0);
        font-weight: 500;
        align-self: end;
        justify-self: end;
        // font-size: 1.6rem;
      }

      & .profitPerc {
        grid-column: 4/5;
        grid-row: 2/3;
        display: flex;
        flex-direction: row;
        background-color: rgba(0, 100, 0, 0.6);
        color: #04dc00;
        font-weight: 500;
        border-radius: 15px;
        padding: 0.15rem 0.8rem;
        align-self: end;
        justify-self: end;

        &-icon {
          margin-right: 0.45rem;

          & i {
            display: flex;
            align-items: center;
            padding-top: 0.25rem;
            font-size: 1rem;
          }
        }

        &-number {
          font-size: 1.2rem;
        }
      }

      & .purchase {
        grid-row: 2/3;
        grid-column: 3/4;
        justify-self: end;
        align-self: start;
        color: #c07c00;
        // font-size: 1.4rem;
      }

      & .revenue {
        grid-row: 1/2;
        grid-column: 3/4;
        justify-self: end;
        align-self: end;
        color: green; // font-size: 1.4rem;
      }
    }
  }

  .empty {
    padding: 0.5rem;
    font-size: 1.4rem;
  }
}
