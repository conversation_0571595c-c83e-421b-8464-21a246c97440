<div class="bg" [ngClass]="{ edit: editMode }"></div>
<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto</div>
  <div class="crypto-list-header-deposit">
    Deposit
    <i class="fa-solid fa-chevron-down"></i>
  </div>
  <div class="crypto-list-header-quantity">Quantity</div>
  <div class="crypto-list-header-edit">Edit</div>
</div>

<div class="crypto-list-content">
  @for (coin of currentPortfolio().sortDeposits; track coin) {
    <div class="crypto">
      <div
        class="crypto-list-table"
        [ngClass]="{ edit: editMode && currentCoin == coin.name }"
        >
        <div class="crypto-list-table-logo">
          <img src="{{ coin?.logo }}" />
        </div>
        <div class="crypto-list-table-name">
          {{ coin?.name }}
        </div>
        <div class="crypto-list-table-ticker">
          {{ coin?.ticker }}
        </div>
        <div class="crypto-list-table-deposit">
          {{ coin?.deposits | depositsNoDecimal }}
        </div>
        <div class="crypto-list-table-quantity">
          {{ coin?.quantity | quantity }}
        </div>
        <div class="crypto-list-table-edit">
          @if(editMode && currentCoin ==coin.name){
            <div style="margin-right: -1rem" (click)="onCloseEditClick()">
              <i class="fa-regular fa-circle-xmark"></i>
            </div>
          } @else {
            <div (click)="onEditClick(coin.name)">
              <i class="fa-solid fa-ellipsis-vertical"></i>
            </div>
          }
        </div>
      </div>
      @if(currentCoin == coin.name && editMode){
        <div class="crypto-info-edit">
          <table class="crypto-info-table">
            <tr>
              <td class="text">Quantity:</td>
              <td class="value">
                <input
                  type="number"
                  [(ngModel)]="coinQuantity"
                  [placeholder]="coin?.quantity | quantity"
                  />
                </td>
              </tr>
              <tr>
                <td class="text">Deposits:</td>
                <td class="value">
                  <input
                    type="number"
                    [(ngModel)]="coinDeposits"
                    [placeholder]="coin?.deposits | deposits"
                    /><br />
                  </td>
                </tr>
                <div class="row">
                  <button
                    class="button"
                    (click)="onSaveClick(coin)"
                    [disabled]="!coinDeposits && !coinQuantity"
                    [ngClass]="{ disabled: !coinDeposits || !coinQuantity }"
                    >
                    SAVE
                  </button>
                </div>
              </table>
              <div class="delete">
                @if(!confirmDelete && currentCoin == coin.name) {
                  <i class="fa-solid fa-trash-can" (click)="confirmDelete = true"></i>
                }
                <!-- @if(confirmDelete && currentCoin == coin.name){
                <div
                  class="delete confirmDelete"
                  [ngClass]="{ transition: confirmDelete }"
                  >
                  Delete coin?
                  <i class="fa-solid fa-circle-check" style="margin-right: 0.5rem"></i
                    ><i class="fa-regular fa-circle-xmark" style="color: green"></i>
                  </div>
                  } -->
                </div>
                <div class="confirmDelete" [ngClass]="{ transition: confirmDelete }">
                  Delete coin?
                  <i class="fa-solid fa-circle-check" style="margin: 0 0.5rem"></i
                    ><i
                    class="fa-regular fa-circle-xmark"
                    style="color: green"
                    (click)="confirmDelete = false"
                  ></i>
                </div>
              </div>
            }
            <!-- <app-crypto-info
            [coin]="coin"
            [showInfo]="editMode"
            [currentCoin]="currentCoin"
          ></app-crypto-info> -->
        </div>
      }
    </div>
