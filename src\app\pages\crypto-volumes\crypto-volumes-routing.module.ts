import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { authGuard } from 'src/app/core/guards/auth.guard';
import { CryptoVolumesComponent } from './crypto-volumes.component';

const routes: Routes = [
  { path: '', component: CryptoVolumesComponent, canActivate: [authGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CryptoVolumesRoutingModule {}
