<!-- <app-loader-spinner [loading]="loading"></app-loader-spinner> -->

@if (!loading()) {
  <div class="login-container">
    <form class="login-content" (ngSubmit)="onLogin()" #loginForm="ngForm">
      <!-- <div
      class="image-content"
      [ngStyle]="{
        'background-image': 'url(/assets/img/login.jpg)'
      }"
    ></div> -->

      <div class="content">
        <div class="container">
          <div class="logo">
            <img src="../../../assets/img/logo/btc.png" width="70" alt="" />
            LOGIN
          </div>
          <div class="title"><i class="fa-solid fa-user"></i>Username</div>
          <input
            name="username"
            type="text"
            required
            placeholder="Insert username"
            minlength="3"
            [disabled]="authLoading()"
            [(ngModel)]="username"
            (blur)="
              !loginForm.controls['username']?.valid
                ? userInput.set('invalid')
                : userInput.set('valid')
            "
          />
          @if (userInput() == "invalid" && !authLoading()) {
            <div class="invalid">Invalid username</div>
          }
          <div class="title"><i class="fa-solid fa-lock"></i>Password</div>
          <input
            name="password"
            type="password"
            required
            placeholder="Insert your password"
            minlength="6"
            [disabled]="authLoading()"
            [(ngModel)]="password"
          />
          @if (
            loginForm.controls["password"]?.touched &&
            !loginForm.controls["password"]?.valid &&
            !authLoading()
          ) {
            <div class="invalid">Invalid password</div>
          }
          @if (authError()) {
            <div class="invalid auth">Credentials are incorrect!</div>
          }

          <button type="submit" [disabled]="!loginForm.valid || authLoading()">
            @if (!authLoading()) {
              LOGIN
            }
            @if (authLoading()) {
              <span class="loader"></span>
            }
          </button>
        </div>
      </div>
    </form>
  </div>
}
