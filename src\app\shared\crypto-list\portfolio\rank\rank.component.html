<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto</div>
  <div class="crypto-list-header-price">Rank</div>
  <div class="crypto-list-header-profit">MarketCap</div>
</div>

@for (coin of portfolio.sortRank; track coin) {
  <div class="crypto">
    <div class="crypto-list-table">
      <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
        <img src="{{ coin.logo }}" />
      </div>
      <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
        {{ coin.ticker }}
      </div>
      <div class="crypto-list-table-name" (click)="onInfoClick($event)">
        {{ coin.name }}
      </div>
      <div class="crypto-list-table-perc">
        {{ coin.rank }}
      </div>
      <div class="crypto-list-table-gain" [ngStyle]="{ color: white }">
        {{ coin.marketCap | shortNumber }}
      </div>
    </div>
    <app-crypto-info
      [coin]="coin"
      [showInfo]="showInfo"
      [currentCoin]="currentCoin"
    ></app-crypto-info>
  </div>
}
