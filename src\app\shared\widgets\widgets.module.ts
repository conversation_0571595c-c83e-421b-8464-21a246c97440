import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PipesModule } from '../../core/utils/pipes.module';
import { BitcoinEtfModule } from '../bitcoin-etf/bitcoin-etf.module';
import { FedProbabilityModule } from '../fed-probability/fed-probability.module';
import { WidgetsComponent } from './widgets.component';

@NgModule({
  declarations: [WidgetsComponent],
  imports: [
    CommonModule,
    PipesModule,
    RouterModule,
    BitcoinEtfModule,
    FedProbabilityModule,
  ],
  exports: [WidgetsComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class WidgetsModule {}
