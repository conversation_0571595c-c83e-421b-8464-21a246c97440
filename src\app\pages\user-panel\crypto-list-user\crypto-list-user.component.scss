.bg {
  display: none;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: black;
  opacity: 0.7;
  z-index: 998;

  &.edit {
    display: block;
  }
}

.crypto-list-header {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 30px 34% 18% 24% auto;
  width: 100%;
  margin: 1rem 0 1rem 0;
  font-size: 1.4rem;
  color: #5b5b5b;
  // border-bottom: 1px solid #282828;

  &-name,
  &-quantity,
  &-profit,
  &-deposit {
    align-self: center;
  }

  &-name {
    grid-column: 2/3;
    padding-left: 0.7rem;
  }

  &-quantity {
    grid-column: 4/5;
    justify-self: end;
  }

  &-deposit {
    grid-column: 3/4;
    justify-self: end;
  }

  &-edit {
    grid-column: 5/6;
    justify-self: end;
  }
}

.crypto-list-content {
  height: calc(100vh - 145px);
  overflow-y: auto;

  .crypto-list-table {
    display: grid;
    grid-template-rows: 17px 17px;
    grid-template-columns: 30px 34% 18% 24% auto;
    margin-top: 1.2rem;
    width: 100%;
    row-gap: 0.3rem;
    // border-bottom: 1px solid #1b1b1b;
    padding-bottom: 0.6rem;
    // border-radius: 15px;
    // background-color: rgb(17, 17, 17);
    // padding: 1rem;

    &:last-child {
      padding-bottom: 0;
    }

    &-logo {
      grid-row: 1/3;
      grid-column: 1/2;
      justify-self: center;
      align-self: center;
      width: 100%;
      // height: 100%;
      margin-top: 0.3rem;
      margin-left: 0.2rem;

      & img {
        width: 100%;
        height: 100%;
      }
    }

    &-name {
      grid-row: 1/2;
      grid-column: 2/3;
      justify-self: start;
      align-self: center;
      font-size: 1.6rem;
      padding-left: 0.7rem;
    }

    &-ticker {
      grid-row: 2/3;
      grid-column: 2/3;
      justify-self: start;
      align-self: center;
      font-size: 1.4rem;
      padding-left: 0.7rem;
      color: #5b5b5b;
    }

    &-deposit {
      grid-row: 1/2;
      grid-column: 3/4;
      align-self: center;
      justify-self: end;
      font-size: 1.4rem;

      & input[type="number"] {
        -moz-appearance: textfield;
      }

      & input {
        width: 100%;
        -webkit-appearance: none;
        text-align: end;
        padding: 0 0.5rem;
        font-size: 1.4rem;
      }
    }

    &-quantity {
      grid-row: 1/2;
      grid-column: 4/5;
      justify-self: end;
      align-self: center;
      font-size: 1.4rem;
    }

    &-edit {
      grid-row: 1/2;
      grid-column: 5/6;
      justify-self: end;
      align-self: center;
      font-size: 1.6rem;
      cursor: pointer;
      color: #1773c4;
      margin-right: 1rem;
    }

    &.edit {
      background: rgb(36, 36, 36);
      border-radius: 10px;
      border-bottom-right-radius: 10px;
      border-bottom-left-radius: 10px;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      padding: 1rem 1rem 0 1rem;
      z-index: 999;
    }
  }

  & .crypto-info-edit {
    display: flex;
    justify-content: space-between;
    background-color: rgb(36, 36, 36);
    border-radius: 10px;
    border-top-right-radius: 10px;
    font-size: 1.2rem;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    display: grid;
    grid-template-columns: 135px auto;
    padding: 0 1rem 1rem 1rem;
    position: relative;
    z-index: 999;

    & .crypto-info-table {
      width: 100%;
      grid-column: 2/3;
      width: 201px;

      & tr {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        font-size: 1.4rem;

        &:last-child {
          margin-bottom: 0;
        }
      }

      & input[type="number"] {
        -moz-appearance: textfield;
      }

      & input {
        width: 120px;
        -webkit-appearance: none;
        text-align: end;
        padding: 0 0.5rem;
        font-size: 1.4rem;
      }

      & .row {
        display: grid;
        grid-template-columns: auto 120px;

        & .button {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: green;
          font-size: 1.4rem;
          border-radius: 2px;
          grid-column: 2/3;
          border: 1px solid gray;
          cursor: pointer;
          color: white;

          &.disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }
    & .delete {
      grid-column: 1/2;
      grid-row: 1/2;
      align-self: end;
      justify-self: start;
      margin-bottom: 0.2rem;
      margin-left: 0.2rem;
      color: #a72323;
      font-size: 1.6rem;
      cursor: pointer;
    }

    & .confirmDelete {
      grid-column: 1/2;
      grid-row: 1/2;
      align-self: end;
      justify-self: start;
      margin-bottom: 0.2rem;
      margin-left: 0.2rem;
      color: #a72323;
      font-size: 1.6rem;
      cursor: pointer;
      transition: 3s ease-in;
      background: #ffb6a1;
      padding: 0.2rem 0.5rem;
      border-radius: 5px;
      font-size: 1.4rem;
      position: absolute;
      left: -120%;
      bottom: 0;
      width: auto;
      display: hidden;
      align-items: center;
      transition: 1s ease-in;

      &.transition {
        display: flex;
        left: 0;
        transition: 0.7s ease-in;
      }
    }
  }
}

.crypto {
  display: flex;
  flex-direction: column;
}

@media screen and (min-width: 768px) {
  .crypto-list-header {
    padding-right: 2rem;
  }
}
