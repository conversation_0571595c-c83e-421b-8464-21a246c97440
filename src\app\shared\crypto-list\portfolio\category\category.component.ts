import { Component, Input } from '@angular/core';
import { PortfolioBinanceAndrea } from 'src/app/core/services/data/binance-andrea.service';

@Component({
  selector: 'app-category',
  templateUrl: './category.component.html',
  styleUrl: './category.component.scss',
})
export class CategoryComponent {
  @Input('portfolio') portfolio: any;
  protected showInfo: boolean = false;
  protected currentCoin: string = '';
  protected priceInput: string;
  protected priceInputNumber: number | string;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected expand: boolean = false;
  protected currentCategories = [];

  constructor(private portfolio2: PortfolioBinanceAndrea) {}

  onInfoClick(value) {
    if (!this.currentCoin) this.showInfo = true;

    let currentCoinValue = (
      value.target.parentElement.children[1]
        ? value.target.parentElement.children[1].textContent
        : value.target.parentElement.parentElement.children[1].textContent
    ).trim();

    this.currentCoin == currentCoinValue
      ? (this.showInfo = !this.showInfo)
      : (this.showInfo = true);

    this.currentCoin = currentCoinValue;

    this.depositsInput = '';
    this.priceInput = '';
    this.priceInputNumber = '';
  }

  toggleCategory(categoryName: string): void {
    const index = this.currentCategories.indexOf(categoryName);

    if (index !== -1) {
      // Se l'elemento è presente, lo rimuoviamo
      this.currentCategories.splice(index, 1);
    } else {
      // Se l'elemento non è presente, lo aggiungiamo
      this.currentCategories.push(categoryName);
    }
  }

  onHeaderCategoriesClick() {
    if (this.currentCategories.length == 0) {
      this.currentCategories = this.portfolio.sortCategory.map(
        (item) => item.name
      );
    } else {
      this.currentCategories = [];
    }
  }
}
