<div class="crypto-chart">
  <div class="crypto-chart-title">Stats</div>
  <!-- Buttons -->
  <div class="crypto-chart-buttons">
    <div
      class="crypto-chart-buttons-balance"
      [ngClass]="{ selected: currentChartFilter == 'balance' }"
      (click)="onBalanceClick()"
    >
      Balance
    </div>
    <div
      class="crypto-chart-buttons-holdings"
      [ngClass]="{ selected: currentChartFilter == 'holdings' }"
      (click)="onHoldingsClick()"
    >
      Holdings
    </div>
    <div
      class="crypto-chart-buttons-holdings"
      [ngClass]="{ selected: currentChartFilter == 'monthly' }"
      (click)="onMonthlyClick()"
    >
      Monthly
    </div>
    <!-- <div
      class="crypto-chart-buttons-holdings"
      [ngClass]="{ selected: currentChartFilter == 'yearly' }"
      (click)="onYearlyClick()"
    >
      Yearly<i
        class="fa-regular fa-arrow-up-right-from-square"
        style="margin-left: 0.5rem"
      ></i>
    </div> -->
  </div>
  <!-- Buttons END -->
</div>

<!-- Balance Chart -->
@if (currentChartFilter == "balance") {
  <app-linechart [maxY]="maxY" [minY]="minY"></app-linechart>
}
<!-- Balance Chart END -->

<!-- Holdings Chart -->
@if (currentChartFilter == "holdings") {
  <app-piechart></app-piechart>
}

<!-- Holdings Chart END-->

<!-- Monthly Chart -->
@if (currentChartFilter == "monthly") {
  <app-columnchart></app-columnchart>
  <!-- <table class="monthly-table">
    <thead>
      <tr>
        <th>Date</th>
        <th class="current">Current</th>
        <th class="profit">Profit</th>
        <th class="profitPerc">Profit %</th>
      </tr>
    </thead>

    <tbody>
      @for (item of portfolioMonthly(); track item.date) {
        <tr>
          <td>{{ item.formatDate }}</td>
          <td class="current">{{ item.current | depositsNoDecimal }}</td>
          <td
            class="profit"
            [ngStyle]="{ color: item.profit > 0 ? 'green' : 'red' }"
          >
            {{ item.profit | profitsNoDecimal }}
          </td>
          <td
            class="profitPerc"
            [ngStyle]="{ color: item.profitPerc > 0 ? 'green' : 'red' }"
          >
            {{ item.profitPerc | profitsPercNodecimal }}
          </td>
        </tr>
      }
    </tbody>
  </table> -->
}
<!-- Monthly Chart END -->
