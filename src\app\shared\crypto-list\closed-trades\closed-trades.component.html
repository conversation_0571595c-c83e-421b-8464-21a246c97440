<div class="container">
  <div class="info">
    <div class="info-text">
      <div class="deposits">
        <span style="color: #a2a2a2; margin-right: 0.5rem">Trades:</span>
        {{ tradeListFilter().length }}
      </div>
      <div
        class="deposits total"
        [ngStyle]="{ color: totalProfit() > 0 ? 'var(--green-profit)' : 'red' }"
      >
        <span style="color: #a2a2a2; margin-right: 0.5rem">Utile:</span>
        {{ totalProfit() | profitsNoDecimal }}
      </div>
    </div>

    <select name="yearList" id="" (change)="onSelectYear($event)">
      <option value="all">Tutto</option>
      @for (year of yearList(); track year) {
        <option [value]="year" [selected]="year.toString() === '2025'">
          {{ year }}
        </option>
      }
    </select>
  </div>

  <div
    infiniteScroll
    [infiniteScrollDistance]="1"
    [infiniteScrollThrottle]="50"
    (scrolled)="onScroll()"
  >
    @for (trade of tradeListFilterSlice(); track trade.tradeNumber) {
      <div class="card">
        @if ($index === 0) {
          <div class="card-date">{{ trade?.date | date }}</div>
        }
        @if (
          $index > 0 && trade.date !== tradeListFilterSlice()[$index - 1].date
        ) {
          <div class="card-date">{{ trade.date | date }}</div>
        }

        <div class="card-grid">
          <div class="logo">
            <img
              [src]="'/assets/img/logo/' + trade.ticker?.toLowerCase() + '.png'"
            />
          </div>
          <div class="name">{{ trade.ticker }}</div>
          <div class="ticker">
            <!-- {{ trade?.ticker }} -->
            <div class="quantity">
              {{ trade?.quantity! | formatNumberIt }}
              @if (trade?.leverage! > 1) {
                <span class="leverage">
                  {{ "X" + (trade?.leverage | number: "0.0-0") }}</span
                >
              }
            </div>
          </div>
          <div
            class="profit"
            [ngStyle]="{
              color: trade.profit! > 0 ? 'var(--green-profit)' : 'red'
            }"
          >
            {{ trade?.profit! | profitsNoDecimal }}
          </div>

          <div
            class="profitPerc"
            [ngStyle]="{
              backgroundColor:
                trade.profit! > 0
                  ? 'rgba(0, 100, 0, 0.4)'
                  : 'rgba(100, 0, 0, 0.4)'
            }"
          >
            <div class="profitPerc-icon">
              @if (trade.profit! > 0) {
                <i class="fa-solid fa-caret-up"></i>
              } @else {
                <i
                  class="fa-solid fa-caret-down"
                  [ngStyle]="{
                    color: 'red'
                  }"
                ></i>
              }
            </div>
            <div
              class="profitPerc-number"
              [ngStyle]="{
                color: trade.profit > 0 ? '#04dc00' : 'red'
              }"
            >
              {{ trade?.profitPerc! | profitsPerc }}
            </div>
          </div>

          <div class="revenue">+ {{ trade?.revenue! | profitsNoDecimal }}</div>

          <div class="purchase">- {{ trade?.deposit! | profitsNoDecimal }}</div>
        </div>
      </div>
    } @empty {
      <span class="empty">No trades found</span>
    }
  </div>
</div>
