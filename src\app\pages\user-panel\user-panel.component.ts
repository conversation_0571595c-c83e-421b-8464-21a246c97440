// import { Component } from '@angular/core';
// import { AuthService } from 'src/app/core/services/auth/auth.service';
// import { PortfolioBinanceAndreaHistory } from 'src/app/core/services/data/binance-andrea-history.service';
// import { PortfolioBinanceAndrea } from 'src/app/core/services/data/binance-andrea.service';
// import { PortfolioBinanceFraHistory } from 'src/app/core/services/data/binance-fra-history.service';
// import { PortfolioBinanceFra } from 'src/app/core/services/data/binance-fra.service';
// import { PortfolioCoinbaseFraHistory } from 'src/app/core/services/data/coinbase-fra-history.service';
// import { PortfolioCoinbaseFra } from 'src/app/core/services/data/coinbase-fra.service';
// import { PortfolioPACHistory } from 'src/app/core/services/data/pac-history.service';
// import { PortfolioPAC } from 'src/app/core/services/data/pac.service';
// import { FirebaseService } from 'src/app/core/services/http/dbFirebase.service';

// @Component({
//   selector: 'app-user-panel',
//   templateUrl: './user-panel.component.html',
//   styleUrl: './user-panel.component.scss',
// })
// export class UserPanelComponent {
//   constructor(
//     private authService: AuthService,
//     private firebaseService: FirebaseService,
//     private portfolio: PortfolioCoinbaseFra,
//     private portfolio2: PortfolioBinanceFra,
//     private portfolio3: PortfolioBinanceAndrea,
//     private portfolio4: PortfolioPAC,
//     private portfolioHistory: PortfolioCoinbaseFraHistory,
//     private portfolio2History: PortfolioBinanceFraHistory,
//     private portfolio3History: PortfolioBinanceAndreaHistory,
//     private portfolio4History: PortfolioPACHistory
//   ) {
//     // this.addPortfoliosBackup();
//   }

//   onLogoutClick() {
//     this.authService.SignOut();
//   }

//   addPortfoliosBackup() {
//     const today = new Date();
//     const yyyy = today.getFullYear();
//     let mm = (today.getMonth() + 1).toString(); // Months start at 0!
//     let dd = today.getDate().toString();

//     if (+dd < 10) dd = '0' + dd;
//     if (+mm < 10) mm = '0' + mm;

//     const formattedToday = dd + '-' + mm + '-' + yyyy;

//     let portfolioBackup = 'portfolios_' + formattedToday;

//     this.firebaseService.addPortfoliosBackup(
//       {
//         coins: this.portfolio.coins,
//         history: {
//           monthly: this.portfolioHistory.portfolioMonthly,
//           yearly: this.portfolioHistory.portfolioYearly,
//         },
//         stats: this.portfolio.portfolioStats,
//       },
//       portfolioBackup,
//       'coinbase-fra'
//     );

//     this.firebaseService.addPortfoliosBackup(
//       {
//         coins: this.portfolio2.coins,
//         history: {
//           monthly: this.portfolio2History.portfolioMonthly,
//           yearly: this.portfolio2History.portfolioYearly,
//         },
//         stats: this.portfolio2.portfolioStats,
//       },
//       portfolioBackup,
//       'binance-fra'
//     );

//     this.firebaseService.addPortfoliosBackup(
//       {
//         coins: this.portfolio3.coins,
//         history: {
//           monthly: this.portfolio3History.portfolioMonthly,
//           yearly: this.portfolio3History.portfolioYearly,
//         },
//         stats: this.portfolio3.portfolioStats,
//       },
//       portfolioBackup,
//       'binance-andrea'
//     );

//     this.firebaseService.addPortfoliosBackup(
//       {
//         coins: this.portfolio4.coins,
//         history: {
//           monthly: this.portfolio4History.portfolioMonthly,
//           yearly: this.portfolio4History.portfolioYearly,
//         },
//         stats: this.portfolio4.portfolioStats,
//       },
//       portfolioBackup,
//       'pac'
//     );
//   }
// }
