import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth/auth.service';

export const authGuard: CanActivateFn = (route, state) => {
  const router = inject(Router);
  const authService = inject(AuthService);

  console.log('AUTH GUARD', authService.isLoggedIn);

  if (authService.isLoggedIn !== true) {
    router.navigateByUrl('/login');
    return false;
  } else return true;
};
