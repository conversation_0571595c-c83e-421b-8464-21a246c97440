.crypto-info-header {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 1rem;

  * {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #282828;
    border-radius: 6px;
    font-size: 1.4rem;
    width: 97.5%;
    padding: 0.7rem 1.2rem;
    cursor: pointer;
    &:first-child {
      margin-right: 10px;
    }

    &.active {
      background-color: #4b4b4b;
    }
  }
}

.crypto-info {
  padding: 0.5rem 1.5rem;
  display: flex;
  justify-content: space-between;
  background-color: rgb(36, 36, 36);
  border-radius: 10px;
  font-size: 1.2rem;

  &-table {
    width: 40%;
    border-collapse: separate;
    border-spacing: 0 1.5rem;

    .text {
      display: flex;
      align-items: flex-start;
    }

    .value {
      text-align: right;
    }
  }

  &.edit {
    border-radius: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    font-size: 1.2rem;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    display: grid;
    grid-template-columns: 30px auto;
    padding: 0 1rem 0 1rem;
    position: relative;
    z-index: 999;

    & .crypto-info-table {
      width: 100%;
      grid-column: 2/3;
      width: 201px;

      & tr {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1.5rem;
        font-size: 1.4rem;

        &:last-child {
          margin-bottom: 0;
        }
      }

      & input[type="number"] {
        -moz-appearance: textfield;
      }

      & input {
        width: 120px;
        -webkit-appearance: none;
        text-align: end;
        padding: 0 0.5rem;
        font-size: 1.4rem;
      }

      & .row {
        display: grid;
        grid-template-columns: auto 120px;

        & .button {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: green;
          font-size: 1.4rem;
          border-radius: 2px;
          grid-column: 2/3;
          border: 1px solid gray;
          cursor: pointer;
        }
      }
    }
    & .delete {
      grid-column: 1/2;
      grid-row: 1/2;
      align-self: end;
      justify-self: center;
      margin-bottom: 1.7rem;
      color: #a72323;
      font-size: 1.6rem;
      cursor: pointer;
    }
  }
}

.crypto-chart {
  width: 100%;
  height: 550px;
}

.tradingview-widget-copyright {
  opacity: 0;
}

.crypto-info-simulator {
  display: flex;
  flex-direction: column;
  width: 55%;
  padding: 1rem;
  margin: 1rem 0 0 1.5rem;
  background-color: rgb(0, 44, 51);
  border-radius: 15px;
  height: 150px;
  font-weight: 500;

  &-title {
    text-align: center;
    margin-bottom: 1rem;
  }

  &-price,
  &-deposits,
  &-profit,
  &-profitPerc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    input {
      width: 60%;
      padding: 0 0.3rem;
      margin-left: 1rem;
      text-align: right;
    }
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}
