<div class="crypto-list-header">
  <div class="crypto-list-header-name">Blockchain</div>
  <div class="crypto-list-header-tvl">TVL</div>
  <div class="crypto-list-header-price">TVL %</div>
</div>

@for (chain of chainsTvl.slice(0, 21); track chain) {
  <div class="crypto">
    @if(!!chain.tokenSymbol){
      <div class="crypto-list-table">
        <div class="crypto-list-table-logo">
          <img [src]="chain?.img" />
        </div>
        <div class="crypto-list-table-name">
          {{ chain?.name }}
        </div>
        <div class="crypto-list-table-ticker">
          {{ chain?.tokenSymbol }}
        </div>
        <div class="crypto-list-table-tvl">
          {{ chain?.tvl | shortNumber }}
        </div>
        <div class="crypto-list-table-price">
          <div>
            {{ (chain?.tvl / chainsTotalTvl) * 100 | profitsPerc }}
          </div>
        </div>
      </div>
    }
  </div>
}
