.crypto-list-header {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: 30px 35% 17.5% 19.5% auto;
  width: 100%;
  margin: 1rem 0 0rem 0;
  font-size: 1.4rem;
  color: #5b5b5b;
  // border-bottom: 1px solid #282828;

  &-name,
  &-price,
  &-profit,
  &-rating {
    align-self: center;
  }

  &-name {
    grid-column: 2/3;
    padding-left: 0.7rem;
  }

  &-score {
    grid-column: 4/5;
    justify-self: center;
    cursor: pointer;
  }

  &-rating {
    grid-column: 3/4;
    justify-self: center;
    height: 100%;
    display: flex;
  }

  &-review {
    grid-column: 5/6;
    justify-self: end;
    cursor: pointer;
  }
}

.crypto-list-table {
  display: grid;
  grid-template-rows: 17px 17px;
  grid-template-columns: 30px 35% 17.5% 19.5% auto;
  margin-top: 1.2rem;
  width: 100%;
  row-gap: 0.3rem;
  // border-bottom: 1px solid #1b1b1b;
  padding-bottom: 0.6rem;
  // border-radius: 15px;
  // background-color: rgb(17, 17, 17);
  // padding: 1rem;

  &-logo {
    grid-row: 1/3;
    grid-column: 1/2;
    justify-self: center;
    align-self: center;
    width: 100%;
    // height: 100%;
    margin-top: 0.3rem;
    margin-left: 0.2rem;
    cursor: pointer;

    & img {
      width: 100%;
      height: 100%;
    }
  }

  &-name {
    grid-row: 2/3;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.2rem;
    padding-left: 0.7rem;
    color: #5b5b5b;
    cursor: pointer;
  }

  &-ticker {
    grid-row: 1/2;
    grid-column: 2/3;
    justify-self: start;
    align-self: center;
    font-size: 1.6rem;
    padding-left: 0.7rem;
    cursor: pointer;
  }

  &-rating {
    grid-row: 1/2;
    grid-column: 3/4;
    align-self: center;
    justify-self: center;
    font-size: 1.4rem;
    border-radius: 10px;
    font-weight: 500;
    width: 80%;
    text-align: center;
    letter-spacing: 1px;
    height: 100%;
    display: flex;
    justify-content: center;
  }

  &-score {
    grid-row: 1/2;
    grid-column: 4/5;
    justify-self: center;
    align-self: center;
    font-size: 1.4rem;
  }

  &-review {
    grid-row: 1/2;
    grid-column: 5/6;
    justify-self: end;
    align-self: center;
    font-size: 1.4rem;
  }
}

.crypto {
  display: flex;
  flex-direction: column;
}
