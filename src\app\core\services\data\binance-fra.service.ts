import { Injectable } from '@angular/core';
import { Coin, ICoin, IPortfolioStats, ITrade } from '../../interfaces/coins';

@Injectable({
  providedIn: 'root',
})
export class PortfolioBinanceFra extends Coin {
  constructor() {
    super();
  }

  //BINANCE
  public coins: ICoin[] = [
    // Profitto BTC chiuso nel 2024 = 900€
    // poi chiuso
    {
      name: 'Ethereum',
      nameApi: 'ethereum',
      ticker: 'ETH',
      logo: '../../../assets/img/logo/eth.png',
      category: 'Layer 1',
      quantity: 0.183,
      deposits: 450.12,
      averagePrice: 2484,
      logoColor: '#627eea',
      ecosystem: ['Ethereum'],
    },
    {
      name: 'BNB',
      nameApi: 'binancecoin',
      ticker: 'BNB',
      logo: '../../../assets/img/logo/bnb.webp',
      category: 'Layer 1',
      quantity: 0.064,
      deposits: 465.2,
      averagePrice: 294.1,
      logoColor: '#F0B90B',
    },
    {
      name: 'Fetch.ai',
      nameApi: 'fetch-ai',
      ticker: 'FET',
      logo: '../../../assets/img/logo/fet.png',
      category: 'AI',
      quantity: 595.47,
      deposits: 290.12,
      averagePrice: 1.07,
      logoColor: '#1E2943',
      ecosystem: ['Ethereum'],
      description: `Fondato nel 2017 e lanciato tramite IEO su Binance nel marzo 2019, Fetch.AI è un laboratorio di 
      <span style='color: #4f4fff'><strong>intelligenza artificiale (AI)</strong></span> che costruisce una rete di apprendimento automatico aperta, senza autorizzazione e decentralizzata con un'economia crittografica. 
      Fetch.ai democratizza l'accesso alla tecnologia IA con una rete senza autorizzazione su cui chiunque può connettersi e accedere a set di dati sicuri utilizzando l'intelligenza artificiale autonoma per eseguire attività che sfruttano la sua rete globale di dati. 
      Il modello Fetch.AI affonda le sue radici in casi d'uso come l'ottimizzazione dei servizi di trading DeFi, delle reti di trasporto (parcheggi, micromobilità), delle reti energetiche intelligenti, dei viaggi - essenzialmente qualsiasi sistema digitale complesso che si basa su set di dati su larga scala.`,
    },
    {
      name: 'Polygon',
      nameApi: 'matic-network',
      ticker: 'MATIC',
      logo: '../../../assets/img/logo/matic.png',
      quantity: 296.08,
      deposits: 229.72,
      averagePrice: 0.86,
      logoColor: '#8247e5',
      category: 'Layer 2',
      description: `Polygon (in precedenza Matic Network) è la prima piattaforma ben strutturata e facile da usare per la scalabilità di Ethereum e lo sviluppo dell'infrastruttura. Il suo componente principale è Polygon SDK, un framework modulare e flessibile che supporta la creazione di più tipi di applicazioni.

Per saperne di più su questo progetto, dai un'occhiata al nostro approfondimento su Polygon Matic.

Utilizzando Polygon, è possibile creare catene di rollup ottimistiche, catene di rollup ZK, catene autonome o qualsiasi altro tipo di infrastruttura richiesta dallo sviluppatore.

Polygon trasforma efficacemente Ethereum in un vero e proprio sistema multi-catena (noto anche come Internet of Blockchains). Questo sistema multi-catena è simile ad altri come Polkadot, Cosmos, Avalanche ecc. con i vantaggi della sicurezza, dell'ecosistema vibrante e dell'apertura di Ethereum.

Il token $MATIC continuerà ad esistere e svolgerà un ruolo sempre più importante, proteggendo il sistema e consentendo la governance.

Polygon (precedentemente Matic Network) è una soluzione di ridimensionamento di livello 2 supportata da Binance e Coinbase. Il progetto cerca di stimolare l'adozione di massa delle criptovalute risolvendo i problemi di scalabilità su molte blockchain.

Polygon combina Plasma Framework e l'architettura blockchain proof-of-stake. Il framework Plasma utilizzato da Polygon, come proposto dal co-fondatore di Ethereum, Vitalik Buterin, consente la facile esecuzione di contratti intelligenti scalabili e autonomi.

Nulla cambierà per l'ecosistema esistente costruito sulla catena Plasma-POS. Con Polygon, vengono sviluppate nuove funzionalità attorno alla tecnologia comprovata esistente per espandere la capacità di soddisfare le diverse esigenze dell'ecosistema degli sviluppatori. Polygon continuerà a sviluppare la tecnologia di base in modo che possa adattarsi a un ecosistema più ampio.

Polygon vanta fino a 65.000 transazioni al secondo su una singola catena laterale, insieme a un tempo di conferma del blocco rispettabile inferiore a due secondi. Il framework consente inoltre la creazione di applicazioni finanziarie decentralizzate disponibili a livello globale su un'unica blockchain fondamentale.

Il framework Plasma offre a Polygon il potenziale di ospitare un numero illimitato di applicazioni decentralizzate sulla propria infrastruttura senza sperimentare i normali inconvenienti comuni alle blockchain proof-of-work. Finora, Polygon ha attirato più di 50 DApp sulla sua sidechain Ethereum protetta da PoS.

MATIC, i token nativi di Polygon, è un token ERC-20 in esecuzione sulla blockchain di Ethereum. I token vengono utilizzati per i servizi di pagamento su Polygon e come valuta di regolamento tra gli utenti che operano all'interno dell'ecosistema Polygon. Anche le commissioni di transazione sulle sidechain Polygon vengono pagate in token MATIC.`,
    },
    {
      name: 'NEAR Protocol',
      nameApi: 'near',
      ticker: 'NEAR',
      logo: '../../../assets/img/logo/near.png',
      quantity: 55.41,
      deposits: 190.34,
      averagePrice: 12.91,
      logoColor: '#24272a',
      category: 'Layer 1',
      description: `NEAR Protocol è una blockchain di livello uno progettata come piattaforma di cloud computing gestita dalla comunità e che elimina alcune delle limitazioni che hanno impantanato le blockchain concorrenti, come basse velocità di transazione, basso throughput e scarsa interoperabilità. Ciò fornisce l'ambiente ideale per le DApp e crea una piattaforma di facile utilizzo per sviluppatori e utenti. Ad esempio, NEAR utilizza nomi di account leggibili dall'uomo, a differenza degli indirizzi di portafoglio crittografici comuni a Ethereum. NEAR introduce anche soluzioni uniche per problemi di scalabilità e dispone di un proprio meccanismo di consenso chiamato “Doomslug”.

Il protocollo NEAR è stato creato dal NEAR Collective, la sua comunità che sta aggiornando il codice iniziale e rilasciando aggiornamenti all'ecosistema. Il suo obiettivo dichiarato è quello di costruire una piattaforma che sia “abbastanza sicura da gestire beni di alto valore come denaro o identità e sufficientemente performante da renderli utili per la gente comune”.

Flux, un protocollo che consente agli sviluppatori di creare mercati basati su asset, materie prime, eventi del mondo reale, e Mintbase, una piattaforma di conio NFT, sono esempi di progetti costruiti sul protocollo NEAR.`,
    },

    {
      name: 'Decentraland',
      nameApi: 'decentraland',
      ticker: 'MANA',
      logo: '../../../assets/img/logo/decentraland.png',
      quantity: 74.95,
      deposits: 79.23,
      averagePrice: 1.06,
      logoColor: '#ff4b56',
      category: 'Gaming',
      description: `Decentraland (MANA) si definisce come una piattaforma di realtà virtuale alimentata dalla blockchain di Ethereum che consente agli utenti di creare, sperimentare e monetizzare contenuti e applicazioni.

In questo mondo virtuale, gli utenti acquistano appezzamenti di terreno che possono successivamente esplorare, costruire e monetizzare.

Decentraland è stato lanciato a seguito di un'offerta iniziale di monete (ICO) da 24 milioni di dollari condotta nel 2017. Il mondo virtuale ha lanciato la sua beta chiusa nel 2019 ed è stato aperto al pubblico nel febbraio 2020. Da allora, gli utenti hanno creato un'ampia gamma di esperienze sul proprio computer. lotti di LAND, inclusi giochi interattivi, vaste scene 3D e una varietà di altre esperienze interattive.

Decentraland utilizza due token: MANA e LAND. MANA è un token ERC-20 che deve essere masterizzato per acquisire token LAND ERC-721 non fungibili. I token MANA possono anche essere utilizzati per pagare una gamma di avatar, dispositivi indossabili, nomi e altro sul mercato Decentraland.

Decentraland (MANA) è una piattaforma di mondo virtuale decentralizzata con software basato sulla blockchain di Ethereum, in cui gli utenti creano, testano e monetizzano contenuti. In breve: gli utenti acquistano appezzamenti di terreno virtuali come NFT con token MANA sulla piattaforma Decentraland. Il progresso nel gioco dipende solo dai giocatori stessi, dai loro sforzi e dalla loro immaginazione.

La piattaforma digitale è completamente di proprietà dei suoi utenti. All'interno del metaverso di Decentraland, i partecipanti possono esplorare il mondo virtuale, acquistare appezzamenti di terreno che possono essere monetizzati, creare opere d'arte, possedere token non fungibili (NFT) e prendere parte a un'organizzazione autonoma decentralizzata della piattaforma Decentraland DAO. Grazie alla DAO, la comunità influenza il vettore di sviluppo del progetto.

Inoltre, gli utenti hanno accesso ad applicazioni interattive, pagamenti nel mondo e comunicazione peer-to-peer. Il mondo 3D incontra molti partecipanti poiché Decentraland ha molte applicazioni, sia intrattenimento per i giocatori che affari per gli investitori.

Due tipi di token chiamati MANA e LAND governano le operazioni in Decentraland. Consentono inoltre agli utenti di interagire con la piattaforma.

MANA è la valuta ufficiale di Decentraland. È un token standard ERC-20 che alimenta la piattaforma Decentraland. Con MANA, gli utenti possono acquistare appezzamenti di LAND, nonché pagare oggetti e servizi di gioco.

MANA dà ai suoi titolari il diritto di voto in Decentraland DAO. MANA può essere acquistato su uno scambio di criptovaluta o vendendo oggetti da collezione su Decentraland Marketplace. Il proprietario dei token li converte in MANA avvolto, o wMANA, e poi li impegna nel DAO. Un wMANA equivale a un voto. MANA può essere scambiato con valuta fiat e altre risorse digitali.

Per quanto riguarda LAND, si tratta di un token non fungibile (NFT) basato su Ethereum (ETH). Viene utilizzato per tenere traccia della proprietà dei lotti di terreno, ovvero degli immobili digitali. Come MANA, LAND fa parte del protocollo e conferisce diritti di voto, ma non è necessario che LAND sia fissato nel DAO. Un LAND equivale a duemila voti.

I giocatori scelgono cosa fare con la loro terra. I proprietari conservano i token in portafogli crittografici e acquistano beni di prima necessità con i token MANA sul mercato Decentraland. Inoltre, sul mercato, i partecipanti possono gestire o scambiare token LAND (prezzati in MANA). I titolari utilizzano il mercato per effettuare transazioni o trasferire oggetti di gioco. Tutte le transazioni sono condotte, regolate e registrate tramite la blockchain di Ethereum.

Decentraland combina uno spazio di gioco online e la tecnologia blockchain. Tutto il controllo sulle regole è dato ai giocatori e i possessori di token votano sulle politiche di gioco e organizzative. L'obiettivo originale era quello di creare una realtà virtuale decentralizzata, ma in pratica Decentraland si è trasformato in un enorme mercato NFT.

Un ruolo speciale in Decentraland è assegnato agli NFT, come oggetti da collezione, attrezzature virtuali e immobili (LAND). Tuttavia, per i nuovi giocatori che vogliono unirsi all'universo e impossessarsi degli NFT, la barriera d'ingresso sarà alta.

Il protocollo Decentraland è costruito su tre livelli utilizzando i contratti intelligenti di Ethereum. A livello di consenso, la proprietà della TERRA viene tracciata e gestita. Il livello del contenuto del territorio mostra il contenuto del mondo virtuale di Decentraland. Infine, nel livello in tempo reale, i partecipanti interagiscono tra loro.

La piattaforma è stata co-fondata da due esperti blockchain, Ari Meilich ed Esteban Ordano, nel 2015. Ufficialmente è stata lanciata un anno dopo ed è diventata disponibile solo a febbraio 2020, trasformandosi gradualmente da un esperimento 2D in un vero e proprio mondo 3D.

Per riassumere, Decentraland è un gioco decentralizzato e una piattaforma di realtà virtuale 3D in cui i giocatori possiedono terre e creano senza limiti. Il software dell'Organizzazione Autonoma Decentralizzata (DAO) è al centro e la governance è nelle mani della comunità. Attraverso il token nativo MANA, i giocatori partecipano a eventuali proposte di gestione e miglioramento dell'ecosistema, del sistema di aste e della policy in-game.`,
    },

    {
      name: 'VeChain',
      nameApi: 'vechain',
      ticker: 'VET',
      logo: '../../../assets/img/logo/vet.webp',
      quantity: 1147.31,
      deposits: 113.31,
      averagePrice: 0.098,
      logoColor: '#28008C',
      category: 'Internet of Things',
      description: `VeChain (VET) è una versatile piattaforma di contratto intelligente L1 di livello aziendale.

VeChain è nata nel 2015 come catena di consorzi privati, collaborando con una serie di imprese per esplorare le applicazioni della blockchain. VeChain inizierà la transizione alla blockchain pubblica nel 2017 con il token ERC-20 VEN, prima di lanciare una propria mainnet nel 2018 utilizzando il ticker VET.

VeChain mira a utilizzare la governance distribuita e le tecnologie dell'Internet delle cose (IoT) per creare un ecosistema che risolva i principali ostacoli relativi ai dati per molteplici settori globali, dalla medicina all'energia, dal cibo e bevande alla sostenibilità e agli obiettivi di sviluppo sostenibile. Sfruttando la potenza dei dati trustless, VeChain sta costruendo la dorsale digitale che sosterrà la quarta rivoluzione industriale, che richiede la condivisione dei dati in tempo reale e trustless tra molti partecipanti.

La piattaforma utilizza due token, VET e VTHO, per gestire e creare valore basato sulla sua blockchain pubblica VeChainThor. L'IFP genera VTHO e funge da riserva di valore e mezzo di trasferimento di valore. VTHO viene utilizzato per pagare i costi GAS, separando la necessità di spendere VET durante la scrittura dei dati. Ciò ha l'ulteriore vantaggio di garantire che i costi di utilizzo della rete possano essere mantenuti stabili modificando alcune variabili come la quantità di VTHO richiesta per servire una transazione o aumentando il tasso di generazione di VTHO. Tali azioni richiedono innanzitutto il voto della comunità di tutte le parti interessate.

VeChain è stata in grado di dimostrare un notevole aumento di efficienza, tracciabilità e trasparenza nei percorsi di dati, nelle catene di approvvigionamento e all'interno di nuovi tipi di ecosistemi, come quelli di San Marino che mirano, tra gli altri, agli obiettivi di sviluppo sostenibile delle Nazioni Unite.`,
    },

    {
      name: 'The Sandbox',
      nameApi: 'the-sandbox',
      ticker: 'SAND',
      logo: '../../../assets/img/logo/the-sandbox.png',
      quantity: 66.68,
      deposits: 82.7,
      averagePrice: 1.24,
      logoColor: '#00aeef',
      category: 'Gaming',
    },
    {
      name: 'Polkadot',
      nameApi: 'polkadot',
      ticker: 'DOT',
      logo: '../../../assets/img/logo/dot.png',
      quantity: 9.458,
      deposits: 55.51,
      averagePrice: 5.2,
      logoColor: '#e6007a',
      category: 'Layer 1',
    },

    {
      name: 'Algorand',
      nameApi: 'algorand',
      ticker: 'ALGO',
      logo: '../../../assets/img/logo/algorand.png',
      quantity: 159.57,
      deposits: 63.38,
      averagePrice: 1.71,
      logoColor: '#ffff',
      category: 'Layer 1',
    },

    {
      name: 'Avalanche',
      nameApi: 'avalanche-2',
      ticker: 'AVAX',
      logo: '../../../assets/img/logo/avax.png',
      quantity: 4.126,
      deposits: 155.95,
      averagePrice: 28.04,
      logoColor: '#e84142',
      category: 'Layer 1',
    },
    // {
    //   name: 'Terra Classic',
    //   nameApi: 'terra-luna',
    //   ticker: 'LUNA',
    //   logo: '../../../assets/img/logo/luna.webp',
    //   quantity: 277116.6,
    //   deposits: 233.04,
    //   averagePrice: 0.00169,
    //   logoColor: '#F9D65D',
    //   category: 'Layer 1',
    // },
    {
      name: 'Cosmos',
      nameApi: 'cosmos',
      ticker: 'ATOM',
      logo: '../../../assets/img/logo/atom.png',
      quantity: 20.23,
      deposits: 182.12,
      averagePrice: 17.12,
      logoColor: '#2e3148',
      category: 'Layer 1',
    },
    {
      name: 'Arbitrum',
      nameApi: 'arbitrum',
      ticker: 'ARB',
      logo: '../../../assets/img/logo/arb.png',
      category: 'Layer 2',
      quantity: 514.4,
      deposits: 1046,
      averagePrice: 2.08,
      logoColor: '#2D6E8D',
    },
    {
      name: 'The Graph',
      nameApi: 'the-graph',
      ticker: 'GRT',
      logo: '../../../assets/img/logo/graph.webp',
      category: 'AI',
      quantity: 695.72,
      deposits: 110,
      averagePrice: 0.16,
      logoColor: '#4827A9',
      ecosystem: ['Ethereum'],
    },
    {
      name: 'Official Trump',
      nameApi: 'official-trump',
      ticker: 'TRUMP',
      logo: '../../../assets/img/logo/trump.webp',
      category: 'Memecoin',
      quantity: 1.4,
      deposits: 100,
      averagePrice: 71.8,
      logoColor: 'yellow',
      ecosystem: ['Solana'],
    },
    {
      name: 'Solana',
      nameApi: 'solana',
      ticker: 'SOL',
      logo: '../../../assets/img/logo/sol.png',
      quantity: 4.22,
      deposits: 449.87,
      averagePrice: 200,
      logoColor: '#0a0b0d',
      ecosystem: ['Solana'],
      category: 'Layer 1',
    },
  ];

  public closedTrades: ITrade[] = [
    {
      tradeNumber: 1,
      date: '2025-03-26',
      coinName: 'Ethereum',
      ticker: 'ETH',
      quantity: 2.05,
      buyPrice: 1539.3,
      deposit: 3078.6,
      sellPrice: 1862,
      revenue: 3735.62,
      profit: 657.02,
      profitPerc: (657.02 / 3078.6) * 100,
    },
    {
      tradeNumber: 2,
      date: '2025-03-26',
      coinName: 'Ripple',
      ticker: 'XRP',
      quantity: 793.95,
      buyPrice: 1.584,
      deposit: 1257.69,
      sellPrice: 2.188,
      revenue: 1737.51,
      profit: 479.82,
      profitPerc: (479.82 / 1257.69) * 100,
    },
    {
      tradeNumber: 3,
      date: '2025-03-26',
      coinName: 'Cardano',
      ticker: 'ADA',
      quantity: 1285.84,
      buyPrice: 1.36,
      deposit: 1749.38,
      sellPrice: 0.675,
      revenue: 868.55,
      profit: -880.83,
      profitPerc: (-880.83 / 1749.38) * 100,
    },
    {
      tradeNumber: 4,
      date: '2025-03-26',
      coinName: 'Binance',
      ticker: 'BNB',
      quantity: 1.474,
      buyPrice: 352.4,
      deposit: 519.45,
      sellPrice: 572.03,
      revenue: 843.32,
      profit: 323.87,
      profitPerc: (323.87 / 1749.38) * 100,
    },
    {
      tradeNumber: 5,
      date: '2025-03-26',
      coinName: 'Fetch.ai',
      ticker: 'FET',
      quantity: 1363.37,
      buyPrice: 0.722,
      deposit: 985.35,
      sellPrice: 0.508,
      revenue: 692.68,
      profit: 265.37,
      profitPerc: (265.37 / 985.35) * 100,
    },
    {
      tradeNumber: 6,
      date: '2025-03-26',
      coinName: 'Solana',
      ticker: 'SOL',
      quantity: 6.29,
      buyPrice: 70.17,
      deposit: 441.43,
      sellPrice: 126.61,
      revenue: 796.49,
      profit: 328.06,
      profitPerc: (328.06 / 441.43) * 100,
    },
    {
      tradeNumber: 7,
      date: '2025-03-26',
      coinName: 'Shiba Inu',
      ticker: 'SHIB',
      quantity: 14989167.09,
      buyPrice: 0.000027,
      deposit: 222.54,
      sellPrice: 0.000023,
      revenue: 195.79,
      profit: -26.75,
      profitPerc: (-26.75 / 222.54) * 100,
    },
    {
      tradeNumber: 8,
      date: '2025-03-26',
      coinName: 'Polkadot',
      ticker: 'DOT',
      quantity: 38.766,
      buyPrice: 6.69,
      deposit: 214.48,
      sellPrice: 4.32,
      revenue: 167.68,
      profit: -46.8,
      profitPerc: (-46.8 / 214.48) * 100,
    },
    {
      tradeNumber: 9,
      date: '2025-03-26',
      coinName: 'Bitcoin',
      ticker: 'BTC',
      quantity: 0.001975,
      buyPrice: 24497,
      deposit: 47.77,
      sellPrice: 80765,
      revenue: 159.51,
      profit: 111.74,
      profitPerc: (111.74 / 47.77) * 100,
    },
  ];

  public portfolioStats: IPortfolioStats = {
    gifts: 120,
    totalFees: -269.31,
    taxes: 41.1,
    realizedProfit: this.realizedProfit,
  };

  // TOTAL EARN AL 16-11-2024 = 229€
}
