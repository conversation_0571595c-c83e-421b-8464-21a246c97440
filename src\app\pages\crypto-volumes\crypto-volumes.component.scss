.crypto-list {
  padding: 0.5rem 1.3rem;
  margin-bottom: 0.5rem;
  overflow: hidden;

  .crypto-list-header {
    display: grid;
    grid-template-rows: auto;
    grid-template-columns: 34px 35% 20% 17% auto;
    width: 100%;
    margin: 1rem 0 0rem 0;
    font-size: 1.4rem;
    color: #5b5b5b;
    // border-bottom: 1px solid #282828;

    &-name,
    &-price,
    &-profit,
    &-tvl,
    &-30d {
      align-self: center;
    }

    &-name {
      grid-column: 2/3;
      padding-left: 0.7rem;
    }

    &-price {
      grid-column: 4/5;
      justify-self: end;
    }

    &-category {
      align-self: start;
      justify-self: center;
    }

    &-tvl {
      grid-column: 3/4;
      justify-self: end;
      cursor: pointer;
    }

    &-30d {
      grid-column: 5/6;
      justify-self: end;
      cursor: pointer;
    }
  }

  .crypto-list {
    padding: 0.5rem 1.3rem;
    margin-bottom: 0.5rem;
    overflow: hidden;

    .info {
      display: flex;
      width: 100%;
      // background-color: #005382;
      border-radius: 10px;
      // border: 1px solid gray;

      &-text {
        display: flex;
        justify-content: space-between;
        font-size: 1.6rem;
        width: 100%;
        // padding: 0 1.3rem;

        & .deposits {
          display: flex;
          justify-content: start;
          align-items: center;
          padding: 1rem 0;
          border-radius: 10px;
          width: auto;
          letter-spacing: 0.2px;
        }
      }

      & select {
        font-size: 1.6rem;
        padding: 1rem 0.5rem;
        border-radius: 10px;
        border: none;
        background-color: black;
        color: #fff;
        width: 122px;
        text-align: center;
        margin-right: 1rem;
        cursor: pointer;
      }
    }

    &-title {
      grid-row: 1/2;
      grid-column: 1/-1;
      align-self: center;
      font-size: 1.8rem;
      font-weight: 500;
      color: #fff;
      margin-bottom: 0.5rem;
    }

    &-buttons {
      display: -webkit-inline-box;
      cursor: pointer;
      margin-bottom: 0.5rem;
      overflow-x: auto;
      width: 109%;
      margin-left: -1.5rem;
      padding-left: 1.5rem;
      padding-right: 3rem;

      &-tvl,
      &-fees {
        color: #c4c4c4;
        background-color: #282828;
        width: auto;
        padding: 0.7rem 1.2rem;
        border-radius: 5px;
        margin-right: 0.5rem;
        font-size: 1.4rem;
        white-space: nowrap;
        // text-wrap: nowrap;

        &.selected {
          background-color: #4b4b4b;
          color: white;
        }
      }

      & .extra {
        width: 1rem;
      }
    }
  }
  .crypto {
    & .crypto-list-table {
      display: grid;
      grid-template-rows: 17px 17px;
      grid-template-columns: 34px 35% 20% 17% auto;
      margin-top: 1.2rem;
      width: 100%;
      row-gap: 0.3rem;
      // border-bottom: 1px solid #1b1b1b;
      padding-bottom: 0.6rem;
      // border-radius: 15px;
      // background-color: rgb(17, 17, 17);
      // padding: 1rem;

      &-logo {
        grid-row: 1/3;
        grid-column: 1/2;
        justify-self: center;
        align-self: center;
        width: 100%;
        // height: 100%;
        margin-top: 0.3rem;
        margin-left: 0.2rem;
        cursor: pointer;

        & img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      &-name {
        grid-row: 1/2;
        grid-column: 2/3;
        justify-self: start;
        align-self: center;
        font-size: 1.6rem;
        padding-left: 0.7rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        cursor: pointer;
      }

      &-ticker {
        grid-row: 2/3;
        grid-column: 2/3;
        justify-self: start;
        align-self: center;
        font-size: 1.4rem;
        padding-left: 0.7rem;
        color: #5b5b5b;
        cursor: pointer;
      }

      &-tvl {
        grid-row: 1/2;
        grid-column: 3/4;
        align-self: center;
        justify-self: end;
        font-size: 1.4rem;
      }

      &-price {
        grid-row: 1/2;
        grid-column: 4/5;
        justify-self: end;
        align-self: center;
        font-size: 1.4rem;
      }

      &-category {
        grid-row: 1/3;
        grid-column: 3/4;
        align-self: start;
        justify-self: center;
        font-size: 1.4rem;
        text-align: center;
      }

      &-gain {
        grid-row: 1/2;
        grid-column: 5/6;
        justify-self: end;
        align-self: center;
        font-size: 1.6rem;
      }

      &-gainPercent24h {
        grid-row: 1/2;
        grid-column: 3/4;
        align-self: center;
        justify-self: end;
        display: flex;
        flex-direction: row;
        // color: #04dc00;
        border-radius: 15px;

        &-number {
          font-size: 1.4rem;
        }
      }

      &-gainPercent7d {
        grid-row: 1/2;
        grid-column: 4/5;
        align-self: center;
        justify-self: end;
        display: flex;
        flex-direction: row;
        // color: #04dc00;
        border-radius: 15px;

        &-number {
          font-size: 1.4rem;
        }
      }

      &-gainPercent30d {
        grid-row: 1/2;
        grid-column: 5/6;
        align-self: center;
        justify-self: end;
        display: flex;
        flex-direction: row;
        // color: #04dc00;
        border-radius: 15px;

        &-number {
          font-size: 1.4rem;
        }
      }
    }
  }
}

@media (min-width: 900px) {
  .crypto-list {
    background-color: rgb(10, 10, 10);
    border-radius: 15px;
    padding-top: 1rem;
    margin: 0 12%;
  }
}
