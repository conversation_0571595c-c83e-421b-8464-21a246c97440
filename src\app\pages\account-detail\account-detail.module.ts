import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { PipesModule } from 'src/app/core/utils/pipes.module';
import { CulumnchartYearlyModule } from 'src/app/shared/charts/culumnchart-yearly/culumnchart-yearly.module';
import { AccountSelectorModule } from '../../shared/account-selector/account-selector.module';
import { AccountDetailRoutingModule } from './account-detail-routing.module';
import { AccountDetailComponent } from './account-detail.component';

@NgModule({
  declarations: [AccountDetailComponent],
  imports: [
    CommonModule,
    AccountDetailRoutingModule,
    AccountSelectorModule,
    PipesModule,
    CulumnchartYearlyModule,
  ],
})
export class AccountDetailModule {}
