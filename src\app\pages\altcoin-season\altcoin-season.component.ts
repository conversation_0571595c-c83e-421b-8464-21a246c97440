import { HttpClient } from '@angular/common/http';
import { Component, effect, signal, untracked } from '@angular/core';
import { GoogleSheetService } from 'src/app/core/services/http/google-sheet.service';

@Component({
  selector: 'app-altcoin-season',
  templateUrl: './altcoin-season.component.html',
  styleUrl: './altcoin-season.component.scss',
})
export class AltcoinSeasonComponent {
  protected loading = signal<boolean>(true);
  protected cryptoStats;
  protected cryptoTop;
  protected currentSection = 'stats';
  protected currentFilter = '7d';

  constructor(
    private http: HttpClient,
    private googleSheetService: GoogleSheetService,
  ) {
    effect(() => {
      if (this.googleSheetService.googleStats()?.length > 0) {
        // console.log('EFFECT', this.googleSheetService.googleAllData());
        this.cryptoTop = this.googleSheetService
          .googleAllData()
          .sort((a, b) => +b[6] - +a[6]);
        this.cryptoStats = this.googleSheetService.googleStats;
        untracked(() => this.loading.set(false));
      }
    });
  }

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    if (this.googleSheetService.shouldFetchData())
      this.googleSheetService.fetchAllData().subscribe();
    if (!this.googleSheetService.shouldFetchData()) {
      this.googleSheetService.googleAllData.set(
        JSON.parse(localStorage.getItem('googleAllData')),
      );
      this.googleSheetService.googleStats.set(
        JSON.parse(localStorage.getItem('googleStats')),
      );
    }
  }

  onSortClick(days) {
    this.currentFilter = days;
    if (days == '7d')
      this.cryptoTop = this.cryptoTop.sort((a, b) => +b[6] - +a[6]);
    if (days == '30d')
      this.cryptoTop = this.cryptoTop.sort((a, b) => +b[7] - +a[7]);
  }
}
