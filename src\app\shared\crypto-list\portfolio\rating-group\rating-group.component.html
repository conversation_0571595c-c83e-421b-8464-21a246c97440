<div class="crypto-list-header">
  <div class="crypto-list-header-expand" (click)="onHeaderCategoriesClick()">
    @if(currentCategories.length == 0){
      <i class="fa-solid fa-angles-down"></i>
    } @else { <i class="fa-solid fa-angles-up"></i>}
  </div>

  <div class="crypto-list-header-rating">Rating</div>
  <div class="crypto-list-header-price">Deposit %</div>
  <div class="crypto-list-header-deposits">
    Deposit <i class="fa-solid fa-caret-down"></i>
  </div>
</div>

@for (ratingGroup of currentAccount; track ratingGroup) {
  <div class="crypto">
    <div
      class="crypto-list-table"
      (click)="toggleRatingGroup(ratingGroup.rating)"
      >
      <div class="crypto-list-table-logo">
        <img src="{{ ratingGroup.coins[0]?.logo }}" />
      </div>
      <div
        class="crypto-list-table-rating"
        [ngClass]="{ expanded: currentCategories.includes(ratingGroup.rating) }"
        [appRatingBackground]
        [rating]="ratingGroup?.rating"
        >
        {{ ratingGroup.rating }}
      </div>
      @if(currentCategories.includes(ratingGroup.rating) ||
        currentCategories.includes('All')){
        <div class="crypto-list-table-ticker">
          @for(coin of ratingGroup.coins; track coin.ticker) {
            <div class="coin">{{ coin.ticker }}</div>
          }
        </div>
      }
      <div
        class="crypto-list-table-perc"
        [ngClass]="{ expanded: currentCategories.includes(ratingGroup.rating) }"
        >
        {{ ratingGroup.depositsPerc | profitsPerc }}
      </div>
      <div
        class="crypto-list-table-deposits"
        [ngStyle]="{ color: white }"
        [ngClass]="{ expanded: currentCategories.includes(ratingGroup.rating) }"
        >
        {{ ratingGroup.deposits | depositsNoDecimal }}
      </div>
      @if(currentCategories.includes(ratingGroup.rating) ||
        currentCategories.includes('All')){
        <div class="crypto-list-table-depositPerc">
          @for(coin of ratingGroup.coins; track coin.ticker) {
            <div class="coin">{{ coin.depositsPerc | profitsPerc }}</div>
          }
        </div>
        } @if(currentCategories.includes(ratingGroup.rating) ||
        currentCategories.includes('All')){
        <div class="crypto-list-table-deposit">
          @for(coin of ratingGroup.coins; track coin.ticker) {
            <div class="coin">{{ coin.deposits | depositsNoDecimal }}</div>
          }
        </div>
      }
    </div>
  </div>
}
