import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import {
  $DepositsNoDecimalPipe,
  DepositsNoDecimalPipe,
  DepositsPipe,
  FormatNumberItPipe,
  ProfitsNoDecimalPipe,
  ProfitsPercNoDecimalPipe,
  ProfitsPercPipe,
  ProfitsPercSimulatorPipe,
  ProfitsPipe,
  QuantityPipe,
  RatingLevelPipe,
  ShortNumberPipe,
  ShortNumberUsdPipe,
  ThousandsSeparatorPipe,
  TimeFromNowPipe,
} from 'src/app/core/utils/pipes';

@NgModule({
  declarations: [
    DepositsPipe,
    ProfitsPipe,
    ProfitsPercPipe,
    ProfitsPercSimulatorPipe,
    ShortNumberPipe,
    DepositsNoDecimalPipe,
    ProfitsNoDecimalPipe,
    $DepositsNoDecimalPipe,
    QuantityPipe,
    TimeFromNowPipe,
    RatingLevelPipe,
    ShortNumberUsdPipe,
    ThousandsSeparatorPipe,
    ProfitsPercNoDecimalPipe,
    FormatNumberItPipe,
  ],
  imports: [CommonModule],
  exports: [
    DepositsPipe,
    ProfitsPipe,
    ProfitsPercPipe,
    ProfitsPercSimulatorPipe,
    ShortNumberPipe,
    DepositsNoDecimalPipe,
    ProfitsNoDecimalPipe,
    $DepositsNoDecimalPipe,
    QuantityPipe,
    TimeFromNowPipe,
    RatingLevelPipe,
    ShortNumberUsdPipe,
    ThousandsSeparatorPipe,
    ProfitsPercNoDecimalPipe,
    FormatNumberItPipe,
  ],
})
export class PipesModule {}
