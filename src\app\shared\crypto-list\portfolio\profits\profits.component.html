<div class="crypto-list-header">
  <!-- <div class="crypto-list-header-name">Ticker <br />Name</div> -->
  <div class="crypto-list-header-deposit">
    <div (click)="onSortCurrentClick()">
      Current
      @if (currentSorting() == 'current') {
        <i
          class="fa-solid fa-caret-down"
        ></i>
      }
    </div>
    <div (click)="onSortDepositClick()">
      Deposit
      @if (currentSorting() == 'deposits') {
        <i
          class="fa-solid fa-caret-down"
        ></i>
      }
    </div>
  </div>
  <div class="crypto-list-header-price">
    <div>Curr. Price</div>
    <div>Avg. Price</div>
  </div>
  <div class="crypto-list-header-profit">
    <div (click)="onSortProfitClick('profits')">
      Profit
      @if (currentSorting() == 'profits') {
        <i
          class="fa-solid fa-caret-down"
        ></i>
      }
    </div>

    <div (click)="onSortProfitClick('profitsPerc')">
      Profit %
      @if (currentSorting() == 'profitsPerc') {
        <i
          class="fa-solid fa-caret-down"
        ></i>
      }
    </div>
  </div>
</div>

@for (coin of sortTable(); track coin) {
  <div class="crypto">
    <div
      class="crypto-list-table"
      [ngStyle]="{
        borderBottom: currentCoin() == coin.name && showInfo() ? 'none' : ''
      }"
      [class]="{ last: $last }"
      >
      <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
        <img src="{{ coin?.logo }}" />
      </div>
      <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
        {{ coin?.ticker }}
      </div>
      <div class="crypto-list-table-name" (click)="onInfoClick($event)">
        {{ coin?.name }}
      </div>
      <div class="crypto-list-table-deposit">
        {{ coin?.deposits | depositsNoDecimal: false }}
      </div>
      <div class="crypto-list-table-current">
        {{ coin?.current | depositsNoDecimal: false }}
      </div>
      <div class="crypto-list-table-price">
        @if (coin.ticker == 'BTC' || coin.ticker == 'ETH') {
          <div>
            {{ coin.price | depositsNoDecimal: false }}
          </div>
        }
        @if (coin.ticker != 'BTC' && coin.ticker != 'ETH') {
          <div>
            {{ coin?.price | deposits: false }}
          </div>
        }
      </div>
      <div class="crypto-list-table-avgPrice">
        @if (coin.ticker == 'BTC' || coin.ticker == 'ETH') {
          <div>
            {{ coin?.averagePrice | depositsNoDecimal: false }}
          </div>
        }
        @if (coin.ticker != 'BTC' && coin.ticker != 'ETH') {
          <div>
            {{
            coin?.averagePrice > 0 ? (coin?.averagePrice | deposits: false) : ""
            }}
          </div>
        }
      </div>
      <div
        class="crypto-list-table-gain"
        [ngStyle]="{ color: coin.profits > 0 ? '#04dc00' : 'red' }"
        >
        {{ coin?.profits | profitsNoDecimal }}
      </div>
      <div
        class="crypto-list-table-gainPercent"
        [ngStyle]="{
          backgroundColor:
            coin.profits > 0 ? 'rgba(0, 100, 0, 0.4)' : 'rgba(100, 0, 0, 0.4)'
        }"
        >
        <div class="crypto-list-table-gainPercent-icon">
          @if (coin.profits > 0) {
            <i class="fa-solid fa-caret-up"></i>
          }
          @if (coin.profits < 0) {
            <i
              class="fa-solid fa-caret-down"
            [ngStyle]="{
              color: 'red'
            }"
            ></i>
          }
        </div>
        <div
          class="crypto-list-table-gainPercent-number"
          [ngStyle]="{
            color: coin.profits > 0 ? '#04dc00' : 'red'
          }"
          >
          {{ coin?.profitsPerc | profitsPerc }}
        </div>
      </div>
    </div>
    @if (!!showInfo() && currentCoin() === coin.ticker) {
      <app-crypto-info
        [coin]="coin"
        [showInfo]="showInfo()"
        [currentCoin]="currentCoin()"
      ></app-crypto-info>
    }
  </div>
}
