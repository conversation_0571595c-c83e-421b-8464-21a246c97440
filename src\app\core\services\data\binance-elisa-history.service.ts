// Binance

import { Injectable } from '@angular/core';
import { IPortfolioMonthly, IPortfolioYearly } from '../../interfaces/coins';
import { InflationService } from '../utils/inflation.service';

@Injectable({
  providedIn: 'root',
})
export class PortfolioBinanceElisaHistory {
  constructor(private inflationService: InflationService) {
    this.portfolioMonthly.forEach((month) => {
      const formatDate = this.formatDate(month.date);
      month.formatDate = formatDate;
    });
  }

  public portfolioMonthly: IPortfolioMonthly[] = [
    {
      title: 'January 2022',
      date: '01/01/2022',
      formatDate: '',
      deposits: 200,
      current: 150,
      profit: -50,
      profitPerc: -25,
    },
    {
      title: 'January 2023',
      date: '01/01/2023',
      formatDate: '',
      deposits: 202,
      current: 84,
      profit: -118,
      profitPerc: -58,
    },
    {
      title: 'January 2024',
      date: '01/01/2024',
      formatDate: '',
      deposits: 802,
      current: 740,
      profit: -62,
      profitPerc: -7.7,
    },
    {
      title: 'September 2024',
      date: '09/01/2024',
      formatDate: '',
      deposits: 1860,
      current: 1912,
      profit: 52,
      profitPerc: 2.8,
    },
    {
      title: 'October 2024',
      date: '10/01/2024',
      formatDate: '',
      deposits: 1860,
      current: 2107,
      profit: 247,
      profitPerc: 13.29,
    },
    {
      title: 'November 2024',
      date: '11/01/2024',
      formatDate: '',
      deposits: 1860,
      current: 2261,
      profit: 402,
      profitPerc: -45,
    },
    {
      title: 'December 2024',
      date: '12/01/2024',
      formatDate: '',
      deposits: 1859,
      current: 3488,
      profit: 1629,
      profitPerc: 88,
    },
    {
      title: 'January 2025',
      date: '01/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 3195,
      profit: 1336,
      profitPerc: 72,
    },
    {
      title: 'February 2025',
      date: '02/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 3101,
      profit: 1242,
      profitPerc: 66,
    },
    {
      title: 'March 2025',
      date: '03/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 2338,
      profit: 479,
      profitPerc: 26,
    },
    {
      title: 'April 2025',
      date: '04/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 1996,
      profit: 137,
      profitPerc: 7,
    },
    {
      title: 'May 2025',
      date: '05/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 1994,
      profit: 135,
      profitPerc: 7,
    },
    {
      title: 'June 2025',
      date: '06/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 2474,
      profit: 615,
      profitPerc: 33,
    },
    {
      title: 'July 2025',
      date: '07/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 2362,
      profit: 503,
      profitPerc: 27,
    },
    {
      title: 'August 2025',
      date: '08/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 3040,
      profit: 1181,
      profitPerc: 64,
    },
    {
      title: 'September 2025',
      date: '09/01/2025',
      formatDate: '',
      deposits: 1859,
      current: 3553,
      profit: 1694,
      profitPerc: 91,
    },
  ];

  public portfolioYearly: IPortfolioYearly[] = [
    {
      year: 2022,
      start: 150,
      deposits: 2,
      startAndDeposits: 152,
      fees: 0,
      withdraw: 0,
      closedTrades: 0,
      openTrades: -118,
      end: 84,
      profit: -118,
      taxes: 0,
      netProfit: -118,
      netProfitPerc: -58,
    },
    {
      year: 2023,
      start: 84,
      deposits: 600,
      startAndDeposits: 684,
      fees: -10,
      withdraw: 0,
      closedTrades: 0,
      openTrades: -62,
      end: 740,
      profit: -62,
      taxes: 0,
      netProfit: -62,
      netProfitPerc: -8,
    },
    {
      year: 2024,
      start: 740,
      deposits: 1058,
      startAndDeposits: 1798,
      fees: -21,
      withdraw: 0,
      closedTrades: 1549,
      openTrades: -139.34,
      end: 3207.66,
      profit: 1409.66,
      taxes: 0,
      netProfit: 1409.66,
      netProfitPerc: -78,
    },
  ];
  public getTotalInflation() {
    return this.inflationService.calculateEffectiveInflation(
      this.portfolioMonthly,
    );
  }

  formatDate(date: string) {
    let year: string | number = new Date(date)
      .getUTCFullYear()
      .toString()
      .substring(-2);
    let month = '' + (new Date(date).getMonth() + 1);
    let day = '' + new Date(date).getDate();

    year = date.startsWith('01/01') ? (+year + 1).toString() : year;

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return `${month}/${year}`;
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('date');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
