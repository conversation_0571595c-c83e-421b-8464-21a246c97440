import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { ChartsModule } from 'src/app/shared/charts/charts.module';
import { CryptoListModule } from 'src/app/shared/crypto-list/crypto-list.module';
import { LoaderSpinnerModule } from 'src/app/shared/loader-spinner/loader-spinner.module';
import { PriceTabModule } from 'src/app/shared/price-tab/price-tab.module';
import { AccountSelectorModule } from '../../shared/account-selector/account-selector.module';
import { HomeComponent } from './home.component';

@NgModule({
  declarations: [HomeComponent],

  imports: [
    CommonModule,
    CryptoListModule,
    ChartsModule,
    PipesModule,
    LoaderSpinnerModule,
    PriceTabModule,
    ReactiveFormsModule,
    AccountSelectorModule,
  ],
  exports: [HomeComponent],
})
export class HomeModule {}
