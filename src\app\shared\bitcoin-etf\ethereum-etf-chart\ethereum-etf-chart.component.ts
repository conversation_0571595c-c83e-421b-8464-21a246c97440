import {
  Component,
  computed,
  effect,
  Input,
  signal,
  untracked,
} from '@angular/core';
import { GoogleSheetService } from 'src/app/core/services/http/google-sheet.service';

@Component({
  selector: 'app-ethereum-etf-chart',
  templateUrl: './ethereum-etf-chart.component.html',
  styleUrl: './ethereum-etf-chart.component.scss',
})
export class EthereumEtfChartComponent {
  public chartOptions: any;
  protected chartInitialized = signal<boolean>(false);
  protected ethereumEtfHistory = computed(() =>
    this.googleSheetService
      .etfEthHistory()
      ?.sort((a, b) => +new Date(a.date) - +new Date(b.date)),
  );
  protected initChart = effect(
    () => {
      if (this.ethereumEtfHistory()?.length > 0) {
        untracked(() => this.createLineChart());
      }
    },
    { allowSignalWrites: true },
  );
  @Input() series!: string;

  constructor(private googleSheetService: GoogleSheetService) {}

  createLineChart() {
    this.chartOptions = {
      chart: {
        // type: 'line',
        // background: 'rgb(10, 10, 10)',
        width: '100%',
        height: '300px',
        foreColor: '#fff',
        fontFamily: 'Roboto, Arial, sans-serif',
        zoom: { autoScaleYaxis: true },
        toolbar: {
          show: false,
          offsetX: 0,
          offsetY: 0,
          tools: {
            download: false,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: false,
            customIcons: [],
          },
          export: {
            svg: {
              filename: undefined,
            },
            png: {
              filename: undefined,
            },
          },
          autoSelected: 'zoom',
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          opacity: 0.2,
        },
        animations: {
          enabled: false,
        },
      },
      stroke: {
        width: 2,
        curve: 'smooth',
        colors: ['#2196F3', 'transparent'],
      },

      fill: {
        // colors: ['', '', '#fff'],
        // type: 'gradient',
        // gradient: {
        //   type: 'vertical',
        //   shadeIntensity: 1,
        //   opacityFrom: 1,
        //   opacityTo: 1,
        //   // gradientToColors: ['', '', ''], // green
        //   stops: [
        //     generateColors(
        //       this.portfolioHistory.portfolioMonthly.map(
        //         (history) => history.profit
        //       )
        //     ),
        //   ],
        // },
      },

      markers: {
        // size: 3,
        // strokeWidth: 1,
        // hover: {
        //   size: 6,
        // },
      },

      grid: {
        show: true,
        borderColor: '#424754',
        padding: {
          bottom: -10,
          right: 20,
          left: -5,
        },
        xaxis: {
          lines: {
            show: false,
          },
          tooltip: {
            enabled: false,
          },
        },
      },

      // colors: [
      //   '#1e38fc',
      //   '#2196F3',
      //   function () {
      //     return portfolioHistory().portfolioMonthly[
      //       portfolioHistory().portfolioMonthly.length - 1
      //     ].profit > 0
      //       ? 'var(--green-profit)'
      //       : '#ed451f';
      //   },

      //   // console.log('VALUE', value);
      //   // // return portfolioHistory.portfolioMonthly[w.globals.seriesLog[2]]
      //   // //   .profit > 0
      //   // //   ? 'var(--green-profit)'
      //   // //   : '#ed451f';
      // ],
      series: [
        {
          name: 'Total ' + this.series.toUpperCase(),
          data: this.ethereumEtfHistory().map(
            (item) => item['total' + this.series],
          ),
          type: 'area',
          color: '#1e38fc',
          stroke: {
            color: '#fff',
            width: 1.5,
            curve: 'smooth',
          },
        },
        // {
        //   name: 'Daily ' + this.series.toUpperCase(),
        //   data: this.bitcoinEtfHistory?.map(
        //     (item) => item['daily' + this.series],
        //   ),
        //   type: 'column',
        // },
      ],
      plotOptions: {
        bar: {
          horizontal: false,
          // columnWidth: '100%',
          endingShape: 'rounded',
          dataLabels: {
            position: 'top',
          },
          colors: {
            ranges: [
              {
                from: -10000,
                to: 0,
                color: '#F15B46',
              },
              {
                from: 0,
                to: 100000,
                color: 'rgb(4, 220, 70)',
              },
            ],
          },
        },
      },
      legend: {
        show: false,
        offsetY: -10,
        markers: {
          fillColors: ['#1e38fc', '#2196F3'],
        },
      },
      xaxis: {
        type: 'datetime',
        categories: this.ethereumEtfHistory().map((item) => item.date),
        showAlways: false,
        forceNiceScale: true,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Roboto, Arial, sans-serif',
          },
          offsetY: 7,
          rotateAlways: true,
          hideOverlappingLabels: true,
          formatter: (value) => {
            let newDate = new Date(value).toLocaleString('en-us', {
              month: 'short',
              day: 'numeric',
            });

            return newDate;

            // return `${day}/${month}`;
          },
        },
      },
      yaxis: [
        {
          decimalsInFloat: 0,
          // max: this.maxY,
          // min: this.minY,
          showAlways: false,
          forceNiceScale: true,
          labels: {
            offsetX: -10,
            style: {
              fontSize: '11px',
              fontFamily: 'Roboto, Arial, sans-serif',
            },
            formatter: (value) => {
              let newValue;
              if (this.series == '$') newValue = this.formatterUsd(value);
              if (this.series == 'Eth')
                newValue = this.formatterThousands(value, 'eth');

              return newValue;
            },
          },
        },
      ],

      tooltip: {
        // x: {
        //   format: 'dd/MM/yy',
        // },
        theme: 'dark',
      },
      annotations: {
        xaxis: [],

        points: [
          {
            // x: this.bitcoinEtfHistory[this.bitcoinEtfHistory.length - 1].date,
            // y: this.bitcoinEtfHistory[this.bitcoinEtfHistory.length - 1][
            //   `'total ' + ${this.series} `
            // ],
            x: null,
            y: this.getTotalData(),
            seriesIndex: 0,
            marker: {
              size: 4,
              fillColor: '#2196F3',
              strokeColor: '#fff',
              radius: 2,
              cssClass: 'apexcharts-custom-class',
            },
            label: {
              borderColor: 'transparent',
              offsetY: 0,
              offsetX: 0,
              style: {
                color: '#fff',
                background: 'transparent',
                fontSize: '12px',
              },
              formatter: (value) => {
                let newValue;
                if (this.series == '$') newValue = this.formatterUsd(value);
                if (this.series == 'Eth')
                  newValue = this.formatterThousands(value, 'eth');

                return newValue;
              },
              text: this.formatter(this.getTotalData()),
            },
          },
          // {
          //   x: null,
          //   y: this.getDailyData(),
          //   seriesIndex: 1,
          //   marker: {
          //     size: 0,
          //     fillColor: '#2196F3',
          //     strokeColor: '#fff',
          //     radius: 2,
          //     cssClass: 'apexcharts-custom-class',
          //   },
          //   label: {
          //     borderColor: 'transparent',
          //     offsetY: 0,
          //     offsetX: 0,
          //     style: {
          //       color: '#fff',
          //       background: 'transparent',
          //       fontSize: '12px',
          //     },
          //     formatter: (value) => {
          //       let newValue;
          //       if (this.series == '$') newValue = this.formatterUsd(value);
          //       if (this.series == 'Btc')
          //         newValue = this.formatterThousands(value, 'btc');

          //       return newValue;
          //     },
          //     text: this.formatter(this.getDailyData()),
          //   },
          // },
        ],
      },
      dataLabels: {
        enabled: false,
        enabledOnSeries: [1],
        textAnchor: 'center',
        background: {
          enabled: false,
        },
      },
    };

    this.chartInitialized.set(true);
  }

  finalDate() {
    let finalDate =
      this.ethereumEtfHistory()[this.ethereumEtfHistory().length - 1].date;

    let newDate = new Date(finalDate);
    // const year = newDate.getFullYear();
    // const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
    // const day = newDate.getDate().toString().padStart(2, '0');

    // return `${year}/${month}/${day}`;
    console.log(newDate.getTime());
    return newDate.getTime();
  }

  formatterUsd(value) {
    if (isNaN(value)) return null;
    if (value === null) return null;
    if (value === 0) return '$ 0';

    const abs = Math.abs(value * 1000000);
    const isNegative = value < 0;
    let isBillion = false;

    const powers = [
      { key: ' B', value: Math.pow(10, 9) },
      { key: ' M', value: Math.pow(10, 6) },
      { key: ' K', value: 1000 },
    ];

    for (let i = 0; i < powers.length; i++) {
      if (abs >= powers[i].value) {
        isBillion = i === 0; // Imposta isBillion a true se il numero è in miliardi
        const formattedNumber = this.formatNumberWithCommas(
          abs / powers[i].value,
          i !== 2 && isBillion,
        );
        const result =
          (isNegative ? '-' : '') + '$' + formattedNumber + powers[i].key;
        return result.trim(); // Rimuovi eventuali spazi bianchi
      }
    }
  }

  formatNumberWithCommas(value: number, includeDecimal: boolean): string {
    const formatter = new Intl.NumberFormat('it-IT', {
      maximumFractionDigits: includeDecimal ? 1 : 0,
      minimumFractionDigits: includeDecimal ? 1 : 0,
      useGrouping: true,
    });

    return formatter.format(value);
  }

  formatterThousands(value, type) {
    if (isNaN(value)) return null;
    if (value === null) return null;
    if (value === 0) return '0';
    if (type == 'eth') value = value / 1000;

    const abs = Math.abs(value);
    const isNegative = value < 0;

    const formattedNumber = new Intl.NumberFormat('it-IT', {
      maximumFractionDigits: 0,
      minimumFractionDigits: 0,
      useGrouping: true,
    }).format(abs);

    if (type == 'eth') return (isNegative ? '-' : '') + formattedNumber + 'k';

    return (isNegative ? '-' : '') + formattedNumber;
  }

  formatter(value) {
    let newValue;
    if (this.series == '$') newValue = this.formatterUsd(value);
    if (this.series == 'Eth') newValue = this.formatterThousands(value, 'eth');

    return newValue;
  }

  getTotalData() {
    if (this.series == 'Eth')
      return this.ethereumEtfHistory()[this.ethereumEtfHistory().length - 1]
        .totalEth;
    if (this.series == '$')
      return this.ethereumEtfHistory()[this.ethereumEtfHistory().length - 1]
        .total$;
  }

  getDailyData() {
    if (this.series == 'Eth')
      return this.ethereumEtfHistory()[this.ethereumEtfHistory().length - 1]
        .dailyEth;
    if (this.series == '$')
      return this.ethereumEtfHistory()[this.ethereumEtfHistory().length - 1]
        .daily$;
  }
}
