<div class="crypto-list-header">
  <div class="crypto-list-header-name">Crypto</div>
  <div
    class="crypto-list-header-ath-date"
    (click)="currentSorting.set('athDate')"
    >
    Date
    @if (currentSorting() == 'athDate') {
      <i class="fa-solid fa-caret-down"></i>
    }
  </div>
  <div class="crypto-list-header-ath" (click)="currentSorting.set('athPerc')">
    ATH %
    @if (currentSorting() == 'athPerc') {
      <i class="fa-solid fa-caret-down"></i>
    }
  </div>
  <div
    class="crypto-list-header-athProfit"
    (click)="currentSorting.set('athProfit')"
    >
    ATH Profit
    @if (currentSorting() == 'athProfit') {
      <i
        class="fa-solid fa-caret-down"
      ></i>
    }
    <br />
    <div class="totalProfit">
      {{ portfolio()?.athTotalProfit | profitsNoDecimal }}
    </div>
  </div>
</div>

@for (coin of currentFilter(); track coin) {
  <div class="crypto">
    @if (coin.ticker != "USDT") {
      <div class="crypto-list-table">
        <div class="crypto-list-table-logo" (click)="onInfoClick($event)">
          <img src="{{ coin.logo }}" />
        </div>
        <div class="crypto-list-table-ticker" (click)="onInfoClick($event)">
          {{ coin.ticker }}
        </div>
        <div class="crypto-list-table-name" (click)="onInfoClick($event)">
          {{ coin.name }}
        </div>
        <div class="crypto-list-table-date">
          {{ coin.athDate | date: "MM-YY" }}
        </div>
        <div class="crypto-list-table-ath" [ngStyle]="{ color: white }">
          @if (coin.ticker == 'BTC' || coin.ticker == 'ETH') {
            <div>
              {{ coin.ath | depositsNoDecimal }}
            </div>
          }
          @if (coin.ticker != 'BTC' && coin.ticker != 'ETH') {
            <div>
              {{ coin.ath | deposits }}
            </div>
          }
        </div>
        <div
          class="crypto-list-table-gainPercent"
        [ngStyle]="{
          backgroundColor: 'darkblue'
        }"
          >
          <div
            class="crypto-list-table-gainPercent-number"
          [ngStyle]="{
            color: '#cecece'
          }"
            >
            {{ coin.athDist | percent: "1.1-1" }}
          </div>
        </div>
        <div class="crypto-list-table-athProfit">
          {{ coin.ticker == "LUNA" ? "" : (coin.athProfit | profitsNoDecimal) }}
        </div>
        <div
          class="crypto-list-table-athProfitPercent"
          [ngStyle]="{ background: coin.ticker == 'LUNA' ? 'none' : null }"
          >
          <div class="crypto-list-table-athProfitPercent-number">
            {{
            coin.ticker == "LUNA"
            ? null
            : ((coin.athProfit / coin.deposits) * 100 | profitsPerc)
            }}
          </div>
        </div>
      </div>
      <app-crypto-info
        [coin]="coin"
        [showInfo]="showInfo()"
        [currentCoin]="currentCoin()"
      ></app-crypto-info>
    }
  </div>
}
