<div class="widgets">
  @if (currentAccountService.currentUser().username === "pac") {
    @if (newsList$().length > 0) {
      <!-- TODO QUESTE SONO LEW NEWS MOBILE -->
      <!-- <div class="newsWidget-mobile">
       @for (item of newsList$(); track item.title) {
          @if ($index < 3) {
            <div class="item">
              <a class="title">{{ newsList$()[$index]?.title }}</a>

              <div class="date">
                {{ newsList$()[$index]?.timestamp | timeFromNow }}
              </div>
            </div>
          }
        }
      </div> -->
      <!-- <div class="newsWidget-mobile">
    <div class="list">
      <div class="image">
        <img
          [src]="addedNews()[addedNews().length - 1]?.image_url"
          width="100px"
          alt=""
        />
      </div>

      <div class="tags">
        @for(tag of addedNews()[addedNews().length-1]?.tags; track tag){
        <div class="tag">
          {{ tag.name }}
        </div>
        }
      </div>

      <a class="title">{{ addedNews()[addedNews().length - 1]?.title }}</a>

      <div class="date">
        {{ addedNews()[addedNews().length - 1]?.timestamp | date }} •
        {{ addedNews()[addedNews().length - 1]?.timestamp | timeFromNow }}
      </div>
    </div>
  </div> -->
    }
    <!-- 
    <app-fed-probability></app-fed-probability> -->

    <!-- BITCOIN EFT -->
    <app-bitcoin-etf></app-bitcoin-etf>
  }
  @if (newsList$().length > 0) {
    <div class="newsWidget-desktop">
      <div class="list">
        <!-- <div class="image">
        <img [src]="newsList[0].image_url" width="100px" alt="" />
      </div> -->

        <!-- <div class="tags">
        @for(tag of newsList[0]?.tags; track tag){
        <div class="tag">
          {{ tag.name }}
        </div>
        }
      </div> -->
        <a class="title" [routerLink]="'/news'">{{ newsList$()[0].title }} </a>
        <div class="date">{{ newsList$()[0].timestamp | timeFromNow }}</div>
      </div>

      <div class="list">
        <!-- <div class="image">
        <img [src]="newsList[0].image_url" width="100px" alt="" />
      </div> -->

        <!-- <div class="tags">
        @for(tag of newsList[0]?.tags; track tag){
        <div class="tag">
          {{ tag.name }}
        </div>
        }
      </div> -->
        <div class="date">{{ newsList$()[1].timestamp | timeFromNow }}</div>
        <a class="title" [routerLink]="'/news'">{{ newsList$()[1].title }} </a>
      </div>
    </div>
  }
</div>
