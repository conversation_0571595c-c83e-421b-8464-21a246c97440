// Crypto.com

import { Injectable } from '@angular/core';
import { IPortfolioMonthly } from '../../interfaces/coins';
import { InflationService } from '../utils/inflation.service';

@Injectable({
  providedIn: 'root',
})
export class PortfolioCryptoFraHistory {
  constructor(private inflationService: InflationService) {
    this.portfolioMonthly.forEach((month) => {
      const formatDate = this.formatDate(month.date);
      month.formatDate = formatDate;
    });
  }

  public portfolioMonthly: IPortfolioMonthly[] = [
    {
      title: 'September 2021',
      date: '09/01/2021',
      formatDate: '',
      deposits: 0,
      current: 0,
      profit: 0,
      profitPerc: 0,
    },
    {
      title: 'October 2021',
      date: '10/01/2021',
      formatDate: '',
      deposits: 1340.61,
      current: 1454,
      profit: 113.39,
      profitPerc: 8.45,
    },
    {
      title: 'November 2021',
      date: '11/01/2021',
      formatDate: '',
      deposits: 1578.32,
      current: 2043,
      profit: 464.68,
      profitPerc: 29.44,
    },
    {
      title: 'December 2021',
      date: '12/01/2021',
      formatDate: '',
      deposits: 3052.23,
      current: 3195,
      profit: 142.77,
      profitPerc: 4.67,
    },
    {
      title: 'January 2022',
      date: '01/01/2022',
      formatDate: '',
      deposits: 3561.93,
      current: 3339,
      profit: -222.93,
      profitPerc: -6.25,
    },
    {
      title: 'February 2022',
      date: '02/01/2022',
      formatDate: '',
      deposits: 3689.45,
      current: 2561,
      profit: -1128.45,
      profitPerc: -30.58,
    },
    {
      title: 'March 2022',
      date: '03/01/2022',
      formatDate: '',
      deposits: 3689.45,
      current: 2580,
      profit: -1109.45,
      profitPerc: -30.07,
    },
    {
      title: 'April 2022',
      date: '04/01/2022',
      formatDate: '',
      deposits: 3838.5,
      current: 3042,
      profit: -796.5,
      profitPerc: -20.75,
    },
    {
      title: 'May 2022',
      date: '05/01/2022',
      formatDate: '',
      deposits: 3988.03,
      current: 2339,
      profit: -1649.03,
      profitPerc: -41.34,
    },
    {
      title: 'June 2022',
      date: '06/01/2022',
      formatDate: '',
      deposits: 4388.08,
      current: 1755,
      profit: -2633.08,
      profitPerc: -60,
    },
    {
      title: 'July 2022',
      date: '07/01/2022',
      formatDate: '',
      deposits: 4488.78,
      current: 1755,
      profit: -2733.78,
      profitPerc: -60.9,
    },
    {
      title: 'August 2022',
      date: '08/01/2022',
      formatDate: '',
      deposits: 4596.98,
      current: 1754,
      profit: -2842.98,
      profitPerc: -61.84,
    },
    {
      title: 'September 2022',
      date: '09/01/2022',
      formatDate: '',
      deposits: 4714.67,
      current: 1708,
      profit: -3006.67,
      profitPerc: -63.77,
    },
    {
      title: 'October 2022',
      date: '10/01/2022',
      formatDate: '',
      deposits: 4732.14,
      current: 1641,
      profit: -3091.14,
      profitPerc: -65.32,
    },
    {
      title: 'November 2022',
      date: '11/01/2022',
      formatDate: '',
      deposits: 4795.36,
      current: 1720,
      profit: -3075.36,
      profitPerc: -64.13,
    },
    {
      title: 'December 2022',
      date: '12/01/2022',
      formatDate: '',
      deposits: 5026.04,
      current: 1594,
      profit: -3432.04,
      profitPerc: -68.28,
    },
    {
      title: 'January 2023',
      date: '01/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 1393,
      profit: -3633.04,
      profitPerc: -72.28,
    },
    {
      title: 'February 2023',
      date: '02/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2028,
      profit: -2998.04,
      profitPerc: -59.65,
    },
    {
      title: 'March 2023',
      date: '03/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 1990,
      profit: -3036.04,
      profitPerc: -60.4,
    },
    {
      title: 'April 2023',
      date: '04/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2195,
      profit: -2831.04,
      profitPerc: -56.32,
    },
    {
      title: 'May 2023',
      date: '05/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2170,
      profit: -2856.04,
      profitPerc: -56.82,
    },
    {
      title: 'June 2023',
      date: '06/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2069,
      profit: -2957.04,
      profitPerc: -58.83,
    },
    {
      title: 'July 2023',
      date: '07/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2025,
      profit: -3001.04,
      profitPerc: -59.71,
    },
    {
      title: 'August 2023',
      date: '08/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2108,
      profit: -2918.04,
      profitPerc: -59.71,
    },
    {
      title: 'September 2023',
      date: '09/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 1790,
      profit: -3236.04,
      profitPerc: -64.39,
    },
    {
      title: 'October 2023',
      date: '10/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 1838,
      profit: -3188.04,
      profitPerc: -63.43,
    },
    {
      title: 'November 2023',
      date: '11/01/2023',
      formatDate: '',
      deposits: 5026.04,
      current: 2166.1,
      profit: -2860.04,
      profitPerc: -56.9,
    },
    {
      title: 'December 2023',
      date: '12/01/2023',
      formatDate: '',
      deposits: 367.65,
      current: 75,
      profit: -292.65,
      profitPerc: -79.6,
    },
  ];

  public getTotalInflation() {
    return this.inflationService.calculateEffectiveInflation(
      this.portfolioMonthly,
    );
  }

  formatDate(date: string) {
    let year: string | number = new Date(date)
      .getUTCFullYear()
      .toString()
      .substring(-2);
    let month = '' + (new Date(date).getMonth() + 1);
    let day = '' + new Date(date).getDate();

    year = date.startsWith('01/01') ? (+year + 1).toString() : year;

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return `${month}/${year}`;
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('date');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
