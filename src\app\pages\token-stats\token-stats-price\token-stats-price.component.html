<div class="crypto-list" style="margin-top: 0; margin-bottom: 0">
  <div class="crypto-list-header">
    <div class="crypto-list-header-name">Token</div>
    <div class="crypto-list-header-tvl" (click)="onSortClick('7d')">
      7D
      @if (currentFilter == '7d') {
        <i
          class="fa-solid fa-caret-down"
          style="margin-left: 0.5rem"
        ></i>
      }
    </div>
    <div class="crypto-list-header-price" (click)="onSortClick('30d')">
      30D
      @if (currentFilter == '30d') {
        <i
          class="fa-solid fa-caret-down"
          style="margin-left: 0.5rem"
        ></i>
      }
    </div>
  </div>

  @for (token of cryptoList; track token) {
    <div class="crypto">
      @if(!token?.name.includes('Wrapped') && !token?.name.includes('Staked') &&
        !token?.name.includes('Pool') && !token?.symbol.includes('ETH') &&
        !token?.symbol.includes('SOL') && !token?.name.includes('Classic USD')) {
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="token?.images['60x60']" />
          </div>
          <div class="crypto-list-table-name">
            {{ token?.name }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ token?.symbol }}
          </div>
          <div
            class="crypto-list-table-gainPercent7d"
        [ngStyle]="{
          backgroundColor:
            token?.values?.USD.percentChange7d > 0
              ? 'rgba(0, 100, 0, 0.4)'
              : 'rgba(100, 0, 0, 0.4)'
        }"
            >
            <div class="crypto-list-table-gainPercent7d-icon">
              @if (token?.values?.USD.percentChange7d > 0) {
                <i
                  class="fa-solid fa-caret-up"
                ></i>
              }
              @if (token?.values?.USD.percentChange7d < 0) {
                <i
                  class="fa-solid fa-caret-down"
            [ngStyle]="{
              color: 'red'
            }"
                ></i>
              }
            </div>
            <div
              class="crypto-list-table-gainPercent7d-number"
          [ngStyle]="{
            color: token?.values?.USD.percentChange7d > 0 ? '#04dc00' : 'red',
          }"
              >
              {{ token?.values?.["USD"].percentChange7d| profitsPerc }}
            </div>
          </div>
          <div
            class="crypto-list-table-gainPercent30d"
        [ngStyle]="{
          backgroundColor:
            token?.values?.USD.percentChange30d > 0
              ? 'rgba(0, 100, 0, 0.4)'
              : 'rgba(100, 0, 0, 0.4)'
        }"
            >
            <div class="crypto-list-table-gainPercent30d-icon">
              @if (token?.values?.USD.percentChange30d > 0) {
                <i
                  class="fa-solid fa-caret-up"
                ></i>
              }
              @if (token?.values?.USD.percentChange30d < 0) {
                <i
                  class="fa-solid fa-caret-down"
            [ngStyle]="{
              color: 'red'
            }"
                ></i>
              }
            </div>
            <div
              class="crypto-list-table-gainPercent30d-number"
          [ngStyle]="{
            color: token?.values?.USD.percentChange30d > 0 ? '#04dc00' : 'red',
          }"
              >
              {{ token?.values?.["USD"].percentChange30d| profitsPerc }}
            </div>
          </div>
        </div>
      }
    </div>
  }
</div>
