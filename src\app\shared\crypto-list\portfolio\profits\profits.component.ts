import { CurrencyPipe, formatCurrency } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  input,
  signal,
  untracked,
} from '@angular/core';
import { PortfolioCoinbaseFra } from 'src/app/core/services/data/coinbase-fra.service';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-profits',
  templateUrl: './profits.component.html',
  styleUrls: ['./profits.component.scss'],
  providers: [CurrencyPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfitsComponent {
  protected showInfo = signal<boolean>(false);
  protected priceInput: string;
  protected priceInputNumber: number | string;
  protected formattedPriceInputValue: any;
  protected depositsInput: string;
  protected currentCoin = signal<string>('');
  protected profits: string;
  protected currentSorting = signal<string>('profits');
  protected sortTable = computed(() => {
    if (this.currentSorting() == 'profits')
      return this.portfolio()?.sortProfits;
    if (this.currentSorting() == 'deposits')
      return this.portfolio()?.sortDeposits;
    if (this.currentSorting() == 'profitsPerc')
      return this.portfolio()?.sortProfitsPerc;
    if (this.currentSorting() == 'current')
      return this.portfolio()?.sortCurrent;
  });

  portfolio = input<PortfolioCoinbaseFra>();

  constructor(private currentAccountService: CurrentAccountService) {
    effect(() => {
      this.currentAccountService.currentAccount()
        ? untracked(() => this.showInfo.set(false))
        : null;
    });

    console.log(
      'current account',
      this.currentAccountService.currentPortfolio(),
    );
  }

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    console.log('portfolio', this.portfolio());
  }

  onInfoClick(value) {
    if (!this.currentCoin()) this.showInfo.set(true);

    let currentCoinValue = (
      value.target.parentElement.children[1]
        ? value.target.parentElement.children[1].textContent
        : value.target.parentElement.parentElement.children[1].textContent
    ).trim();

    this.currentCoin() == currentCoinValue
      ? this.showInfo.update((prev) => !prev)
      : this.showInfo.set(true);

    this.currentCoin.set(currentCoinValue);

    this.depositsInput = '';
    this.priceInput = '';
    this.priceInputNumber = '';
  }

  onInputChange(element) {
    let elementNumber: any;
    if (element.includes(',')) {
      this.priceInputNumber = element.replaceAll('.', '').replace(',', '.');

      return;
    }

    elementNumber = element.replaceAll('.', '');
    this.priceInputNumber = elementNumber;

    let formattedValue = elementNumber.replaceAll('.', '');

    this.priceInput = formatCurrency(
      formattedValue,
      'it-IT',
      '',
      'EUR',
      '0.0-2',
    );
  }

  onInputCompleted(element) {
    this.priceInput = '€ ' + element.target.value;
  }

  onSortDepositClick() {
    this.currentSorting.set('deposits');
  }

  onSortCurrentClick() {
    this.currentSorting.set('current');
  }

  onSortProfitClick(type: string) {
    this.currentSorting.set(type);
  }
}
