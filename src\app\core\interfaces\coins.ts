export interface ICoin {
  name: string;
  nameApi: string;
  ticker: string;
  logo: string;
  description?: string;
  category?: string;
  quantity: number;
  deposits: number;
  depositsPerc?: number;
  current?: number;
  currentPerc?: number;
  price?: number;
  averagePrice?: number;
  profits?: number;
  profitsPerc?: number;
  earnQuantity?: number;
  earn?: number;
  marketCap?: number;
  rank?: number;
  ath?: number;
  athDate?: string;
  athDist?: number;
  athProfit?: number;
  change24h?: number;
  logoColor?: string;
  ecosystem?: string[];
  rating?: {
    level?: string;
    score?: number;
    progress?: string;
    team?: string;
    economics?: string;
    performance?: string;
    security?: string;
    URL?: string;
    reviewTime?: Date;
    tags?: string[];
    news?: {}[];
  };
}

export interface IPortfolioStats {
  totalDeposits?: number;
  current?: number;
  profits?: number;
  profitsPerc?: number;
  gifts: number;
  totalFees: number;
  taxes?: number;
  earn?: number;
  realizedProfit?: number;
  totalProfits?: number;
}

export interface IPortfolioMonthly {
  title: string;
  date: string;
  formatDate: string;
  deposits: number;
  current: number;
  profit: number;
  profitPerc: number;
}

export interface IPortfolioYearly {
  year: number;
  start: number;
  deposits: number;
  startAndDeposits: number;
  fees: number;
  withdraw: number;
  closedTrades: number;
  openTrades: number;
  end: number;
  taxes: number;
  profit: number;
  netProfit: number;
  netProfitPerc: number;
}

export interface ITrade {
  tradeNumber: number;
  date: string;
  coinName: string;
  ticker: string;
  quantity: number;
  buyPrice: number;
  deposit: number;
  sellPrice: number;
  revenue: number;
  profit: number;
  profitPerc: number;
  totalFees?: number;
}

export const coinDescription: { ticker: string; description: string }[] = [
  {
    ticker: 'BTC',
    description: `Bitcoin è una delle crypto più popolari sul mercato. Introdotto per la prima volta nel 2009 da Satoshi Nakamoto, Bitcoin ha mantenuto il primo posto nel mercato delle crypto per capitalizzazione. Bitcoin ha aperto la strada a molte altcoin esistenti oggi sul mercato e ha segnato un momento cruciale per le soluzioni di pagamento digitali.  
<br><br>
In quanto prima criptovaluta al mondo, Bitcoin ha fatto molta strada in termini di valore. Oggi tuttavia non è necessario comprare un intero Bitcoin, dato che questa crypto può essere suddivisa in piccole unità chiamate Satoshis, dal nome del creatore di Bitcoin. Uno Satoshi equivale a 0,00000001 Bitcoin.
<br><br>
Non esiste un token BTC fisico, quindi puoi pensare a Bitcoin come a una moneta digitale. Le transazioni in Bitcoin sono completamente trasparenti e non possono essere censurate. Puoi inviare denaro a chiunque nel mondo con estrema facilità. È un sistema finanziario supportato da migliaia di computer in tutto il mondo, chiamati "nodi", e non da una singola banca centrale o un governo. Da qui deriva il termine "decentralizzazione". `,
  },
  {
    ticker: 'ETH',
    description: `Ethereum (ETH) è il secondo token crypto più grande in termini di capitalizzazione di mercato. Questo è dovuto al fatto che ha innovato in maniera decisiva e aggiunto nuove modalità di utilizzo all'interno del settore, introducendo la funzionalità smart contract, una soluzione che ha aperto la strada alla finanza decentralizzata (DeFi) e alle app decentralizzate, o Dapp.
<br><br>
Ethereum consente agli utenti di creare e distribuire software, generalmente sotto forma di Dapps, che sono poi alimentati da una rete globale distribuita di computer, tutti in grado di eseguire Ethereum. La rete Ethereum è decentralizzata, quindi è altamente resistente a qualsiasi forma di censura o tempo di inattività.
<br><br>
Inoltre, Ethereum è una piattaforma blockchain open source che utilizza la sua valuta nativa, chiamata Ether o ETH. Tutte le commissioni sulle transazioni di rete, o commissioni gas, sono pagate in ETH. 
<br><br>
Ethereum o ETH è un token utilizzato specificamente dalla blockchain di Ethereum per pagare le transazioni. Questo token è responsabile dell'alimentazione di quasi tutto ciò che accade all'interno della rete. 
<br><br>
La rete Ethereum può essere utilizzata da chiunque per creare ed eseguire smart contract, ovvero programmi software che funzionano autonomamente, senza l'intervento dell'utente. La crescita di Ethereum può essere attribuita in parte alla sua capacità smart contract, che ha consentito un ecosistema in crescita di Dapp, token non fungibili (NFT) e altro ancora. `,
  },
  {
    ticker: 'BNB',
    description: `
    BNB è una criptovaluta che alimenta l'ecosistema BNB Chain, ed è il token nativo di BNB Beacon Chain e BNB Smart Chain. BNB può essere utilizzato per il trading e per pagare le commissioni sull'exchange di criptovalute Binance. 
<br><br>
Essendo uno degli utility token più popolari al mondo, BNB può essere utilizzato per un'ampia gamma di applicazioni e casi d'uso. 
<br><br>
BNB è stato lanciato attraverso una Initial Coin Offering (o ICO) avvenuta tra il 26 giugno e il 3 luglio 2017, 11 giorni prima dell'apertura del trading di Binance Exchange. Il prezzo di emissione era di 1 ETH per 2.700 BNB o 1 BTC per 20.000 BNB. Anche se BNB è stato lanciato attraverso una ICO, BNB non conferisce agli utenti diritti sui profitti di Binance e non rappresenta un investimento in Binance. 
<br><br>
Grazie alle numerose applicazioni sia all'interno dell'ecosistema BNB Chain che al di fuori di esso, BNB offre molti casi d'uso. Lanciato originariamente come token ERC-20 sulla blockchain di Ethereum, BNB è ora migrato su BNB Chain. Sebbene l'offerta totale iniziale fosse fissata a 200 milioni di unità, l'offerta del token è gradualmente diminuita a causa dei frequenti burn di monete. Il prezzo attuale di BNB è aggiornato e disponibile in tempo reale su Binance.
    `,
  },
  {
    ticker: 'SOL',
    description: `
    Solana è una blockchain indipendente di livello 1 creata come rete rapida ed efficiente con un protocollo smart contract sottostante. Dal suo lancio nel 2020, la rete Solana è stata individuata come la competitor principale di Ethereum, al punto da essere soprannominata "killer di Ethereum ". 
<br><br>
La scalabilità della rete, combinata con le sue basse commissioni e l'alta velocità, la rendono una soluzione leader per molte applicazioni decentralizzate basate su NFT (dApp), oltre che per la finanza decentralizzata (DeFi) e gli ecosistemi di pagamento digitali basati su blockchain.
<br><br>
La rete Solana si concentra su un elevato rendimento di transazione e su tempi di elaborazione ridotti, che ottiene grazie alla sua tecnologia Proof-of-History (PoH). Solana sostiene di essere in grado di elaborare circa 65.000 transazioni al secondo, qualificandosi come una delle più veloci del settore.
<br><br>
La blockchain Solana utilizza un meccanismo di hashing lineare delle transazioni per creare un ordine verificabile di tutta l'attività della rete e raggiungere la velocità prima indicata. Per questo motivo, non è necessario fare affidamento sul timestamp di un creatore di blocchi o sui validatori di rete per verificare che le transazioni siano avvenute nell'ordine corretto.
<br><br>
La rete Solana è stata co-fondata nel 2017 dall'ingegnere di sistema e programmatore di computer Anatoly Yakovenko e dall'attuale COO di Solana Raj Gokal.  La rete opera sul meccanismo di timing PoH implementato prima del livello di consenso che ora opera su un algoritmo di proof-of-stake (PoS).
<br><br>
Il token nativo di Solana, SOL, è utilizzato principalmente per lo staking a sostegno del processo di validazione e nelle commissioni di transazione per i pagamenti peer-to-peer (P2P). Il modo migliore per adempiere a queste funzioni è un'offerta illimitata di token. Tuttavia, la blockchain effettua un burning del 50% del SOL utilizzato in ogni commissione di transazione, come parte della formula per mantenere stabile il suo tasso di inflazione annuale.
<br><br>
Il restante 50% della commissione di transazione viene assegnato ai validatori della transazione. Chiunque possieda un numero sufficiente di SOL può diventare un validatore della rete o un delegatore di un validatore e sostenere quindi il processo di consenso necessario per gestire la blockchain indipendente. Questo meccanismo genera ricompense per gli utenti che mettono in stake e supportano la blockchain. Il prezzo di Solana viene aggiornato in tempo reale su Binance.
<br><br>
Il 3 agosto 2022 si è verificata un grave data breach della rete Solana. Le app wallet decentralizzate, Phantom e Slope, che funzionano entrambe in conformità con la rete Solana, hanno comunicato sui social media gli attacchi subito, in modo da informare il pubblico.
<br><br>
In totale, gli attacchi hanno portato a una stima di 8 milioni di dollari in SOL che sono stati prelevati da circa 8.000 portafogli. Alla fine è emerso che tutti wallet coinvolti avevano interagito con altre applicazioni all'interno dei sistemi operativi per dispositivi mobili iOS e Android. La fonte dell'hacking e la possibilità che i proprietari dei wallet danneggiati vengano rimborsati sono rimaste sconosciute.
    `,
  },
  {
    ticker: 'MATIC',
    description: `
    Polygon (MATIC), precedentemente conosciuto come MATIC Network, è una soluzione di interoperabilità layer-2 per blockchain compatibili con Ethereum. La criptovaluta nativa di Polygon, MATIC, viene utilizzata per la governance, lo staking e le commissioni gas. 
<br><br>
Polygon è stato sviluppato per superare le limitazioni di Ethereum, tra cui il throughput, la velocità delle transazioni e la mancanza di governance della community. Per design, Polygon è una piattaforma creata per il lancio di blockchain interoperabili. 
<br><br>
Polygon raggiunge questo obiettivo utilizzando checkpoint proof of stake costruiti sulla mainchain di Ethereum e sul suo More Viable Plasma, una variante del Plasma Network.`,
  },
  {
    ticker: 'ADA',
    description: `
    Cardano (ADA) è una piattaforma blockchain decentralizzata proof-of-stake (PoS) lanciata nel 2017. È stata concettualizzata per la prima volta nel 2015 da Charles Hoskinson, uno dei co-fondatori della rete blockchain Ethereum. 
<br><br>
Cardano è stato sviluppato per risolvere i limiti infrastrutturali e di scalabilità della blockchain di prima generazione Bitcoin e delle reti di seconda generazione come Ethereum. In effetti, Cardano è considerato un'alternativa più scalabile di Ethereum. Il prezzo di Cardano è aggiornato in tempo reale su Binance. 
<br><br>
Ad oggi, la rete blockchain Cardano è una delle più grandi ad aver implementato con successo un meccanismo di consenso PoS più efficiente in termini di potenza rispetto alle reti blockchain alternative proof-of-work (PoW). La funzione sulla blockchain Cardano è facilitata dalle funzionalità concesse dal protocollo smart, una tecnologia inizialmente diffusa da Ethereum.
<br><br>
Il token nativo della criptovaluta Cardano è ADA, che consente agli utenti di partecipare alle operazioni di rete. Il token prende il nome da Ada Lovelace, una matematica del XIX secolo riconosciuta come la prima programmatrice informatica al mondo. `,
  },
  {
    ticker: 'XRP',
    description: `
    Ripple (XRP) è una rete e un protocollo di pagamento digitale che viene utilizzato per regolare pagamenti, scambiare asset e trasferire denaro. 
<br><br>
Originariamente fondato da Jed McCaleb e Chris Larsen nel 2012 sotto il nome di "OpenCoin", Ripple fu creato per rendere le persone indipendenti da istituzioni finanziarie come banche e carte di credito. 
<br><br>
La crypto nativa di Ripple, XRP, alimenta la blockchain sottostante nota come “XRP ledger".  A differenza di altre blockchain crypto, solo i partecipanti pre-approvati possono contribuire a potenziare e convalidare le transazioni sul ledger XRP. 
<br><br>
Tieni presente che i nodi non guadagnano token mantenendo lo storico delle transazioni del ledger XRP. Quando la società è stata lanciata inizialmente, tutti i 100 miliardi di XRP erano già stati creati e distribuiti tra privati e aziende. 
<br><br>
Invece delle commissioni sulle transazioni, il ledger XRP deduce un piccolo importo di token dall'offerta totale ogni volta che un mittente effettua una transazione.`,
  },
  {
    ticker: 'FET',
    description: `
    Il token $FET è la valuta digitale essenziale per le operazioni di rete all'interno dell'ecosistema Fetch.AI. Viene utilizzato per eseguire transazioni e facilitare una comunicazione sicura con le crypto, alimentando così la rete Fetch.AI.
 Inoltre, ai nodi e agli agenti viene richiesto di mettere in staking una determinata quantità di token in cambio di specifiche qualifiche operative come meccanismo di sicurezza per prevenire comportamenti dannosi.
 Fetch.AI combina l'apprendimento automatico, l'intelligenza artificiale, i sistemi multi-agente e la tecnologia a registro distribuito per costruire la sua rete e condividere le conoscenze attraverso silos di dati con potenziali applicazioni nei settori finanziario, assicurativo e sanitario. Fetch.AI sostiene di rendere i sistemi autonomi disponibili per tutti i mercati. Attualmente, l'autonomia del mercato richiede risorse umane e materiali. Fetch.AI afferma di automatizzare le transazioni economiche e di aiutare i mercati a funzionare in modo efficiente.
<br><br>
Fetch.AI è una rete digitale decentralizzata che costruisce l'economia digitale del futuro implementando l'apprendimento automatico, criptovalute avanzate e agenti autonomi sulla blockchain.
<br><br>
Con i token FET, gli utenti possono creare e distribuire agenti sulla rete. Questo processo è simile allo staking, che attribuisce all'agente il diritto di operare nella rete. Gli sviluppatori possono utilizzare le funzionalità di apprendimento automatico per formare agenti autonomi e distribuire l'intelligenza collettiva sulla rete pagando in token FET al momento dell'implementazione. I titolari di token possono anche partecipare allo staking per convalidare i servizi di rete. Gli utenti attivano i nodi di verifica mettendo in staking token FET, aiutando la rete a convalidare le transazioni.`,
  },
  {
    ticker: 'TRON',
    description: `
    Tron è una blockchain fondata nel 2017 da Justin Sun con un meccanismo di consenso Delegated Proof of Stake. La Tron Foundation, con sede a San Francisco e Singapore, si occupa dello sviluppo della rete. L'utilizzo principale delle blockchain è premiare i creatori di contenuti per il loro lavoro senza che un intermediario intaschi una percentuale. 
<br><br>
Puoi utilizzare il token nativo della rete, Tronix (TRX), per premiare un creatore specifico di cui hai visualizzato i contenuti. Il protocollo Tron ha tre livelli separati che alimentano le sue operazioni: un livello di archiviazione, un livello di applicazione e un livello core. Nel meccanismo di consenso Proof of Stake delegated di Tron, 27 Super Rappresentanti vengono selezionati ogni 6 ore per proporre blocchi e convalidare le transazioni.
<br><br>
TRX è stato minato su Ethereum, ma si è trasferito al protocollo Tron nel 2018 quando è stata lanciata la mainnet. Ha una fornitura illimitata e puoi guadagnare ricompense partecipando al sistema di voto della rete Tron. Puoi anche utilizzare Tron per creare DApp e smart contract e accedere a un ecosistema di progetti di finanza decentralizzata.`,
  },
  {
    ticker: 'LINK',
    description: ``,
  },
];

export class Coin {
  public coins: ICoin[];
  public portfolioStats: IPortfolioStats;
  public closedTrades: ITrade[];

  public calculateStats() {
    this.portfolioStats.totalDeposits = this.coins.reduce(
      (acc, coin) => acc + coin.deposits,
      0,
    );
    this.coins.forEach((coin) => {
      // if (coin.earnQuantity) {
      //   coin.earn = coin.earnQuantity * coin.price;
      //   coin.quantity = coin.quantity + coin.earnQuantity;
      // }
      coin.depositsPerc =
        (coin.deposits / this.portfolioStats.totalDeposits) * 100;
      coin.current = coin.quantity * coin.price;
      coin.profits = coin.current - coin.deposits;
      coin.profitsPerc = (coin.profits / coin.deposits) * 100;
      coin.athDist = (coin.price - coin.ath) / coin.ath;
      coin.averagePrice = coin.deposits / coin.quantity;
      if (!coin.rating) coin.rating = { level: 'Other' };
      let descriptionFound = coinDescription.find(
        (crypto) => crypto.ticker == coin.ticker,
      );
      if (descriptionFound) coin.description = descriptionFound.description;
    });

    this.portfolioStats.current = +this.coins
      .reduce((acc, coin) => acc + coin.current, 0)
      .toFixed(2);

    this.coins.forEach((coin) => {
      coin.currentPerc = (coin.current / this.portfolioStats.current) * 100;
    });

    this.coins.forEach((coin) => {
      if (coin.deposits == 0) coin.athProfit = coin.ath * coin.quantity;

      if (coin.deposits != 0)
        coin.athProfit =
          ((coin.ath - coin.averagePrice) / coin.averagePrice) * coin.deposits;
    });

    this.portfolioStats.profits = +(
      this.portfolioStats.current - this.portfolioStats.totalDeposits
    ).toFixed(2);

    this.portfolioStats.profitsPerc =
      (this.portfolioStats.profits / this.portfolioStats.totalDeposits) * 100;

    this.portfolioStats.totalProfits =
      this.portfolioStats.profits +
      (this.closedTrades?.reduce((acc, trade) => acc + trade.profit, 0) || 0);
  }

  public get sortProfits() {
    return this.coins.sort((a, b) => b.profits - a.profits);
  }

  public get sortProfitsPerc() {
    const newCoins = this.coins.slice();
    const positivi = newCoins.filter(
      (item) => item.profitsPerc >= 0 && item.profitsPerc != Infinity,
    );
    const negativi = newCoins.filter(
      (item) => item.profitsPerc < 0 && item.profitsPerc != Infinity,
    );
    const infiniti = newCoins.filter((item) => item.profitsPerc == Infinity);

    positivi.sort((a, b) => b.profitsPerc - a.profitsPerc);
    negativi.sort((a, b) => b.profitsPerc - a.profitsPerc);
    infiniti.sort((a, b) => b.profits - a.profits);

    return [...positivi, ...infiniti, ...negativi];
  }
  public get sortCurrent() {
    return this.coins.sort((a, b) => b.current - a.current);
  }

  public get sortDeposits() {
    return this.coins.sort((a, b) => b.deposits - a.deposits);
  }

  public get sortAthPerc() {
    return this.coins.sort((a, b) => b.athDist - a.athDist);
  }

  public get sortAthProfit() {
    return this.coins.sort((a, b) => {
      if (a.ticker == 'LUNA') return 1;
      if (b.ticker == 'LUNA') return -1;
      return b.athProfit - a.athProfit;
    });
  }

  public get athTotalProfit() {
    return this.coins.reduce(
      (acc, curr) =>
        curr.ticker == 'LUNA' || curr.ticker == 'USDT'
          ? acc
          : acc + curr.athProfit,
      0,
    );
  }

  public get sortAthDate() {
    return this.coins.sort((a, b) => {
      if (!a.athDate) return 1;
      if (!b.athDate) return -1;
      let dateA = new Date(a.athDate);
      let dateB = new Date(b.athDate);
      return +dateB - +dateA;
    });
  }

  public get sortH24() {
    return this.coins.sort((a, b) => b.change24h - a.change24h);
  }
  public get sortH24Top3() {
    let coins: ICoin[];

    let newCoins = this.coins.slice();

    newCoins.find((item) => item.ticker == 'USDT')
      ? (coins = this.coins.filter((item) => item.ticker != 'USDT'))
      : (coins = this.coins);

    coins = coins.sort((a, b) => b.change24h - a.change24h);
    return [coins[0], coins[1], coins[2]];
  }

  public get sortRank() {
    return this.coins.sort((a, b) => {
      if (!a.rank) return 1;
      if (!b.rank) return -1;
      return a.rank - b.rank;
    });
  }

  public get sortEarn() {
    let earnCoins = this.coins.filter((item) => item.earnQuantity);
    return earnCoins.sort((a, b) => b.earn - a.earn);
  }

  public get earnTotalProfit() {
    let newCoins = this.coins.slice();
    return newCoins.reduce(
      (acc, curr) => (curr.earn ? acc + curr.earn : acc),
      0,
    );
  }

  public get sortCurrentPerc() {
    let newCoins = this.sortCurrent.slice();
    return newCoins.map((coin) => coin.currentPerc);
  }

  public get sortCurrentPercName() {
    let newCoins = this.sortCurrent.slice();
    return newCoins.map((coin) => coin.name);
  }

  public get sortCurrentPercColor() {
    let newCoins = this.sortCurrent.slice();
    return newCoins.map((coin) => coin.logoColor);
  }

  public get coinsInProfit() {
    return this.coins.reduce(
      (acc, currValue) => (currValue.profits > 0 ? acc + 1 : acc),
      0,
    );
  }

  public get sortRatingScore() {
    let newCoins = this.coins.slice();

    newCoins.forEach((coin) =>
      coin.rating.level ? null : (coin.rating = { level: 'Other' }),
    );
    // console.log('COINS RATING', newCoins);

    return newCoins.sort((a, b) => {
      if (!a.rating.score) return 1;
      if (!b.rating.score) return -1;

      return b.rating.score - a.rating.score;
    });
  }

  public get sortRatingLevel() {
    let newCoins = this.coins.slice();

    return newCoins.sort((a, b) => {
      const ratingOrder = {
        AAA: 10,
        AA: 9,
        A: 8,
        BBB: 7,
        BB: 6,
        B: 5,
        CCC: 4,
        CC: 3,
        C: 2,
        D: 1,
        Other: 0,
      };

      // if (!ratingOrder.hasOwnProperty(a.rating.level)) return 1;
      // if (!ratingOrder.hasOwnProperty(b.rating.level)) return -1;

      return ratingOrder[b.rating.level] - ratingOrder[a.rating.level];
    });
  }
  public get portfolioRatings() {
    let newCoins = this.sortDeposits.slice();

    newCoins.forEach((coin) =>
      coin.rating.level ? null : (coin.rating = { level: 'Other' }),
    );

    let uniqueRatings = [
      ...new Set([...newCoins.map((crypto) => crypto.rating.level)]),
    ];

    let ratingDistribution = [];

    ratingDistribution = uniqueRatings.map((rating) => {
      let filteredCrypto = newCoins.filter(
        (crypto) => crypto.rating.level === rating,
      );

      const totalDeposits = filteredCrypto.reduce(
        (acc, crypto) => acc + crypto.deposits,
        0,
      );
      const depositsPerc = (
        (totalDeposits / this.portfolioStats.totalDeposits) *
        100
      ).toFixed(2);

      return {
        rating: rating,
        deposits: totalDeposits,
        depositsPerc: depositsPerc,
        coins: [...newCoins.filter((coin) => coin.rating.level == rating)],
      };
    });

    ratingDistribution.forEach((item) => {
      item.coins.sort((a, b) => b.deposits - a.deposits);
    });

    // console.log(
    //   ratingDistribution.sort((a, b) => b.depositsPerc - a.depositsPerc)
    // );
    return ratingDistribution.sort((a, b) => b.depositsPerc - a.depositsPerc);
  }

  public get portfolioAverageScore() {
    // Filtra gli oggetti che hanno il valore 'raiting' definito
    let oggettiConRaiting = this.coins.slice();
    oggettiConRaiting = oggettiConRaiting.filter((coin) => coin.deposits > 0);

    // Calcola la somma dei prodotti tra score e deposits per ogni oggetto
    const sommaProdotti = oggettiConRaiting.reduce((acc, curr) => {
      const score = curr.rating.score ? curr.rating.score : 30; // Usa uno score di 30 per quelli senza rating
      return acc + score * curr.deposits;
    }, 0);

    // Calcola la somma totale dei depositi
    const sommaDepositi = oggettiConRaiting.reduce((acc, curr) => {
      return acc + curr.deposits;
    }, 0);

    const mediaPonderata = Math.ceil(sommaProdotti / sommaDepositi);
    let ratingLevel;

    if (mediaPonderata >= 80 && mediaPonderata <= 100) {
      ratingLevel = 'AAA';
    } else if (mediaPonderata >= 75 && mediaPonderata < 80) {
      ratingLevel = 'AA';
    } else if (mediaPonderata >= 69 && mediaPonderata < 75) {
      ratingLevel = 'A';
    } else if (mediaPonderata >= 62 && mediaPonderata < 69) {
      ratingLevel = 'BBB';
    } else if (mediaPonderata >= 54 && mediaPonderata < 62) {
      ratingLevel = 'BB';
    } else if (mediaPonderata >= 47 && mediaPonderata < 54) {
      ratingLevel = 'B';
    } else {
      ratingLevel = 'N/A';
    }

    // console.log('RATING FINALE', {
    //   average: mediaPonderata,
    //   rating: ratingLevel,
    // });

    return { average: mediaPonderata, rating: ratingLevel };
  }

  // public get portfolioAverageScore() {
  //   // Filtra gli oggetti che hanno il valore 'raiting' definito
  //   const oggettiConRaiting = this.coins.filter(
  //     (coin) =>
  //       coin.rating && coin.rating.score !== undefined && coin.deposits > 0
  //   );

  //   // Calcola la somma dei prodotti tra score e deposits per ogni oggetto
  //   const sommaProdotti = oggettiConRaiting.reduce((acc, curr) => {
  //     return acc + curr.rating.score * curr.deposits;
  //   }, 0);

  //   // Calcola la somma totale dei depositi
  //   const sommaDepositi = oggettiConRaiting.reduce((acc, curr) => {
  //     return acc + curr.deposits;
  //   }, 0);

  //   const mediaPonderata = Math.ceil(sommaProdotti / sommaDepositi);
  //   let ratingLevel;

  //   if (mediaPonderata >= 80 && mediaPonderata <= 100) {
  //     ratingLevel = 'AAA';
  //   } else if (mediaPonderata >= 75 && mediaPonderata < 80) {
  //     ratingLevel = 'AA';
  //   } else if (mediaPonderata >= 69 && mediaPonderata < 75) {
  //     ratingLevel = 'A';
  //   } else if (mediaPonderata >= 62 && mediaPonderata < 69) {
  //     ratingLevel = 'BBB';
  //   } else if (mediaPonderata >= 54 && mediaPonderata < 62) {
  //     ratingLevel = 'BB';
  //   } else if (mediaPonderata >= 47 && mediaPonderata < 54) {
  //     ratingLevel = 'B';
  //   } else {
  //     ratingLevel = 'N/A';
  //   }

  //   return { average: mediaPonderata, rating: ratingLevel };
  // }

  public get realizedProfit() {
    return (
      this.closedTrades?.reduce((acc, trade) => acc + trade.profit, 0) || 0
    );
  }

  public get sortRatingReviewTime() {
    let newCoins = this.coins.slice();

    return newCoins.sort((a, b) => {
      if (!a.rating.reviewTime) return 1;
      if (!b.rating.reviewTime) return -1;
      let dateA = new Date(a.rating.reviewTime);
      let dateB = new Date(b.rating.reviewTime);
      return +dateB - +dateA;
    });
  }

  public get sortAZ() {
    let newCoins = this.coins.slice();
    return newCoins.sort((a, b) => a.ticker.localeCompare(b.ticker));
  }

  public get sortCategory() {
    let category = [];
    let totalDeposits = 0;

    let newCoins = this.coins.slice();

    // Calcola il totale dei depositi
    newCoins.forEach((coin: ICoin) => {
      totalDeposits += coin.deposits;
    });

    // Categorizza e calcola la percentuale dei depositi
    newCoins.forEach((coin: ICoin) => {
      if (!coin.category) {
        // Elemento senza categoria, aggiungi a "Other"
        const otherCategory = category.find((item) => item.name === 'Other');
        if (otherCategory) {
          otherCategory.deposits += coin.deposits;
          otherCategory.coins.push(coin);
        } else {
          category.push({
            name: 'Other',
            deposits: coin.deposits,
            coins: [coin],
          });
        }
      } else {
        // Elemento con categoria
        const existingCategory = category.find(
          (item) => item.name === coin.category,
        );

        if (existingCategory) {
          existingCategory.deposits += coin.deposits;
          existingCategory.coins.push(coin);
        } else {
          category.push({
            name: coin.category,
            deposits: coin.deposits,
            coins: [coin],
          });
        }
      }
    });

    // Calcola la percentuale per ogni categoria
    category.forEach(
      (item) =>
        (item.depositsPerc =
          (item.deposits / this.portfolioStats.totalDeposits) * 100),
    );

    // Aggiungi il sort dell'array per depositi di ogni categoria
    category.forEach((item) => {
      item.coins.sort((a, b) => b.deposits - a.deposits);
    });

    return category.sort((a, b) => b.deposits - a.deposits);
  }

  private shouldFetchData() {
    const storedDate = localStorage.getItem('date');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 2) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
