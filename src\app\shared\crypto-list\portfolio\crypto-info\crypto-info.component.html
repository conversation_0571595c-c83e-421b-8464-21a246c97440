@if (currentCoin == coin().ticker && showInfo()) {
  <div class="crypto-info-header">
    <div
      class="info"
      [ngClass]="{ active: selectedTab() === 'chart' }"
      (click)="selectedTab.set('chart')"
    >
      Chart
    </div>
    <div
      class="chart"
      [ngClass]="{ active: selectedTab() === 'info' }"
      (click)="selectedTab.set('info')"
    >
      Info
    </div>
  </div>

  @if (selectedTab() === "chart") {
    <div class="crypto-chart">
      <!-- TradingView Widget BEGIN -->
      <div
        class="tradingview-widget-container {{ 'container-' + coin().ticker }}"
        #container
        style="height: 100%; width: 100%"
      >
        <div
          class="tradingview-widget-container__widget"
          style="height: calc(100% - 32px); width: 100%"
        ></div>
        <div class="tradingview-widget-copyright">
          <a
            href="https://www.tradingview.com/"
            rel="noopener nofollow"
            target="_blank"
            ><span class="blue-text">Track all markets on TradingView</span></a
          >
        </div>
      </div>
      <!-- TradingView Widget END -->
    </div>
  }
  @if (selectedTab() === "info") {
    <div class="crypto-info">
      <table class="crypto-info-table">
        <tr>
          <td class="text">Quantity:</td>
          <td class="value">
            {{ coin().quantity | quantity }}
          </td>
        </tr>

        <tr>
          <td class="text">Deposits:</td>
          <td class="value">
            {{ coin().deposits | depositsNoDecimal }} <br />
            {{ coin().depositsPerc | profitsPerc }}
          </td>
        </tr>

        <tr>
          <td class="text">Current:</td>
          <td class="value">
            {{ coin().current | depositsNoDecimal }} <br />
            {{ coin().currentPerc | profitsPerc }}
          </td>
        </tr>

        <tr>
          <td class="text">Profit:</td>
          <td
            class="value"
            [ngStyle]="{
              color: coin().profits > 0 ? '#04dc00' : 'red'
            }"
          >
            {{ coin().profits | profitsNoDecimal }} <br />
            {{ coin().profitsPerc | profitsPerc }}
          </td>
        </tr>

        <tr>
          <td class="text">Price:</td>
          <td class="value">{{ coinPrice(coin().price) }}</td>
        </tr>

        <tr>
          <td class="text">Avg Price:</td>
          <td class="value">
            {{ coinPrice(coin().averagePrice) }}
          </td>
        </tr>

        <tr>
          <td class="text">ATH:</td>
          <td class="value">{{ coinPrice(coin().ath) }}</td>
        </tr>

        <tr>
          <td class="text">ATH Profit:</td>
          <td
            class="value"
            [ngStyle]="{
              color:
                ((coin().ath - coin().averagePrice) / coin().averagePrice) *
                  coin().deposits <
                0
                  ? 'red'
                  : '#04dc00'
            }"
          >
            {{ coin().athProfit | profitsNoDecimal }}
            <br />
            {{
              ((coin().ath - coin().averagePrice) / coin().averagePrice) * 100
                | profitsPerc
            }}
          </td>
        </tr>
      </table>
      <div class="crypto-info-simulator">
        <div class="crypto-info-simulator-title">SIMULATOR</div>
        <div class="crypto-info-simulator-price">
          <span>PRICE</span>
          <input
            [ngModel]="priceInput"
            name="priceInput"
            type="text"
            (ngModelChange)="onInputChange($event)"
            (blur)="onInputCompleted($event)"
            [placeholder]="coin().price | deposits"
            onfocus="value=''"
          />
        </div>

        <!-- <div class="crypto-info-simulator-deposits">
          <span>DEPOSITS</span>
          <input
            id="deposits"
            name="depositsInput"
            [(ngModel)]="depositsInput"
            type="text"
            [placeholder]="coin.deposits | deposits"
          />
        </div> -->
        <div class="crypto-info-simulator-profit">
          <span>PROFIT</span>
          <div
            #profitsValue
            [ngStyle]="{
              color:
                (priceInputNumber / coin().averagePrice) * coin().deposits -
                  coin().deposits >
                0
                  ? '#04dc00'
                  : 'red'
            }"
          >
            {{
              priceInputNumber > 0
                ? ((priceInputNumber / coin().averagePrice) * coin().deposits -
                    coin().deposits | profitsNoDecimal)
                : ""
            }}
          </div>
        </div>
        <div class="crypto-info-simulator-profitPerc">
          <span>PROFIT %</span>
          <div
            [ngStyle]="{
              color:
                ((priceInputNumber - coin().averagePrice) / priceInputNumber) *
                  coin().deposits >
                0
                  ? '#04dc00'
                  : 'red'
            }"
          >
            {{
              priceInputNumber > 0
                ? ((((priceInputNumber / coin().averagePrice) *
                    coin().deposits -
                    coin().deposits) /
                    coin().deposits) *
                    100 | profitsPercSimulator)
                : ""
            }}
          </div>
        </div>
      </div>
    </div>
  }
}
