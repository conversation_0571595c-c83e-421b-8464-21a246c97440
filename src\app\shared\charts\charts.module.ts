import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { ChartsComponent } from './charts.component';
import { ColumnchartModule } from './columnchart/columnchart.module';
import { LinechartModule } from './linechart/linechart.module';
import { PiechartModule } from './piechart/piechart.module';

@NgModule({
  declarations: [ChartsComponent],
  imports: [
    CommonModule,
    LinechartModule,
    PiechartModule,
    ColumnchartModule,
    PipesModule,
  ],
  exports: [ChartsComponent],
})
export class ChartsModule {}
