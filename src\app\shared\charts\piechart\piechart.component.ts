import { AfterViewInit, Component, ViewChild, effect } from '@angular/core';
import { ChartComponent } from 'ng-apexcharts';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';

@Component({
  selector: 'app-piechart',
  templateUrl: './piechart.component.html',
  styleUrls: ['./piechart.component.scss'],
})
export class PiechartComponent implements AfterViewInit {
  protected portfolioHistory =
    this.currentAccountService.currentPortfolioHistory;

  protected portfolio = this.currentAccountService.currentPortfolio;

  @ViewChild('chart', { static: false }) chart: ChartComponent;
  public chartOptions: any;

  constructor(private currentAccountService: CurrentAccountService) {
    // console.log('PORTFOLIO', this.portfolio());
    this.createPieChart();
    effect(() =>
      this.currentAccountService.currentPortfolioHistory().portfolioMonthly
        ? this.createPieChart()
        : null,
    );
  }

  create<PERSON>ie<PERSON><PERSON>() {
    this.chartOptions = {
      chart: {
        width: '100%',
        type: 'donut',
        height: '350px',
        fontFamily: 'Roboto, Arial, sans-serif',
      },
      series: this.portfolio().sortCurrentPerc.filter((item) => {
        return item > 0.1;
      }),
      legend: {
        horizontalAlign: 'center',
        offsetY: 30,
        position: 'right',
        fontSize: '14px',
        fontFamily: 'Roboto',
        labels: {
          colors: '#fff',
          useSeriesColors: false,
        },
        markers: {
          shape: 'circle',
          strokeWidth: 0,
        },
        customLegendItems: [
          this.portfolio().sortCurrent[0].name,
          this.portfolio().sortCurrent[1].name,
          this.portfolio().sortCurrent[2].name,
          this.portfolio().sortCurrent[3].name,
          this.portfolio().sortCurrent[4].name,
        ],
      },
      colors: this.portfolio().sortCurrentPercColor,
      labels: this.portfolio().sortCurrentPercName,
      stroke: {
        colors: undefined,
      },

      plotOptions: {
        pie: {
          customScale: 1,
          donut: {
            size: '55%',
          },
          offsetY: 10,
          expandOnClick: true,
        },
        stroke: {
          colors: undefined,
        },
      },
      tooltip: {
        theme: 'dark',
      },
    };
  }

  ngAfterViewInit() {}
}
