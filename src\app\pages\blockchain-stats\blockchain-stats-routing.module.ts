import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { authGuard } from 'src/app/core/guards/auth.guard';
import { BlockchainStatsComponent } from './blockchain-stats.component';

const routes: Routes = [
  { path: '', component: BlockchainStatsComponent, canActivate: [authGuard] },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BlockchainStatsRoutingModule {}
