<app-loader-spinner [loading]="loading()"></app-loader-spinner>

@if (!loading()) {
  <div class="crypto-list" style="margin-top: 0; margin-bottom: 0">
    <!-- <div class="crypto-list-title" style="margin-bottom: 1rem">
    Total Value Locked
  </div> -->

  <!-- Buttons -->
  <div class="crypto-list-buttons" style="margin-bottom: 0">
    <div
      class="crypto-list-buttons-tvl"
      (click)="onTableFilterChange('general')"
      [ngClass]="{ selected: tableFilter() == 'general' }"
      >
      General
    </div>
    <div
      class="crypto-list-buttons-tvl"
      (click)="onTableFilterChange('detail')"
      [ngClass]="{ selected: tableFilter() == 'detail' }"
      >
      Detail
    </div>
    <div
      class="crypto-list-buttons-tvl"
      (click)="onTableFilterChange('price')"
      [ngClass]="{ selected: tableFilter() == 'price' }"
      >
      Price
    </div>

    <div class="extra"></div>
  </div>
  <!-- Buttons END -->
</div>

@if (tableFilter() == "general") {
  <div class="crypto-list table" style="margin-top: 0; margin-bottom: 0">
    <div class="crypto-list-header general">
      <div class="crypto-list-header-name">Token</div>
      <div class="crypto-list-header-tvl">Market Cap</div>
      <div class="crypto-list-header-price" (click)="onSortRankCapClick()">
        Rank
        @if (currentSorting == 'rank') {
          <i
            class="fa-solid fa-caret-down"
            style="margin-left: 0.5rem"
          ></i>
        }
      </div>
    </div>

    @for (token of tokenFilter; track token) {
      <div class="crypto">
        @if (
          !token?.name.includes("Wrapped") &&
          !token?.name.includes("Staked") &&
          !token?.name.includes("Pool") &&
          !token?.symbol.includes("ETH") &&
          !token?.symbol.includes("SOL") &&
          !token?.name.includes("Classic USD")
          ) {
          <div class="crypto-list-table general">
            <div class="crypto-list-table-logo">
              <img [src]="token?.images['60x60']" />
            </div>
            <div class="crypto-list-table-name">
              {{ token?.name }}
            </div>
            <div class="crypto-list-table-ticker">
              {{ token?.symbol }}
            </div>
            <div class="crypto-list-table-tvl">
              {{ token?.values?.["USD"].marketCap | shortNumber }}
            </div>
            <div class="crypto-list-table-price">
              <div>
                {{ token?.rank }}
              </div>
            </div>
          </div>
        }
      </div>
    }
  </div>
}
@if (tableFilter() == "detail") {
  <div class="crypto-list" style="margin-top: 0; margin-bottom: 0">
    <div class="crypto-list-header detail">
      <div class="crypto-list-header-name">Token</div>
      <div
        class="crypto-list-header-category"
        (click)="onSortCategoryClick()"
        >
        Category
        @if (currentSorting == 'category') {
          <i
            class="fa-solid fa-caret-down"
            style="margin-left: 0.3rem"
          ></i>
        }
      </div>
      <div class="crypto-list-header-price" (click)="onSortChainsClick()">
        Chains
        @if (currentSorting == 'chains') {
          <i
            class="fa-solid fa-caret-down"
            style="margin-left: 0.3rem"
          ></i>
        }
      </div>
    </div>

    @for (token of tokenFilter; track token) {
      <div class="crypto">
        @if (
          !token?.name.includes("Wrapped") &&
          !token?.name.includes("Staked") &&
          !token?.name.includes("Pool") &&
          !token?.symbol.includes("ETH") &&
          !token?.symbol.includes("SOL") &&
          !token?.name.includes("Classic USD")
          ) {
          <div class="crypto-list-table detail">
            <div class="crypto-list-table-logo">
              <img [src]="token?.images['60x60']" />
            </div>
            <div class="crypto-list-table-name">
              {{ token?.name }}
            </div>
            <div class="crypto-list-table-ticker">
              {{ token?.symbol }}
            </div>
            <div class="crypto-list-table-category">
              {{ token?.category }}
            </div>
            <div class="crypto-list-table-price">
              <div>
                {{ token?.tokens?.length }}
              </div>
            </div>
          </div>
        }
      </div>
    }
  </div>
}
@if (tableFilter() == "price") {
  <app-token-stats-price [cryptoList]="tokenList"></app-token-stats-price>
}
}
