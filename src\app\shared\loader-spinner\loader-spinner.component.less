:root {
  --hue: 223;
  --bg: hsl(223, 10%, 90%);
  --fg: hsl(223, 10%, 10%);
  font-size: calc(16px + (24 - 16) * (100vw - 320px) / (1280 - 320));
}



.pl {
  display: block;
  width: 2.75em;
  height: 2.75em;
}

.pl__ring,
.pl__ball {
  animation: ring 2s ease-out infinite;
}

.pl__ball {
  animation-name: ball;
}

/* Animation */
@keyframes ring {
  from {
    stroke-dasharray: 0 257 0 0 1 0 0 258;
  }

  25% {
    stroke-dasharray: 0 0 0 0 257 0 258 0;
  }

  50%,
  to {
    stroke-dasharray: 0 0 0 0 0 515 0 0;
  }
}

@keyframes ball {

  from,
  50% {
    animation-timing-function: ease-in;
    stroke-dashoffset: 1;
  }

  64% {
    animation-timing-function: ease-in;
    stroke-dashoffset: -109;
  }

  78% {
    animation-timing-function: ease-in;
    stroke-dashoffset: -145;
  }

  92% {
    animation-timing-function: ease-in;
    stroke-dashoffset: -157;
  }

  57%,
  71%,
  85%,
  99%,
  to {
    animation-timing-function: ease-out;
    stroke-dashoffset: -163;
  }
}


.loader-spinner {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

@media screen and (min-width: 900px) {
  .bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100dvw;
    height: 100dvh;
    background-color: black;
    opacity: 0.3;
    z-index: 1000;
  }
}