import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { BehaviorSubject, tap } from 'rxjs';
import { PortfolioBinanceAndrea } from '../data/binance-andrea.service';
import { PortfolioBinanceElisa } from '../data/binance-elisa.service';
import { PortfolioBinanceFra } from '../data/binance-fra.service';
import { PortfolioCoinbaseFra } from '../data/coinbase-fra.service';
import { PortfolioPAC } from '../data/pac.service';

@Injectable({
  providedIn: 'root',
})
export class TokeninsightService {
  private apiUrl = 'https://api.tokeninsight.com';
  private apiKey = 'e8623980a4e942868f92bb8a353052e5';
  public loading$ = new BehaviorSubject(null);
  public loadingNews$ = new BehaviorSubject(null);
  public cryptoNews = signal([]);
  public prevNews1 = [];
  public addedNews = signal([]);

  constructor(
    private http: HttpClient,
    private portfolio: PortfolioCoinbaseFra,
    private portfolio2: PortfolioBinanceFra,
    private portfolio3: PortfolioBinanceAndrea,
    private portfolio4: PortfolioPAC,
    private portfolio5: PortfolioBinanceElisa
  ) {}

  getCryptoRatings(account?: string) {
    if (!this.shouldFetchRating) return;

    const headers = new HttpHeaders().set('TI_API_KEY', this.apiKey);
    return this.http
      .get(`${this.apiUrl}/api/v1/rating/coins`, {
        headers,
      })
      .pipe(
        tap((data: any) => {
          let cryptoRatings = data.data.items;
          // console.log('DATA', data.data.items);

          // console.log('RAITING AGGIORNATI', data.data.items);
          // console.log('RAITING AGGIORNATI2', this.portfolio.coins);

          cryptoRatings = cryptoRatings.map((item) => {
            return {
              ecosystem_development: item.ecosystem_development,
              name: item.name,
              rating_level: item.rating_level,
              rating_score: item.rating_score,
              symbol: item.symbol,
              team_partners_investors: item.team_partners_investors,
              tid: item.tid,
              token_economics: item.token_economics,
              token_performance: item.token_performance,
              underlying_technology_security:
                item.underlying_technology_security,
              review_time: item.review_time,
              roadmap_progress: item.roadmap_progress,
              rating_page: item.rating_page,
              tags: item.tags,
              // related_news: item.related_news,
            };
          });

          if (account == 'singleAccount') {
            this.setCoinsRatingSinglePortfolio(cryptoRatings);
          } else {
            this.setCoinsRating(cryptoRatings);
          }

          localStorage.setItem('coinsRating', JSON.stringify(cryptoRatings));
          localStorage.setItem('dateRating', new Date().toString());

          // console.log('RATINGS', cryptoRatings);
        })
      );
  }

  setCoinsRating(data2) {
    // console.log('RATING', data2);
    // console.log('RATING2', this.portfolio4.coins);
    this.portfolio.coins.forEach((coin) => {
      coin.rating = {};

      if (data2.find((item: any) => item.symbol == coin.ticker)) {
        let tokenInsight = data2.find(
          (item: any) => item.symbol == coin.ticker
        );

        coin.rating.level = tokenInsight.rating_level;

        coin.rating.score = tokenInsight.rating_score;
        coin.rating.progress = tokenInsight.roadmap_progress;
        coin.rating.team = tokenInsight.team_partners_investors;
        coin.rating.economics = tokenInsight.token_economics;
        coin.rating.performance = tokenInsight.token_performance;
        coin.rating.security = tokenInsight.underlying_technology_security;
        coin.rating.URL = tokenInsight.rating_page;
        coin.rating.reviewTime = tokenInsight.review_time;
        coin.rating.tags = tokenInsight.tags;
        // coin.rating.news = tokenInsight.related_news;
      }
    });

    this.portfolio2.coins.forEach((coin) => {
      coin.rating = {};

      if (data2.find((item: any) => item.symbol == coin.ticker)) {
        let tokenInsight = data2.find(
          (item: any) => item.symbol == coin.ticker
        );

        coin.rating.level = tokenInsight.rating_level;
        coin.rating.score = tokenInsight.rating_score;
        coin.rating.progress = tokenInsight.roadmap_progress;
        coin.rating.team = tokenInsight.team_partners_investors;
        coin.rating.economics = tokenInsight.token_economics;
        coin.rating.performance = tokenInsight.token_performance;
        coin.rating.security = tokenInsight.underlying_technology_security;
        coin.rating.URL = tokenInsight.rating_page;
        coin.rating.reviewTime = tokenInsight.review_time;
        coin.rating.tags = tokenInsight.tags;
        coin.rating.news = tokenInsight.news;
      } else {
        coin.rating = { level: 'Other' };
      }
    });

    this.portfolio3.coins.forEach((coin) => {
      coin.rating = {};

      if (data2.find((item: any) => item.symbol == coin.ticker)) {
        let tokenInsight = data2.find(
          (item: any) => item.symbol == coin.ticker
        );

        coin.rating.level = tokenInsight.rating_level;
        coin.rating.score = tokenInsight.rating_score;
        coin.rating.progress = tokenInsight.roadmap_progress;
        coin.rating.team = tokenInsight.team_partners_investors;
        coin.rating.economics = tokenInsight.token_economics;
        coin.rating.performance = tokenInsight.token_performance;
        coin.rating.security = tokenInsight.underlying_technology_security;
        coin.rating.URL = tokenInsight.rating_page;
        coin.rating.reviewTime = tokenInsight.review_time;
        coin.rating.tags = tokenInsight.tags;
        coin.rating.news = tokenInsight.news;
      } else {
        coin.rating = { level: 'Other' };
      }
    });

    this.portfolio4.coins.forEach((coin) => {
      coin.rating = {};

      if (data2.find((item: any) => item.symbol == coin.ticker)) {
        let tokenInsight = data2.find(
          (item: any) => item.symbol == coin.ticker
        );

        coin.rating.level = tokenInsight.rating_level;
        coin.rating.score = tokenInsight.rating_score;
        coin.rating.progress = tokenInsight.roadmap_progress;
        coin.rating.team = tokenInsight.team_partners_investors;
        coin.rating.economics = tokenInsight.token_economics;
        coin.rating.performance = tokenInsight.token_performance;
        coin.rating.security = tokenInsight.underlying_technology_security;
        coin.rating.URL = tokenInsight.rating_page;
        coin.rating.reviewTime = tokenInsight.review_time;
        coin.rating.tags = tokenInsight.tags;
        coin.rating.news = tokenInsight.news;
      } else {
        coin.rating = { level: 'Other' };
      }
    });

    this.portfolio5.coins.forEach((coin) => {
      coin.rating = {};

      if (data2.find((item: any) => item.symbol == coin.ticker)) {
        let tokenInsight = data2.find(
          (item: any) => item.symbol == coin.ticker
        );

        coin.rating.level = tokenInsight.rating_level;
        coin.rating.score = tokenInsight.rating_score;
        coin.rating.progress = tokenInsight.roadmap_progress;
        coin.rating.team = tokenInsight.team_partners_investors;
        coin.rating.economics = tokenInsight.token_economics;
        coin.rating.performance = tokenInsight.token_performance;
        coin.rating.security = tokenInsight.underlying_technology_security;
        coin.rating.URL = tokenInsight.rating_page;
        coin.rating.reviewTime = tokenInsight.review_time;
        coin.rating.tags = tokenInsight.tags;
        coin.rating.news = tokenInsight.news;
      } else {
        coin.rating = { level: 'Other' };
      }
    });

    this.loading$.next('ok');
  }

  setCoinsRatingSinglePortfolio(data2) {
    this.portfolio5.coins.forEach((coin) => {
      coin.rating = {};

      if (data2.find((item: any) => item.symbol == coin.ticker)) {
        let tokenInsight = data2.find(
          (item: any) => item.symbol == coin.ticker
        );

        coin.rating.level = tokenInsight.rating_level;

        coin.rating.score = tokenInsight.rating_score;
        coin.rating.progress = tokenInsight.roadmap_progress;
        coin.rating.team = tokenInsight.team_partners_investors;
        coin.rating.economics = tokenInsight.token_economics;
        coin.rating.performance = tokenInsight.token_performance;
        coin.rating.security = tokenInsight.underlying_technology_security;
        coin.rating.URL = tokenInsight.rating_page;
        coin.rating.reviewTime = tokenInsight.review_time;
        coin.rating.tags = tokenInsight.tags;
        coin.rating.news = tokenInsight.news;
      }
    });

    this.loading$.next('ok');
  }

  getCryptoNews() {
    if (!this.shouldFetchNews) return;
    if (localStorage.getItem('cryptoNews')) {
      this.prevNews1 = JSON.parse(localStorage.getItem('cryptoNews'));
    }

    const headers = new HttpHeaders().set('TI_API_KEY', this.apiKey);
    return this.http
      .get('https://api.tokeninsight.com/api/v1/news/list', {
        headers,
      })
      .pipe(
        tap((data: any) => {
          localStorage.setItem('dateNews', new Date().toString());
          localStorage.setItem('cryptoNews', JSON.stringify(data.data.items));
          this.cryptoNews.set(data.data.items);
        })
      );
  }

  public shouldFetchRating() {
    const storedDate = localStorage.getItem('dateRating');
    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1440) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  public shouldFetchNews() {
    const storedDate = localStorage.getItem('dateNews');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 10) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
