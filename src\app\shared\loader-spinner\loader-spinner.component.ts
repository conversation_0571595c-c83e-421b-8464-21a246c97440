import { ChangeDetectionStrategy, Component, input } from '@angular/core';

@Component({
  selector: 'app-loader-spinner',
  templateUrl: './loader-spinner.component.html',
  styleUrls: ['./loader-spinner.component.less'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoaderSpinnerComponent {
  readonly loading = input<boolean>(true);
  readonly style = input<{ [key: string]: string }>();
  readonly showBg = input<boolean>(true);
}
