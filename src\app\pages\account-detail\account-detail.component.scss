// background-color: #43495c;

@mixin header-table-item {
  padding: 0.3rem 0.5rem;
  width: 100%;
  text-align: center;
}

@mixin table-item {
  padding: 0.4rem 1rem;
  border: 1px solid rgb(56, 56, 56);
  width: 100%;
}

hr {
  background-color: white;
  color: white;
  height: 2px;
  border-width: 0;
}

.home {
  width: 100%;
  padding: 1rem;
  margin-top: 0.5rem;

  & .home-desktop-1 {
    & .container {
      & .yearTitle {
        display: flex;
        width: 100%;
        justify-content: center;
        align-items: center;
        position: relative;
        font-size: 2rem;
        margin-top: 4rem;
        background: #1a1f61;
        border-radius: 10px;
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
        padding: 0.5rem;
        cursor: pointer;

        &.open {
          border: 1px solid #484848;
          border-bottom: none;
        }

        & i {
          position: absolute;
          right: 2rem;
          font-size: 1.6rem;
        }
      }

      & .table-header {
        display: grid;
        grid-template-columns: 20% 20% 20% 20% 20%;
        grid-template-rows: auto;
        grid-template-areas:
          "startTitle depositsTitle endTitle profitTitle profitPercTitle"
          "start deposits end profit profitPerc";
        margin: 0;
        font-size: 1.4rem;
        padding: 1rem 0;
        background-color: rgb(30, 30, 30);
        border-radius: 10px;
        cursor: pointer;
        border-top-right-radius: 0;
        border-top-left-radius: 0;

        &:last-child {
          margin-bottom: 3rem;
        }

        &.open {
          border-bottom-left-radius: 0;
          border-bottom-right-radius: 0;
          border: 1px solid #484848;
          border-bottom: none;
          border-top: none;
          padding: 1rem 0;
        }

        & .startTitle {
          grid-area: startTitle;
          @include header-table-item;
        }
        & .endTitle {
          grid-area: endTitle;
          @include header-table-item;
        }
        & .profitTitle {
          grid-area: profitTitle;
          @include header-table-item;
        }

        & .profitPercTitle {
          grid-area: profitPercTitle;
          @include header-table-item;
        }

        & .deposits {
          grid-area: deposits;
          @include header-table-item;
        }
        & .depositsTitle {
          grid-area: depositsTitle;
          @include header-table-item;
        }

        & .start {
          grid-area: start;
          @include header-table-item;
        }
        & .end {
          grid-area: end;
          @include header-table-item;
        }
        & .profit {
          grid-area: profit;
          @include header-table-item;
        }

        & .profitPerc {
          grid-area: profitPerc;
          @include header-table-item;
        }
      }

      .table {
        display: grid;
        grid-template-columns: 65% 35%;
        grid-template-rows: auto;
        grid-template-areas:
          "start  startValue "
          "deposits  depositsValue "
          "depositsStart  depositsStartValue "
          "fees  feesValue "
          "withdraw  withdrawValue "
          "openTrades   openTradesValue "
          "closedTrades  closedTradesValue "
          "final  finalValue "
          "profit  profitValue "
          "taxes  taxesValue "
          "netProfit  netProfitValue "
          "netProfitPerc  netProfitPercValue ";
        font-size: 1.6rem;

        // & .header {
        //   grid-area: header;
        //   font-weight: 500;
        //   text-align: center;
        //   padding: 1.5rem;
        //   width: 100%;
        //   font-size: 2rem;
        //   background-color: rgb(40, 40, 40);
        //   border: 1px solid rgb(56, 56, 56);
        // }

        & .start {
          grid-area: start;
          @include table-item;
        }

        & .start-value {
          grid-area: startValue;
          @include table-item;
          text-align: right;
        }

        & .deposits {
          grid-area: deposits;
          @include table-item;
        }

        & .deposits-value {
          grid-area: depositsValue;
          @include table-item;
          text-align: right;
        }

        & .deposits-start {
          grid-area: depositsStart;
          @include table-item;
        }

        & .deposits-start-value {
          grid-area: depositsStartValue;
          @include table-item;

          text-align: right;
        }

        & .fees {
          grid-area: fees;
          @include table-item;
        }

        & .fees-value {
          grid-area: feesValue;
          @include table-item;
          text-align: right;
          color: orange;
        }

        & .withdraw {
          grid-area: withdraw;
          @include table-item;
        }

        & .withdraw-value {
          grid-area: withdrawValue;
          @include table-item;
          text-align: right;
        }

        & .openTrades {
          grid-area: openTrades;
          @include table-item;
        }

        & .openTrades-value {
          grid-area: openTradesValue;
          @include table-item;
          text-align: right;
          color: red;
        }

        & .closedTrades {
          grid-area: closedTrades;
          @include table-item;
        }

        & .closedTrades-value {
          grid-area: closedTradesValue;
          @include table-item;
          text-align: right;
        }

        & .final {
          grid-area: final;
          @include table-item;
        }

        & .final-value {
          grid-area: finalValue;
          @include table-item;
          text-align: right;
        }

        & .profit {
          grid-area: profit;
          @include table-item;
        }

        & .profit-value {
          grid-area: profitValue;
          @include table-item;
          text-align: right;
          color: red;
        }

        & .taxes {
          grid-area: taxes;
          @include table-item;
        }

        & .taxes-value {
          grid-area: taxesValue;
          @include table-item;
          text-align: right;
        }

        & .netProfit {
          grid-area: netProfit;
          @include table-item;
        }

        & .netProfit-value {
          grid-area: netProfitValue;
          @include table-item;
          text-align: right;
          color: red;
        }

        & .netProfitPerc {
          grid-area: netProfitPerc;
          @include table-item;
          border-bottom-left-radius: 10px;
        }

        & .netProfitPerc-value {
          grid-area: netProfitPercValue;
          @include table-item;
          text-align: right;
          color: red;
          border-bottom-right-radius: 10px;
        }
      }
      & .taxesInfo {
        display: flex;
        flex-direction: column;
        width: 100%;
        justify-content: center;
        align-items: center;
        position: relative;
        font-size: 1.6rem;
        margin-top: 3rem;
        margin-bottom: 2rem;

        &-title {
          display: flex;
          justify-content: center;
          background: #4b4b4b;
          border-radius: 10px;
          padding: 1rem 0;
          width: 100%;
          font-size: 1.8rem;

          &.open {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
            cursor: pointer;
          }

          & i {
            position: absolute;
            right: 2rem;
            font-size: 1.6rem;
          }
        }

        &-text {
          display: flex;
          justify-content: start;
          align-items: center;
          flex-direction: column;
          font-size: 1.6rem;
          background: rgb(16, 16, 16);
          padding: 1.5rem 1rem;
          width: 100%;
          color: #d0d0d0;
          border-bottom-right-radius: 10px;
          border-bottom-left-radius: 10px;
          line-height: 23px;
          text-align: justify;
          hyphens: auto;

          & i {
            margin-right: 0.5rem;
            font-size: 1rem;
          }

          & .rw,
          & .rt,
          & .otherTaxes {
            display: flex;
            align-items: center;
            width: 100%;
            color: #fff;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 1.8rem;
          }
        }
      }
    }
  }
}

.select-after {
  &::after {
    content: "\25BC";
    color: #fff;
    position: absolute;
    top: 5px;
    right: -2px;
    font-size: 16px;
    padding: 0 1em;
    cursor: pointer;
    pointer-events: none;
  }
}

@media (min-width: 900px) {
  .home {
    padding-top: 0;
  }
  .home-desktop {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    // margin: 0% 2%;

    .home-desktop-1 {
      flex-direction: column;
      width: 100%;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    & .home {
      width: 100%;
    }
  }
}

@media (min-width: 1200px) {
  .home-desktop {
    // margin: 0% 12%;
  }
}
