import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';
import { AccountDepositsComponent } from './pages/account-deposits/account-deposits.component';
import { HomeComponent } from './pages/home/<USER>';
import { LoginComponent } from './pages/login/login.component';
import { NewsComponent } from './pages/news/news.component';

const routes: Routes = [
  {
    path: '',
    component: HomeComponent,
    canActivate: [authGuard],
  },
  {
    path: 'account-detail',
    loadChildren: () =>
      import('./pages/account-detail/account-detail.module').then(
        (m) => m.AccountDetailModule
      ),
  },
  {
    path: 'blockchain-stats',
    loadChildren: () =>
      import('./pages/blockchain-stats/blockchain-stats.module').then(
        (m) => m.BlockchainStatsModule
      ),
  },
  {
    path: 'crypto-volumes',
    loadChildren: () =>
      import('./pages/crypto-volumes/crypto-volumes.module').then(
        (m) => m.CryptoVolumesModule
      ),
  },
  {
    path: 'token-stats',
    loadChildren: () =>
      import('./pages/token-stats/token-stats.module').then(
        (m) => m.TokenStatsModule
      ),
  },
  {
    path: 'altcoin-season',
    loadChildren: () =>
      import('./pages/altcoin-season/altcoin-season.module').then(
        (m) => m.AltcoinSeasonModule
      ),
  },
  {
    path: 'login',
    component: LoginComponent,
  },
  // {
  //   path: 'user-panel',
  //   loadChildren: () =>
  //     import('./pages/user-panel/user-panel.module').then(
  //       (m) => m.UserPanelModule
  //     ),
  //   canActivate: [authGuard],
  // },
  {
    path: 'account-deposits',
    component: AccountDepositsComponent,
    canActivate: [authGuard],
  },
  {
    path: 'news',
    component: NewsComponent,
    canActivate: [authGuard],
  },
  {
    path: '**',
    redirectTo: '',
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      preloadingStrategy: PreloadAllModules,
      useHash: true,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
