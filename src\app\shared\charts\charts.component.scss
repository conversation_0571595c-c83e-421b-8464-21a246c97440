#chart {
  padding: 0;
  margin-top: -1rem;
}

.crypto-chart {
  padding: 3rem 0.5rem 0rem 0.5rem;

  &-title {
    grid-row: 1/2;
    grid-column: 1/-1;
    align-self: center;
    font-size: 1.8rem;
    font-weight: 500;
    color: #fff;
    margin-bottom: 0.5rem;
  }

  &-buttons {
    display: inline-flex;
    cursor: pointer;
    margin-bottom: 0.5rem;

    @media (max-width: 768px) {
      scrollbar-width: none;

      &::-webkit-scrollbar {
        display: none;
      }
    }

    &-balance,
    &-holdings,
    &-deposits,
    &-ath {
      color: #c4c4c4;
      background-color: #282828;
      padding: 0.7rem 1.2rem;
      border-radius: 5px;
      margin-right: 0.5rem;
      font-size: 1.4rem;

      &.selected {
        background-color: #4b4b4b;
        color: white;
      }
    }
  }
}

.monthly-table {
  border-collapse: collapse;
  margin: 25px 0;
  font-size: 1.4rem;
  width: 100%;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  border: 1px solid gray;
  border-radius: 15px;

  thead tr {
    background-color: #009879;
    color: #ffffff;
    text-align: left;

    .profit,
    .profitPerc,
    .current {
      text-align: right;
    }
  }

  th,
  td {
    padding: 12px 15px;
  }

  tbody tr {
    // border-bottom: 1px solid #dddddd;

    .profit,
    .profitPerc,
    .current {
      text-align: right;
    }
  }

  tbody tr:nth-of-type(even) {
    background-color: #1f1f1f;
  }

  tbody tr:last-of-type {
    // border-bottom: 2px solid #009879;
  }
}
