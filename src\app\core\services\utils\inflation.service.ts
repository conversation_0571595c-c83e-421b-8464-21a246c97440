import { Injectable } from '@angular/core';
import { IPortfolioMonthly } from '../../interfaces/coins';

@Injectable({
  providedIn: 'root',
})
export class InflationService {
  constructor() {}

  /**
   * Calcola l'inflazione effettiva cumulata sui depositi,
   * tenendo conto del momento in cui sono stati fatti e dei tassi d'inflazione trimestrali.
   *
   * @param months - Array di oggetti che rappresentano ogni mese, con un campo "deposits"
   * @returns Inflazione effettiva in percentuale (es. 8.57 significa +8.57% di inflazione)
   */
  calculateEffectiveInflation(months: IPortfolioMonthly[]): number {
    // Mappa con inflazione mensile italiana (formato: 'YYYY-MM')

    // Calcola automaticamente il cutoff dall'ultimo mese disponibile
    const inflationKeys = Array.from(this.italianMonthlyInflation.keys());
    const lastKey = inflationKeys[inflationKeys.length - 1];
    const [lastYear, lastMonth] = lastKey.split('-').map(Number);
    // Crea la data di cutoff come ultimo giorno del mese
    const cutoffDate = new Date(lastYear, lastMonth, 0);

    let originalSum = 0; // Somma dei depositi originali
    let adjustedSum = 0; // Somma dei depositi attualizzati all'inflazione

    // Itera su ciascun mese dell'array
    months.forEach((month) => {
      const deposit = month.deposits;

      // Salta i mesi senza depositi
      if (deposit <= 0) return;

      originalSum += deposit; // Aggiunge alla somma originale

      const depositDate = new Date(month.date);
      let adjusted = deposit;

      // Attualizza il deposito avanzando mese per mese
      for (
        let current = new Date(depositDate);
        current < cutoffDate;
        current.setMonth(current.getMonth() + 1)
      ) {
        // Formato chiave: 'YYYY-MM'
        const year = current.getFullYear();
        const month = (current.getMonth() + 1).toString().padStart(2, '0');
        const key = `${year}-${month}`;

        const rate = this.italianMonthlyInflation.get(key) ?? 0; // Tasso mensile

        adjusted *= 1 + rate; // Applica inflazione mensile
      }

      adjustedSum += adjusted; // Somma il valore attualizzato

      // console.log(
      //   `Mese: ${month.title}, Deposito originale: ${deposit}, Deposito attualizzato: ${adjusted.toFixed(
      //     2,
      //   )}, Inflazione totale: ${((adjusted / deposit - 1) * 100).toFixed(2)}%`,
      // );
    });

    // Evita divisione per zero se non ci sono depositi
    if (originalSum === 0) return 0;

    // Calcolo finale: inflazione effettiva in percentuale
    const inflation = (adjustedSum / originalSum - 1) * 100;
    return parseFloat(inflation.toFixed(2)); // es. 7.45 significa +7.45% inflazione cumulata
  }

  private calculateTotalInflation() {
    // Calcolo della somma cumulata (2021-2025)
    const totalInflation = Array.from(
      this.italianMonthlyInflation.values(),
    ).reduce((sum, rate) => sum + rate, 0);
    console.log(
      `Inflazione cumulata totale: ${(totalInflation * 100).toFixed(2)}%`,
    );
  }

  // https://it.tradingeconomics.com/italy/inflation-rate-mom
  private italianMonthlyInflation = new Map<string, number>([
    // 2021
    ['2021-08', 0.004], // +0.4%
    ['2021-09', -0.002], // -0.2%
    ['2021-10', 0.007], // +0.7%
    ['2021-11', 0.006], // +0.6%
    ['2021-12', 0.004], // +0.4%

    // 2022
    ['2022-01', 0.016], // +1.6%
    ['2022-02', 0.009], // +0.9%
    ['2022-03', 0.01], // +1.0%
    ['2022-04', -0.001], // -0.1%
    ['2022-05', 0.008], // +0.8%
    ['2022-06', 0.012], // +1.2%
    ['2022-07', 0.004], // +0.4%
    ['2022-08', 0.008], // +0.8%
    ['2022-09', 0.003], // +0.3%
    ['2022-10', 0.034], // +3.4%
    ['2022-11', 0.005], // +0.5%
    ['2022-12', 0.003], // +0.3%

    // 2023
    ['2023-01', 0.001], // +0.1%
    ['2023-02', 0.002], // +0.2%
    ['2023-03', -0.004], // -0.4%
    ['2023-04', 0.004], // +0.4%
    ['2023-05', 0.003], // +0.3%
    ['2023-06', 0.0], // +0.0%
    ['2023-07', 0.0], // +0.0%
    ['2023-08', 0.003], // +0.3%
    ['2023-09', 0.002], // +0.2%
    ['2023-10', -0.002], // -0.2%
    ['2023-11', -0.005], // -0.5%
    ['2023-12', 0.002], // +0.2%

    // 2024
    ['2024-01', 0.003], // +0.3%
    ['2024-02', 0.001], // +0.1%
    ['2024-03', 0.0], // +0.0%
    ['2024-04', 0.001], // +0.1%
    ['2024-05', 0.002], // +0.2%
    ['2024-06', 0.001], // +0.1%
    ['2024-07', 0.004], // +0.4%
    ['2024-08', 0.002], // +0.2%
    ['2024-09', -0.002], // -0.2%
    ['2024-10', 0.0], // +0.0%
    ['2024-11', -0.001], // -0.1%
    ['2024-12', 0.001], // +0.1%

    // 2025
    ['2025-01', 0.006], // +0.6%
    ['2025-02', 0.002], // +0.2%
    ['2025-03', 0.003], // +0.3%
    ['2025-04', 0.001], // +0.1%
    ['2025-05', -0.001], // -0.1%
    ['2025-06', 0.002], // +0.2%
    ['2025-07', 0.004], // +0.4%
    ['2025-08', 0.001], // +0.1%
  ]);
}
