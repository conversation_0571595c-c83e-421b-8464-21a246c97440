import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError, timer } from 'rxjs';
import { catchError, finalize, mergeMap } from 'rxjs/operators';

@Injectable()
export class RetryInterceptor implements HttpInterceptor {
  private readonly maxRetries = 3;
  private readonly delayMs = 2000;

  intercept(
    req: HttpRequest<any>,
    next: <PERSON>ttp<PERSON>and<PERSON>,
  ): Observable<HttpEvent<any>> {
    let retries = 0;

    const handleRequest = (): Observable<HttpEvent<any>> => {
      return next.handle(req).pipe(
        catchError((error: HttpErrorResponse) => {
          if (
            error.status >= 500 &&
            error.status < 600 &&
            retries < this.maxRetries
          ) {
            console.log(
              `Tentativo ${retries} fallito. Ritento tra ${this.delayMs}ms...`,
            );
            retries++;

            // Utilizza timer per ritardare il retry
            return timer(this.delayMs).pipe(
              mergeMap(() => handleRequest()), // Ritenta la richiesta ricorsivamente
            );
          }
          return throwError(() => new Error(error.message));
        }),
      );
    };

    return handleRequest().pipe(
      finalize(() => {
        if (retries > 0) {
          console.log(`Retry eseguito ${retries} volte`);
        }
      }),
    );
  }
}
