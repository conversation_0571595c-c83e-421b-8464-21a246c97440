import { Injectable } from '@angular/core';
import { Coin, ICoin, IPortfolioStats, ITrade } from '../../interfaces/coins';

@Injectable({
  providedIn: 'root',
})
export class PortfolioBinanceElisa extends Coin {
  constructor() {
    super();
  }

  // BINANCE
  public coins: ICoin[] = [
    {
      name: 'Euro',
      nameApi: 'euro-coin',
      ticker: 'EUR',
      logo: '../../../assets/img/logo/euro.png',
      category: 'Fiat',
      quantity: 3881,
      deposits: 1859,
      averagePrice: 1,
      logoColor: '#000000',
      ecosystem: ['Fiat'],
    },
    // {
    //   name: 'Bitcoin',
    //   nameApi: 'bitcoin',
    //   ticker: 'BTC',
    //   logo: '../../../assets/img/logo/btc.png',
    //   quantity: 0.00997,
    //   deposits: 410.49,
    //   averagePrice: 41117,
    //   logoColor: '#F7931A',
    //   category: 'Layer 1',
    // },
    // {
    //   name: 'Ethereum',
    //   nameApi: 'ethereum',
    //   ticker: 'ETH',
    //   logo: '../../../assets/img/logo/eth.png',
    //   quantity: 0.328,
    //   deposits: 564.72,
    //   averagePrice: 2510,
    //   logoColor: '#627eea',
    //   category: 'Layer 1',
    // },
    // {
    //   name: 'Cardano',
    //   nameApi: 'cardano',
    //   ticker: 'ADA',
    //   logo: '../../../assets/img/logo/ada.png',
    //   quantity: 23.97,
    //   deposits: 31,
    //   averagePrice: 1.32,
    //   logoColor: '#0033ad',
    //   category: 'Layer 1',
    // },
    // {
    //   name: 'Solana',
    //   nameApi: 'solana',
    //   ticker: 'SOL',
    //   logo: '../../../assets/img/logo/sol.png',
    //   quantity: 7.599,
    //   deposits: 566.78,
    //   averagePrice: 200,
    //   logoColor: '#0a0b0d',
    //   ecosystem: ['Solana'],
    //   category: 'Layer 1',
    // },
    // {
    //   name: 'Avalanche',
    //   nameApi: 'avalanche-2',
    //   ticker: 'AVAX',
    //   logo: '../../../assets/img/logo/avax.png',
    //   quantity: 0.569,
    //   deposits: 47,
    //   averagePrice: 81.67,
    //   logoColor: '#e84142',
    //   category: 'Layer 1',
    // },
    // {
    //   name: 'Arbitrum',
    //   nameApi: 'arbitrum',
    //   ticker: 'ARB',
    //   logo: '../../../assets/img/logo/arb.png',
    //   category: 'Layer 2',
    //   quantity: 115.26,
    //   deposits: 189,
    //   averagePrice: 1.64,
    //   logoColor: '#2D6E8D',
    // },
    // {
    //   name: 'Fetch.ai',
    //   nameApi: 'fetch-ai',
    //   ticker: 'FET',
    //   logo: '../../../assets/img/logo/fet.png',
    //   quantity: 69.89,
    //   deposits: 50,
    //   averagePrice: 0.73,
    //   logoColor: '#1E2943',
    //   category: 'AI',
    // },
    // {
    //   name: 'BNB',
    //   nameApi: 'binancecoin',
    //   ticker: 'BNB',
    //   logo: '../../../assets/img/logo/bnb.webp',
    //   category: 'Layer 1',
    //   quantity: 0.0018,
    //   deposits: 0,
    //   averagePrice: 294.1,
    //   logoColor: '#F0B90B',
    // },
  ];

  public portfolioStats: IPortfolioStats = {
    gifts: 0,
    totalFees: -49,
    taxes: 2,
    realizedProfit: 0,
  };

  public closedTrades: ITrade[] = [];
}
