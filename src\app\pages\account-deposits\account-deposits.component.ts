import { Component, effect, signal } from '@angular/core';
import { concatMap, tap } from 'rxjs';
import { CurrentAccountService } from 'src/app/core/services/data/current-account.service';
import { FirebaseService } from 'src/app/core/services/http/dbFirebase.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-account-deposits',
  templateUrl: './account-deposits.component.html',
  styleUrl: './account-deposits.component.scss',
})
export class AccountDepositsComponent {
  protected currentAccount: string;
  protected yearsList = [];
  protected filteredList = [];
  protected filteredListStart = [];
  protected filteredListItem = [];
  protected filteredListItemFull = [];
  protected totalDeposits: number;
  protected crypto: any;
  protected avgPrice: any;
  protected depositsList = [];
  protected loading = signal<boolean>(true);
  protected selectCoin = 'all';
  protected isProduction = environment.production;

  constructor(
    private firebaseService: FirebaseService,
    private currentAccountService: CurrentAccountService,
  ) {
    effect(
      () => {
        if (this.currentAccountService.currentAccount()) {
          this.loading.set(true);
          this.currentAccount = this.currentAccountService.currentAccount();

          if (this.currentAccountService.currentAccount() === 'pac') {
            this.firebaseService
              .getAccountDeposits('binance-andrea')
              .pipe(
                tap((data: any) => {
                  // console.log('DATA', data);

                  if (data?.deposits) {
                    this.depositsList = this.groupedData(
                      data.deposits.reverse(),
                    ).sort((a, b) => {
                      let dateA = new Date(a.date);
                      let dateB = new Date(b.date);

                      return +dateB - +dateA;
                    });

                    this.yearsList = Array.from(
                      new Set(
                        this.depositsList.map((item) =>
                          new Date(item.date).getFullYear(),
                        ),
                      ),
                    );

                    this.depositsList.sort((a, b) => {
                      let dateA = new Date(a.date);
                      let dateB = new Date(b.date);

                      return +dateB - +dateA;
                    });

                    this.filteredListStart = this.depositsList;
                    this.filteredList = [...this.filteredListStart];
                  }
                }),
                concatMap(() => {
                  return this.firebaseService
                    .getAccountDeposits('coinbase-fra')
                    .pipe(
                      tap((data: any) => {
                        // console.log('DATA', data);

                        // console.log('QUI1', this.depositsList, this.filteredList);

                        if (data?.deposits) {
                          this.depositsList.push(
                            ...this.groupedData(data.deposits.reverse()),
                          );
                          this.depositsList = this.depositsList.sort((a, b) => {
                            let dateA = new Date(a.date);
                            let dateB = new Date(b.date);

                            return +dateB - +dateA;
                          });

                          this.yearsList = Array.from(
                            new Set(
                              this.depositsList.map((item) =>
                                new Date(item.date).getFullYear(),
                              ),
                            ),
                          ).sort((a, b) => b - a);

                          this.yearsList.unshift('All Dates');

                          this.filteredListStart = this.depositsList;
                          this.filteredList = [...this.filteredListStart];

                          let arrayList = [];
                          this.filteredListStart.forEach(function (item) {
                            // Estrai gli oggetti dalla chiave "value" e uniscili a myarray2
                            arrayList = arrayList.concat(item.value);
                          });

                          this.filteredListItemFull = [...arrayList];

                          this.filteredListItem = this.getUniqueValues(
                            this.filteredList,
                          );
                          this.totalDeposits = arrayList.reduce(
                            (acc, curr) => acc + curr.totalDeposit,
                            0,
                          );
                          // console.log('FINALE', this.filteredList);
                          // console.log('LIST', this.filteredListItem);
                          this.loading.set(false);
                        }
                      }),
                    );
                }),
              )
              .subscribe();
          } else {
            this.firebaseService
              .getAccountDeposits(this.currentAccountService.currentAccount())
              .subscribe((data: any) => {
                // console.log('DATA', data);
                this.loading.set(true);
                if (data?.deposits) {
                  this.depositsList = this.groupedData(
                    data.deposits.reverse(),
                  ).sort((a, b) => {
                    let dateA = new Date(a.date);
                    let dateB = new Date(b.date);

                    return +dateB - +dateA;
                  });

                  this.yearsList = Array.from(
                    new Set(
                      this.depositsList.map((item) =>
                        new Date(item.date).getFullYear(),
                      ),
                    ),
                  ).sort((a, b) => b - a);

                  this.yearsList.unshift('All Dates');

                  this.depositsList.sort((a, b) => {
                    let dateA = new Date(a.date);
                    let dateB = new Date(b.date);

                    return +dateB - +dateA;
                  });

                  this.filteredListStart = this.depositsList;
                  this.filteredList = [...this.filteredListStart];

                  let arrayList = [];
                  this.filteredListStart.forEach(function (item) {
                    // Estrai gli oggetti dalla chiave "value" e uniscili a myarray2
                    arrayList = arrayList.concat(item.value);
                  });

                  this.filteredListStart.sort((a, b) => {
                    let dateA = new Date(a.date);
                    let dateB = new Date(b.date);

                    return +dateB - +dateA;
                  });
                  this.filteredListItemFull = [...arrayList];

                  this.filteredListItem = this.getUniqueValues(
                    this.filteredList,
                  );

                  this.totalDeposits = +arrayList.reduce(
                    (acc, curr) => acc + curr.totalDeposit,
                    0,
                  );

                  this.loading.set(false);

                  // console.log(this.yearsList);

                  // console.log('group', this.groupedData(data.deposits.reverse()));
                } else {
                  this.depositsList = [];
                  this.filteredListStart = [];
                  this.filteredList = [...this.filteredListStart];
                  this.filteredListItem = [];
                  this.totalDeposits = 0;
                  this.loading.set(false);
                }
              });
          }
        }
      },
      { allowSignalWrites: true },
    );
  }

  findCoinImg(ticker) {
    if (ticker === 'busd' || ticker === 'BUSD') {
      return '../../../assets/img/logo/busd.png';
    } else if (ticker === 'crv' || ticker === 'CRV') {
      return '../../../assets/img/logo/crv.webp';
    } else if (ticker === 'bake' || ticker === 'BAKE') {
      return '../../../assets/img/logo/bake.webp';
    } else if (ticker === 'usdt' || ticker === 'USDT') {
      return '../../../assets/img/logo/usdt.png';
    } else if (ticker === 'bb' || ticker === 'BB') {
      return '../../../assets/img/logo/bb.png';
    } else if (ticker === 'perp' || ticker === 'PERP') {
      return '../../../assets/img/logo/perp.png';
    } else if (ticker === 'gala' || ticker === 'GALA') {
      return '../../../assets/img/logo/gala.png';
    } else if (ticker === 'coti' || ticker === 'COTI') {
      return '../../../assets/img/logo/coti.webp';
    } else if (ticker === 'imx' || ticker === 'IMX') {
      return '../../../assets/img/logo/immutable.webp';
    } else if (ticker === 'pepe' || ticker === 'PEPE') {
      return '../../../assets/img/logo/pepe.png';
    } else if (ticker === 'ape' || ticker === 'APE') {
      return '../../../assets/img/logo/ape-coin.png';
    } else if (ticker === 'trump' || ticker === 'TRUMP') {
      return '../../../assets/img/logo/trump.webp';
    } else
      return this.currentAccountService
        .currentPortfolio()
        .coins.find((coin) => coin.ticker === ticker.toUpperCase())?.logo;
  }

  groupedData(data: any) {
    let dataGroup = data.reduce((acc, currentItem) => {
      const key = currentItem.date;

      // Verifica se c'è già un gruppo per la data corrente
      if (!acc[key]) {
        acc[key] = [];
      }

      // Aggiungi l'elemento al gruppo corrente
      acc[key].push(currentItem);

      return acc;
    }, {});

    const groupedArray = Object.keys(dataGroup).map((key) => ({
      date: key,
      value: dataGroup[key],
    }));

    return groupedArray;
  }

  yearFilter(year: number) {
    const filteredArray = this.depositsList.filter(
      (item) => new Date(item.date).getFullYear() === year,
    );

    this.filteredListStart = filteredArray;
    this.filteredList = [...this.filteredListStart];

    let arrayList = [];
    this.filteredListStart.forEach(function (item) {
      // Estrai gli oggetti dalla chiave "value" e uniscili a myarray2
      arrayList = arrayList.concat(item.value);
    });

    this.filteredListItemFull = [...arrayList];

    this.filteredListItem = this.getUniqueValues(this.filteredList);

    this.totalDeposits = +arrayList.reduce(
      (acc, curr) => acc + curr.totalDeposit,
      0,
    );
  }

  coinFilter(coinFilter: string) {
    if (coinFilter == 'all') {
      this.filteredList = [...this.filteredListStart];
    } else {
      this.filteredList = [
        ...this.filteredListStart.filter((item) =>
          item.value.some((coin) => coin.crypto === coinFilter),
        ),
      ].map((filtered) => {
        return {
          ...filtered,
          value: filtered.value.filter(
            (singleCrypto) => singleCrypto.crypto === coinFilter,
          ),
        };
      });
    }
  }

  onSelectYear(data: any) {
    const year = data.target.value;

    if (year == 'All Dates') {
      this.filteredList = this.depositsList;
      let arrayList = [];
      this.filteredList.forEach(function (item) {
        // Estrai gli oggetti dalla chiave "value" e uniscili a myarray2
        arrayList = arrayList.concat(item.value);
      });

      this.filteredListItemFull = [...arrayList];

      this.filteredListItem = this.getUniqueValues(this.filteredList);

      this.totalDeposits = +arrayList.reduce(
        (acc, curr) => acc + curr.totalDeposit,
        0,
      );
    } else {
      this.yearFilter(+year);
    }
  }

  getUniqueValues(data: { date: []; value: { crypto: string }[] }[]) {
    let cryptoList = [];
    data.forEach((item) => {
      item.value.forEach((coin) => cryptoList.push(coin.crypto));
    });
    cryptoList = Array.from(new Set([...cryptoList]));
    cryptoList = cryptoList.sort((a, b) => a.localeCompare(b));
    return cryptoList;
  }
}
