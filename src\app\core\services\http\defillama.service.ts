import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DefillamaService {
  public chainsTvl = signal([]);
  public chainsFees = signal([]);
  public chainsTvlHistory = signal([]);

  public chainsImg = [
    {
      ticker: 'ETH',
      name: 'Ethereum',
      img: '../../../assets/img/logo/eth.png',
    },
    { ticker: 'BTC', name: 'Bitcoin', img: '../../../assets/img/logo/btc.png' },
    {
      ticker: 'AVAX',
      name: 'Avalanche',
      img: '../../../assets/img/logo/avax.png',
    },
    { ticker: 'TRON', name: 'Tron', img: '../../../assets/img/logo/tron.webp' },
    {
      ticker: 'BN<PERSON>',
      name: '<PERSON><PERSON>',
      img: '../../../assets/img/logo/bnb.webp',
    },
    {
      ticker: 'ARB',
      name: 'Arbitrum',
      img: '../../../assets/img/logo/arb.png',
    },
    { ticker: 'SOL', name: 'Solana', img: '../../../assets/img/logo/sol.png' },
    {
      ticker: 'MATIC',
      name: 'Polygon',
      img: '../../../assets/img/logo/matic.png',
    },
    { ticker: 'OP', name: 'Optimism', img: '../../../assets/img/logo/op.webp' },
    { ticker: 'SUI', name: 'Sui', img: '../../../assets/img/logo/sui.webp' },
    {
      ticker: 'MANTA',
      name: 'Manta',
      img: '../../../assets/img/logo/manta.webp',
    },
    { ticker: 'PLS', name: 'Pulsar', img: '../../../assets/img/logo/pls.webp' },
    { ticker: 'ADA', name: 'Cardano', img: '../../../assets/img/logo/ada.png' },
    { ticker: 'CRO', name: 'Cronos', img: '../../../assets/img/logo/cro.png' },
    { ticker: 'KAVA', name: 'Kava', img: '../../../assets/img/logo/kava.png' },
    { ticker: 'GNO', name: 'Gnosis', img: '../../../assets/img/logo/gno.webp' },
    {
      ticker: 'OSMO',
      name: 'Osmosis',
      img: '../../../assets/img/logo/osmo.webp',
    },
    { ticker: 'RON', name: 'Ronin', img: '../../../assets/img/logo/ron.png' },
    {
      ticker: 'EGLD',
      name: 'Elrond',
      img: '../../../assets/img/logo/egld.png',
    },
    { ticker: 'RUNE', name: 'Rune', img: '../../../assets/img/logo/rune.webp' },
    {
      ticker: 'APT',
      name: 'Aptos',
      img: '../../../assets/img/logo/aptos.png',
    },
    {
      ticker: 'MANTLE',
      name: 'Mantle',
      img: '../../../assets/img/logo/mantle.png',
    },
    {
      ticker: 'NEAR',
      name: 'Near',
      img: '../../../assets/img/logo/near.png',
    },
    {
      ticker: 'DOGE',
      name: 'Doge',
      img: '../../../assets/img/logo/dogecoin.webp',
    },
    {
      ticker: 'FTM',
      name: 'Fantom',
      img: '../../../assets/img/logo/fantom.webp',
    },
    {
      ticker: 'ALGO',
      name: 'Algorand',
      img: '../../../assets/img/logo/algorand.png',
    },
    {
      ticker: 'MNT',
      name: 'Mantle',
      img: '../../../assets/img/logo/mnt.png',
    },
    {
      ticker: 'TON',
      name: 'Ton',
      img: '../../../assets/img/logo/ton.webp',
    },
    {
      ticker: 'MODE',
      name: 'Mode',
      img: '../../../assets/img/logo/mode.png',
    },
    {
      ticker: 'MODE',
      name: 'Mode',
      img: '../../../assets/img/logo/mode.png',
    },
    {
      ticker: 'ZK',
      name: 'zkSync Era',
      img: '../../../assets/img/logo/zk.png',
    },
    {
      ticker: 'ZKL',
      name: 'zkLink Nova',
      img: '../../../assets/img/logo/zk.png',
    },
  ];
  constructor(private http: HttpClient) {
    if (!this.shouldFetchData()) {
      this.chainsTvl.set(JSON.parse(localStorage.getItem('chainsTvl')));
      this.chainsFees.set(JSON.parse(localStorage.getItem('chainsFees')));
      this.chainsTvlHistory.set(
        JSON.parse(localStorage.getItem('chainsTvlHistory')),
      );
    }
  }

  getChainsTvl() {
    return this.http.get('https://api.llama.fi/v2/chains').pipe(
      tap((data: any) => {
        // console.log('Chain', data);

        let chains = data;
        chains = chains.sort((a, b) => b.tvl - a.tvl);
        chains.forEach((chain) => {
          chain.img = this.chainsImg.find(
            (img) => img.ticker == chain.tokenSymbol,
          )?.img;
        });

        // console.log('CHAIN', chains);
        this.chainsTvl.set(chains);
        localStorage.setItem('chainsTvl', JSON.stringify(this.chainsTvl()));
        localStorage.setItem('dateDefillama', new Date().toString());
        // console.log('CHAINSTVL', this.chainsTvl());
      }),
    );
  }

  getProtocolsTvl() {
    return this.http.get('https://api.llama.fi/protocols').pipe(
      tap((data: any) => {
        let protocols = data;

        this.chainsTvl().forEach((chain) => {
          chain.protocols = protocols.filter((item) =>
            item.chains.includes(chain.name),
          ).length;

          // chain.img = this.chainsImg.find((img) => img.name == chain.name)?.img;
        });

        // this.chainsTvl().forEach((chain) => {
        //   chain.img = this.chainsImg.find((img) => img.name == chain.name)?.img;
        //   chain.tokenSymbol = this.chainsTvl().find(
        //     (item) => item.name == chain.name
        //   )?.tokenSymbol;
        // });

        localStorage.setItem('chainsTvl', JSON.stringify(this.chainsTvl()));
        localStorage.setItem('dateDefillama', new Date().toString());

        // console.log(
        //   protocols
        //     .slice()
        //     .filter(
        //       (item) =>
        //         item.category !== 'Chain' &&
        //         item.symbol !== '-' &&
        //         item.chains.length > 0
        //     )
        //     .sort((a, b) => b.mcap - a.mcap)
        // );

        // console.log('PROTOCOLS', this.chainsTvl());
      }),
    );
  }

  getChainsFees() {
    return this.http.get('https://api.llama.fi/overview/fees').pipe(
      tap((data: any) => {
        console.log('Fees', data);

        let fees = data.protocols;
        fees = fees.sort((a, b) => b.total1y - a.total1y);

        fees = fees.filter((item) => item.category == 'Chain');

        console.log('FEES CHAIN', fees);

        fees.forEach((chain) => {
          chain.img = this.chainsImg.find((img) => img.name == chain.name)?.img;
          chain.tokenSymbol = this.chainsTvl().find(
            (item) => item.name == chain.name,
          )?.tokenSymbol;
        });

        this.chainsFees.set(fees);

        localStorage.setItem('chainsFees', JSON.stringify(this.chainsFees()));
        localStorage.setItem('chainsTvl', JSON.stringify(this.chainsTvl()));
        localStorage.setItem('dateDefillama', new Date().toString());
      }),
    );
  }

  getChainsTvlHistory() {
    return this.http.get('https://api.llama.fi/v2/historicalChainTvl').pipe(
      tap((data: any) => {
        // console.log('TVL History', data);
        let tvlHistory = data;
        this.chainsTvlHistory.set(tvlHistory);
        localStorage.setItem(
          'chainsTvlHistory',
          JSON.stringify(this.chainsTvlHistory()),
        );
      }),
    );
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('dateDefillama');

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 1440) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
