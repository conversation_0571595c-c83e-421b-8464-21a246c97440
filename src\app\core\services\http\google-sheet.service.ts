import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { concatMap, map, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class GoogleSheetService {
  public googleAllData = signal(null);
  public googleStats = signal(null);
  public etfBtcHistory = signal<undefined | any[]>(undefined);
  public etfEthHistory = signal<undefined | any[]>(undefined);
  public fedProbabilty = signal<
    | undefined
    | {
        date: string;
        rate1: {
          probabilityCurrent: string;
          probabilty1Day: string;
          probabilty1Week: string;
          rateTarget: string;
        };
        rate2: {
          probabilityCurrent: string;
          probabilty1Day: string;
          probabilty1Week: string;
          rateTarget: string;
        };
      }
  >(undefined);

  protected cryptoImg = [
    {
      ticker: 'ETH',
      name: 'Ethereum',
      img: '../../../assets/img/logo/eth.png',
    },
    { ticker: '<PERSON><PERSON>', name: 'Bitcoin', img: '../../../assets/img/logo/btc.png' },
    {
      ticker: 'AVAX',
      name: 'Avalanche',
      img: '../../../assets/img/logo/avax.png',
    },
    { ticker: 'TRON', name: 'Tron', img: '../../../assets/img/logo/tron.webp' },
    {
      ticker: 'BNB',
      name: 'BSC',
      img: '../../../assets/img/logo/bnb.webp',
    },
    {
      ticker: 'ARB',
      name: 'Arbitrum',
      img: '../../../assets/img/logo/arb.png',
    },
    { ticker: 'SOL', name: 'Solana', img: '../../../assets/img/logo/sol.png' },
    {
      ticker: 'MATIC',
      name: 'Polygon',
      img: '../../../assets/img/logo/matic.png',
    },
    { ticker: 'OP', name: 'Optimism', img: '../../../assets/img/logo/op.webp' },
    { ticker: 'SUI', name: 'Sui', img: '../../../assets/img/logo/sui.webp' },
    {
      ticker: 'MANTA',
      name: 'Manta',
      img: '../../../assets/img/logo/manta.webp',
    },
    { ticker: 'PLS', name: 'Pulsar', img: '../../../assets/img/logo/pls.webp' },
    { ticker: 'ADA', name: 'Cardano', img: '../../../assets/img/logo/ada.png' },
    { ticker: 'CRO', name: 'Cronos', img: '../../../assets/img/logo/cro.png' },
    { ticker: 'KAVA', name: 'Kava', img: '../../../assets/img/logo/kava.png' },
    { ticker: 'GNO', name: 'Gnosis', img: '../../../assets/img/logo/gno.webp' },
    {
      ticker: 'OSMO',
      name: 'Osmosis',
      img: '../../../assets/img/logo/osmo.webp',
    },
    { ticker: 'RON', name: 'Ronin', img: '../../../assets/img/logo/ron.png' },
    {
      ticker: 'EGLD',
      name: 'Elrond',
      img: '../../../assets/img/logo/egld.png',
    },
    { ticker: 'RUNE', name: 'Rune', img: '../../../assets/img/logo/rune.webp' },
    {
      ticker: 'APT',
      name: 'Aptos',
      img: '../../../assets/img/logo/aptos.png',
    },
    {
      ticker: 'MANTLE',
      name: 'Mantle',
      img: '../../../assets/img/logo/mantle.png',
    },
    {
      ticker: 'NEAR',
      name: 'Near',
      img: '../../../assets/img/logo/near.png',
    },
    {
      ticker: 'DOGE',
      name: 'Doge',
      img: '../../../assets/img/logo/dogecoin.webp',
    },
    {
      ticker: 'FTM',
      name: 'Fantom',
      img: '../../../assets/img/logo/fantom.webp',
    },
    {
      ticker: 'ALGO',
      name: 'Algorand',
      img: '../../../assets/img/logo/algorand.png',
    },
    {
      ticker: 'XRP',
      name: 'Ripple',
      img: '../../../assets/img/logo/xrp.png',
    },
    {
      ticker: 'LINK',
      name: 'Chainlink',
      img: '../../../assets/img/logo/chainlink.png',
    },
    {
      ticker: 'TIA',
      name: 'Celestia',
      img: '../../../assets/img/logo/tia.png',
    },
    {
      ticker: 'DOT',
      name: 'Polkadot',
      img: '../../../assets/img/logo/dot.png',
    },
    {
      ticker: 'ALT',
      name: 'Altlayer',
      img: '../../../assets/img/logo/alt.png',
    },
    {
      ticker: 'ATOM',
      name: 'Cosmos',
      img: '../../../assets/img/logo/atom.png',
    },
    {
      ticker: 'STX',
      name: 'Stacks',
      img: '../../../assets/img/logo/stx.png',
    },
    {
      ticker: 'JUP',
      name: 'Jupiter',
      img: '../../../assets/img/logo/jup.png',
    },
    {
      ticker: 'GRT',
      name: 'The Graph',
      img: '../../../assets/img/logo/graph.webp',
    },
    {
      ticker: 'BONK',
      name: 'Bonk',
      img: '../../../assets/img/logo/bonk.png',
    },
  ];

  constructor(private http: HttpClient) {}

  fetchAllData() {
    return this.http
      .get(
        'https://sheets.googleapis.com/v4/spreadsheets/1QO-h_7S4vMQEy3LxTvGFt7LaGfCD0NL_-jWsAJ1RF68/values/AllData?alt=json&key=AIzaSyAhXXS5ctqKfELybuqs88pkDBLOWN8FGfs',
      )
      .pipe(
        tap((data: any) => {
          console.log('DATA GOOGLE', data.values);

          this.googleAllData.set(data.values.slice(1));

          this.googleAllData().forEach((coin) => {
            if (
              this.cryptoImg.filter((coinImg) => coinImg.ticker == coin[2])
                .length > 0
            ) {
              coin[12] = this.cryptoImg.filter(
                (coinImg) => coinImg.ticker == coin[2],
              )[0]?.img;
            }
            coin[5] = coin[5].replace(',', '.');
            coin[6] = coin[6].replace(',', '.');
            coin[7] = coin[7].replace(',', '.');
          });

          console.log('DATA GOOGLE', this.googleAllData());

          // console.log(this.cryptoList);
          localStorage.setItem(
            'googleAllData',
            JSON.stringify(this.googleAllData()),
          );
        }),
        concatMap(() => {
          return this.http
            .get(
              'https://sheets.googleapis.com/v4/spreadsheets/1QO-h_7S4vMQEy3LxTvGFt7LaGfCD0NL_-jWsAJ1RF68/values/Stats?alt=json&key=AIzaSyAhXXS5ctqKfELybuqs88pkDBLOWN8FGfs',
            )
            .pipe(
              tap((data: any) => {
                console.log('DATA GOOGLE STATS', data.values);

                localStorage.setItem('dateGoogle', new Date().toString());

                this.googleStats.set(data.values);
                localStorage.setItem(
                  'googleStats',
                  JSON.stringify(this.googleStats()),
                );
              }),
            );
        }),
      );
  }

  fetchEtfBtcEthHistoryData() {
    return this.http
      .get(
        'https://sheets.googleapis.com/v4/spreadsheets/1QO-h_7S4vMQEy3LxTvGFt7LaGfCD0NL_-jWsAJ1RF68/values/EtfBtc?alt=json&key=AIzaSyAhXXS5ctqKfELybuqs88pkDBLOWN8FGfs',
      )
      .pipe(
        map(
          (data: { majorDimensios: string; range: string; values: any[] }) => {
            const btcHistory = [];
            const ethHistory = [];

            console.log('VALUES', data.values);

            data.values = data.values.slice(1);

            data.values.forEach((item) => {
              btcHistory.push({
                date: item[0].split('/').reverse().join('/'),
                daily$: +item[1],
                dailyBtc: +item[2],
                total$: +item[3],
                totalBtc: +item[4],
              });

              if (!!item[6]) {
                ethHistory.push({
                  date: item[6].split('/').reverse().join('/'),
                  daily$: +item[7],
                  dailyEth: +item[8],
                  total$: +item[9],
                  totalEth: +item[10],
                });
              }
            });

            this.etfBtcHistory.set(btcHistory);
            this.etfEthHistory.set(ethHistory);

            console.log('ETF ETH', this.etfEthHistory());

            return { btcHistory: btcHistory, ethHistory: ethHistory };
          },
        ),
        tap((res) => {
          localStorage.setItem('etfHistoryLastUpdated', new Date().toString());

          localStorage.setItem(
            'etfBtcHistoryGoogle',
            JSON.stringify(this.etfBtcHistory()),
          );

          localStorage.setItem(
            'etfEthHistoryGoogle',
            JSON.stringify(this.etfEthHistory()),
          );

          localStorage.setItem('fedEtfLastUpdated', new Date().toString());
        }),
      );

    // return this.http
    //   .get(
    //     'https://docs.google.com/spreadsheets/d/e/2PACX-1vS4kJFoka7iw011bpZUgDrL5OgJE89n1m8NLuOaM_E-GV3StRTbUykJymoduk_FQHZ0c9AT4jPaimFu/pubhtml?gid=601850886&single=true',
    //     { responseType: 'text' },
    //   )
    //   .pipe(
    //     map((data: string) => {
    //       let startUrl = data.slice(data.search('<img src="') + 9, -1);
    //       let imageUrl = startUrl.slice(0, startUrl.search(' '));
    //       // console.log('DATA GOOGLE - ETF BTC', finalData);
    //       return imageUrl;
    //     }),
    //   );
  }

  fetchFedProbabilty() {
    return this.http
      .get(
        'https://sheets.googleapis.com/v4/spreadsheets/1QO-h_7S4vMQEy3LxTvGFt7LaGfCD0NL_-jWsAJ1RF68/values/FED?alt=json&key=AIzaSyAhXXS5ctqKfELybuqs88pkDBLOWN8FGfs',
      )
      .pipe(
        tap((data: any) => {
          console.log('FED', data);

          const fixedProbability: string[] = data.values[1][1]
            .split(' ')
            .slice(1, -1);

          fixedProbability.forEach((item, index) => {
            if (
              index === 3 ||
              index === 4 ||
              index === 5 ||
              index === 10 ||
              index === 11 ||
              index === 12
            ) {
              fixedProbability[index] =
                item.slice(0, item.length - 3) + item.slice(-1);
            }
          });

          console.log('fixedprobabilty', fixedProbability);

          const fedProbabilty = {
            date: data.values[0][0].trim(),
            rate1: {
              rateTarget:
                fixedProbability[0] +
                ' ' +
                fixedProbability[1] +
                ' ' +
                fixedProbability[2],
              probabilityCurrent: fixedProbability[3],
              probabilty1Day: fixedProbability[4],
              probabilty1Week: fixedProbability[5],
            },
            rate2: {
              rateTarget:
                fixedProbability[7] +
                ' ' +
                fixedProbability[8] +
                ' ' +
                fixedProbability[9],
              probabilityCurrent: fixedProbability[10],
              probabilty1Day: fixedProbability[11],
              probabilty1Week: fixedProbability[12],
            },
          };

          this.fedProbabilty.set(fedProbabilty);

          console.log('fedProbability', fedProbabilty);

          localStorage.setItem('fedEtfLastUpdated', new Date().toString());

          localStorage.setItem(
            'fedProbability',
            JSON.stringify(this.fedProbabilty()),
          );
        }),
      );
  }

  public shouldFetchData() {
    const storedDate = localStorage.getItem('dateGoogle')
      ? localStorage.getItem('dateGoogle')
      : null;

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 30) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  public shouldFetchEtfFedData() {
    const storedDate = localStorage.getItem('fedEtfLastUpdated')
      ? localStorage.getItem('fedEtfLastUpdated')
      : null;

    if (storedDate) {
      const date = new Date(storedDate);

      // Get timestamp in milliseconds
      const dateMs = date.getTime();
      const currentMs = new Date().getTime();

      // Difference in milliseconds
      const diffMs = currentMs - dateMs;

      // Convert to minutes
      const diffMins = diffMs / 1000 / 60;

      if (diffMins > 15) {
        return true;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
