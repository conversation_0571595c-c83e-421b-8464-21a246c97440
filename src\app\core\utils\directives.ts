import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appRatingBackground]',
})
export class RatingBackgroundDirective implements OnInit {
  @Input() rating!: string | undefined;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngOnInit() {
    this.setRatingBackground();
  }

  setRatingBackground() {
    let backgroundColor = '';

    switch (this.rating) {
      case 'AAA':
      case 'AA':
      case 'A':
        backgroundColor = 'green';
        break;
      case 'BBB':
      case 'BB':
      case 'B':
        backgroundColor = 'rgb(19, 108, 110)';
        break;
      case 'CCC':
      case 'CC':
      case 'C':
        backgroundColor = '#ee974d';
        break;
      case 'DDD':
      case 'DD':
      case 'D':
        backgroundColor = '#f8685f';
        break;
      default:
        backgroundColor = '#f8685f';
    }

    this.renderer.setStyle(
      this.el.nativeElement,
      'background-color',
      backgroundColor
    );
  }
}
