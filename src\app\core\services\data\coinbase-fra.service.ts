import { Injectable } from '@angular/core';
import { Coin, ICoin, IPortfolioStats, ITrade } from '../../interfaces/coins';

@Injectable({
  providedIn: 'root',
})
export class PortfolioCoinbaseFra extends Coin {
  constructor() {
    super();
  }

  // COINBASE
  public coins: ICoin[] = [
    {
      name: 'Bitcoin',
      nameApi: 'bitcoin',
      ticker: 'BTC',
      logo: '../../../assets/img/logo/btc.png',
      category: 'Layer 1',
      quantity: 0.02025,
      deposits: 614.35,
      averagePrice: 25595,
      logoColor: '#F7931A',
      description: `Bitcoin è una criptovaluta decentralizzata originariamente descritta in un white paper del 2008 da una persona, o un gruppo di persone, utilizzando l'alias <PERSON><PERSON>. È stato lanciato subito dopo, nel gennaio 2009.

Bitcoin è una valuta online peer-to-peer, il che significa che tutte le transazioni avvengono direttamente tra partecipanti alla rete uguali e indipendenti, senza la necessità di alcun intermediario che le consenta o le faciliti. Bitcoin è stato creato, secondo le parole di Nakamoto, per consentire "i pagamenti online possono essere inviati direttamente da una parte all'altra senza passare attraverso un istituto finanziario".

Alcuni concetti per un tipo simile di valuta elettronica decentralizzata precedono BTC, ma Bitcoin detiene la particolarità di essere la prima criptovaluta in assoluto ad entrare in uso effettivo.`,
    },
    {
      name: 'Ethereum',
      nameApi: 'ethereum',
      ticker: 'ETH',
      logo: '../../../assets/img/logo/eth.png',
      category: 'Layer 1',
      quantity: 0.9652,
      deposits: 1654.54,
      averagePrice: 1817,
      logoColor: '#627eea',
    },
    {
      name: 'Cardano',
      nameApi: 'cardano',
      ticker: 'ADA',
      logo: '../../../assets/img/logo/ada.png',
      category: 'Layer 1',
      quantity: 260.18,
      deposits: 153,
      averagePrice: 0.602,
      logoColor: '#0033ad',
      description: `Cardano è una piattaforma blockchain proof-of-stake che afferma che il suo obiettivo è consentire a "changemaker, innovatori e visionari" di realizzare un cambiamento globale positivo.

Per saperne di più su questo progetto, dai un'occhiata al nostro approfondimento su Cardano.

Il progetto open source mira anche a “ridistribuire il potere dalle strutture irresponsabili ai margini degli individui”, contribuendo a creare una società più sicura, trasparente ed equa.

Cardano è stata fondata nel 2017 e prende il nome dal poliedrico italiano del XVI secolo Gerolamo Cardano. Il token ADA nativo prende il nome dalla matematica del XIX secolo Ada Lovelace, ampiamente considerata la prima programmatrice di computer al mondo. Il token ADA è progettato per garantire che i proprietari possano partecipare al funzionamento della rete. Per questo motivo, coloro che detengono la criptovaluta hanno il diritto di votare su eventuali modifiche proposte al software.

Il team dietro la blockchain a strati afferma che ci sono già stati alcuni casi d'uso convincenti per la sua tecnologia, che mira a consentire lo sviluppo di app decentralizzate e contratti intelligenti con modularità.

Nell'agosto 2021, Charles Hoskinson ha annunciato il lancio dell'hard fork Alonzo, facendo aumentare il prezzo di Cardano, guadagnando il 116% nel mese successivo. Il 12 settembre 2021 è stato lanciato ufficialmente l'hard fork Cardano "Alonzo", portando la funzionalità del contratto intelligente sulla blockchain. Nelle 24 ore successive al lancio sono stati implementati oltre 100 contratti intelligenti.

Cardano viene utilizzato dalle aziende agricole per tracciare i prodotti freschi dal campo alla tavola, mentre altri prodotti costruiti sulla piattaforma consentono di archiviare le credenziali educative in modo a prova di manomissione e ai rivenditori di reprimere le merci contraffatte.
`,
    },
    {
      name: 'Polygon',
      nameApi: 'matic-network',
      ticker: 'MATIC',
      logo: '../../../assets/img/logo/matic.png',
      category: 'Layer 2',
      quantity: 291.59,
      deposits: 202,
      averagePrice: 0.72,
      logoColor: '#8247e5',
      description: `Polygon (in precedenza Matic Network) è la prima piattaforma ben strutturata e facile da usare per la scalabilità di Ethereum e lo sviluppo dell'infrastruttura. Il suo componente principale è Polygon SDK, un framework modulare e flessibile che supporta la creazione di più tipi di applicazioni.

Per saperne di più su questo progetto, dai un'occhiata al nostro approfondimento su Polygon Matic.

Utilizzando Polygon, è possibile creare catene di rollup ottimistiche, catene di rollup ZK, catene autonome o qualsiasi altro tipo di infrastruttura richiesta dallo sviluppatore.

Polygon trasforma efficacemente Ethereum in un vero e proprio sistema multi-catena (noto anche come Internet of Blockchains). Questo sistema multi-catena è simile ad altri come Polkadot, Cosmos, Avalanche ecc. con i vantaggi della sicurezza, dell'ecosistema vibrante e dell'apertura di Ethereum.

Il token $MATIC continuerà ad esistere e svolgerà un ruolo sempre più importante, proteggendo il sistema e consentendo la governance.

Polygon (precedentemente Matic Network) è una soluzione di ridimensionamento di livello 2 supportata da Binance e Coinbase. Il progetto cerca di stimolare l'adozione di massa delle criptovalute risolvendo i problemi di scalabilità su molte blockchain.

Polygon combina Plasma Framework e l'architettura blockchain proof-of-stake. Il framework Plasma utilizzato da Polygon, come proposto dal co-fondatore di Ethereum, Vitalik Buterin, consente la facile esecuzione di contratti intelligenti scalabili e autonomi.

Nulla cambierà per l'ecosistema esistente costruito sulla catena Plasma-POS. Con Polygon, vengono sviluppate nuove funzionalità attorno alla tecnologia comprovata esistente per espandere la capacità di soddisfare le diverse esigenze dell'ecosistema degli sviluppatori. Polygon continuerà a sviluppare la tecnologia di base in modo che possa adattarsi a un ecosistema più ampio.

Polygon vanta fino a 65.000 transazioni al secondo su una singola catena laterale, insieme a un tempo di conferma del blocco rispettabile inferiore a due secondi. Il framework consente inoltre la creazione di applicazioni finanziarie decentralizzate disponibili a livello globale su un'unica blockchain fondamentale.

Il framework Plasma offre a Polygon il potenziale di ospitare un numero illimitato di applicazioni decentralizzate sulla propria infrastruttura senza sperimentare i normali inconvenienti comuni alle blockchain proof-of-work. Finora, Polygon ha attirato più di 50 DApp sulla sua sidechain Ethereum protetta da PoS.

MATIC, i token nativi di Polygon, è un token ERC-20 in esecuzione sulla blockchain di Ethereum. I token vengono utilizzati per i servizi di pagamento su Polygon e come valuta di regolamento tra gli utenti che operano all'interno dell'ecosistema Polygon. Anche le commissioni di transazione sulle sidechain Polygon vengono pagate in token MATIC.`,
    },
    {
      name: 'Cosmos',
      nameApi: 'cosmos',
      ticker: 'ATOM',
      logo: '../../../assets/img/logo/atom.png',
      category: 'Layer 1',
      quantity: 4.98,
      deposits: 60,
      averagePrice: 12.21,
      logoColor: '#2e3148',
      description: `In poche parole, Cosmos si presenta come un progetto che risolve alcuni dei “problemi più difficili” che affligge il settore blockchain. Mira a offrire un antidoto ai protocolli di prova del lavoro “lenti, costosi, non scalabili e dannosi per l'ambiente”, come quelli utilizzati da Bitcoin, offrendo un ecosistema di blockchain connesse.

Gli altri obiettivi del progetto includono rendere la tecnologia blockchain meno complessa e difficile per gli sviluppatori grazie a un framework modulare che demistifica le app decentralizzate. Ultimo ma non meno importante, un protocollo di comunicazione Interblockchain rende più semplice la comunicazione tra le reti blockchain, prevenendo la frammentazione del settore.

Le origini di Cosmos risalgono al 2014, quando è stata fondata Tendermint, uno dei principali contributori della rete. Nel 2016 è stato pubblicato un white paper per Cosmos e l'anno successivo si è tenuta una vendita di token. I token ATOM vengono guadagnati attraverso un algoritmo ibrido di prova del palo e aiutano a mantenere sicuro il Cosmos Hub, la blockchain di punta del progetto. Questa criptovaluta ha anche un ruolo nella governance della rete.`,
      earnQuantity: 0.041,
    },
    {
      name: 'Solana',
      nameApi: 'solana',
      ticker: 'SOL',
      logo: '../../../assets/img/logo/sol.png',
      category: 'Layer 1',
      quantity: 14.94,
      deposits: 1263.67,
      averagePrice: 70.6,
      logoColor: '#0a0b0d',
    },
    {
      name: 'Ripple',
      nameApi: 'ripple',
      ticker: 'XRP',
      logo: '../../../assets/img/logo/xrp.png',
      category: 'Layer 1',
      quantity: 90.75,
      deposits: 50,
      averagePrice: 0.55,
      logoColor: '#23292F',
    },
    {
      name: 'Avalanche',
      nameApi: 'avalanche-2',
      ticker: 'AVAX',
      logo: '../../../assets/img/logo/avax.png',
      category: 'Layer 1',
      quantity: 0.54,
      deposits: 37,
      averagePrice: 68.59,
      logoColor: '#e84142',
      description: `Avalanche è una blockchain di primo livello che funziona come piattaforma per applicazioni decentralizzate e reti blockchain personalizzate. È uno dei rivali di Ethereum, che mira a spodestare Ethereum come la blockchain più popolare per i contratti intelligenti. L'obiettivo è raggiungere questo obiettivo ottenendo un output di transazioni più elevato, fino a 6.500 transazioni al secondo, senza compromettere la scalabilità.

Ciò è reso possibile dall'architettura unica di Avalanche. La rete Avalanche è composta da tre blockchain individuali: X-Chain, C-Chain e P-Chain. Ogni catena ha uno scopo distinto, che è radicalmente diverso dall'approccio utilizzato da Bitcoin ed Ethereum, ovvero far sì che tutti i nodi convalidino tutte le transazioni. Le blockchain Avalanche utilizzano anche diversi meccanismi di consenso in base ai loro casi d'uso.

Dopo il lancio sulla mainnet nel 2020, Avalanche ha lavorato allo sviluppo del proprio ecosistema di DApp e DeFi. Diversi progetti basati su Ethereum come SushiSwap e TrueUSD si sono integrati con Avalanche. Inoltre, la piattaforma lavora costantemente per migliorare l'interoperabilità tra il proprio ecosistema ed Ethereum, ad esempio attraverso lo sviluppo di bridge.`,
    },
    {
      name: 'Polkadot',
      nameApi: 'polkadot',
      ticker: 'DOT',
      logo: '../../../assets/img/logo/dot.png',
      category: 'Layer 1',
      quantity: 12.53,
      deposits: 106,
      averagePrice: 7.54,
      logoColor: '#e6007a',
      description: `Polkadot è un protocollo multichain frammentato open source che collega e protegge una rete di blockchain specializzate, facilitando il trasferimento cross-chain di qualsiasi tipo di dato o risorsa, non solo di token, consentendo così alle blockchain di essere interoperabili tra loro. Polkadot è stato progettato per fornire una base per un Internet decentralizzato di blockchain, noto anche come Web3.

Polkadot è noto come metaprotocollo di livello 0 perché è alla base e descrive un formato per una rete di blockchain di livello 1 note come parachain (catene parallele). Essendo un metaprotocollo, Polkadot è anche in grado di aggiornare in modo autonomo e senza fork la propria base di codice tramite la governance on-chain secondo la volontà della sua comunità di possessori di token.

Polkadot fornisce una base per supportare un web decentralizzato, controllato dai suoi utenti, e per semplificare la creazione di nuove applicazioni, istituzioni e servizi.

Il protocollo Polkadot può connettere catene pubbliche e private, reti senza autorizzazione, oracoli e tecnologie future, consentendo a queste blockchain indipendenti di condividere in modo affidabile informazioni e transazioni attraverso la Polkadot Relay Chain (spiegata più avanti).

Il token DOT nativo di Polkadot ha tre scopi chiari: picchettamento per operazioni e sicurezza, facilitazione della governance della rete e bonding token per connettere le parachain.

Polkadot ha quattro componenti principali:

     - Relay Chain: il “cuore” di Polkadot, che aiuta a creare consenso, interoperabilità e sicurezza condivisa attraverso la rete di diverse catene;
     - Parachain: catene indipendenti che possono avere i propri token ed essere ottimizzate per casi d'uso specifici;
     - Parathread: simili alle parachain ma con connettività flessibile basata su un modello economico pay-as-you-go;
     - Bridge: consentono a parachain e parathread di connettersi e comunicare con blockchain esterne come Ethereum.`,
    },
    {
      name: 'NEAR Protocol',
      nameApi: 'near',
      ticker: 'NEAR',
      logo: '../../../assets/img/logo/near.png',
      category: 'Layer 1',
      quantity: 13.51,
      deposits: 50,
      averagePrice: 3.7,
      logoColor: '#24272a',
      description: `NEAR Protocol è una blockchain di livello uno progettata come piattaforma di cloud computing gestita dalla comunità e che elimina alcune delle limitazioni che hanno impantanato le blockchain concorrenti, come basse velocità di transazione, basso throughput e scarsa interoperabilità. Ciò fornisce l'ambiente ideale per le DApp e crea una piattaforma di facile utilizzo per sviluppatori e utenti. Ad esempio, NEAR utilizza nomi di account leggibili dall'uomo, a differenza degli indirizzi di portafoglio crittografici comuni a Ethereum. NEAR introduce anche soluzioni uniche per problemi di scalabilità e dispone di un proprio meccanismo di consenso chiamato “Doomslug”.

Il protocollo NEAR è stato creato dal NEAR Collective, la sua comunità che sta aggiornando il codice iniziale e rilasciando aggiornamenti all'ecosistema. Il suo obiettivo dichiarato è quello di costruire una piattaforma che sia “abbastanza sicura da gestire beni di alto valore come denaro o identità e sufficientemente performante da renderli utili per la gente comune”.

Flux, un protocollo che consente agli sviluppatori di creare mercati basati su asset, materie prime, eventi del mondo reale, e Mintbase, una piattaforma di conio NFT, sono esempi di progetti costruiti sul protocollo NEAR.`,
    },
    {
      name: 'The Graph',
      nameApi: 'the-graph',
      ticker: 'GRT',
      logo: '../../../assets/img/logo/graph.webp',
      category: 'AI',
      quantity: 473.42,
      deposits: 126,
      averagePrice: 6.84,
      logoColor: '#4827A9',
    },
    // Profitto FET = 900€
    {
      name: 'Fetch.ai',
      nameApi: 'fetch-ai',
      ticker: 'FET',
      logo: '../../../assets/img/logo/fet.png',
      category: 'AI',
      quantity: 492.43,
      deposits: 243.43,
      averagePrice: 1.27,
      logoColor: '#1E2943',
      ecosystem: ['Ethereum'],
      description: `Fondato nel 2017 e lanciato tramite IEO su Binance nel marzo 2019, Fetch.AI è un laboratorio di 
      <span style='color: #4f4fff'><strong>intelligenza artificiale (AI)</strong></span> che costruisce una rete di apprendimento automatico aperta, senza autorizzazione e decentralizzata con un'economia crittografica. 
      Fetch.ai democratizza l'accesso alla tecnologia IA con una rete senza autorizzazione su cui chiunque può connettersi e accedere a set di dati sicuri utilizzando l'intelligenza artificiale autonoma per eseguire attività che sfruttano la sua rete globale di dati. 
      Il modello Fetch.AI affonda le sue radici in casi d'uso come l'ottimizzazione dei servizi di trading DeFi, delle reti di trasporto (parcheggi, micromobilità), delle reti energetiche intelligenti, dei viaggi - essenzialmente qualsiasi sistema digitale complesso che si basa su set di dati su larga scala.`,
    },
  ];

  public portfolioStats: IPortfolioStats = {
    gifts: 61.2,
    totalFees: -255.1,
    taxes: 12,
    realizedProfit: 0,
  };

  public closedTrades: ITrade[] = [];
}
