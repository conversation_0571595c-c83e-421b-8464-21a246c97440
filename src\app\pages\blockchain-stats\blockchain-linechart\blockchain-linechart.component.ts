import {
  ChangeDetectionStrategy,
  Component,
  effect,
  input,
  signal,
  untracked,
} from '@angular/core';
import { DefillamaService } from 'src/app/core/services/http/defillama.service';

@Component({
  selector: 'app-blockchain-linechart',
  templateUrl: './blockchain-linechart.component.html',
  styleUrl: './blockchain-linechart.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BlockchainLinechartComponent {
  public chartOptions = signal<undefined | {}>(undefined);

  protected tvlHistory!: any;
  timeInterval = input<string>();
  timeIntervalEffect = effect(() => {
    if (
      this.timeInterval() &&
      this.defillamaService.chainsTvlHistory().length > 0
    ) {
      if (this.timeInterval() === 'all')
        this.tvlHistory = this.defillamaService
          .chainsTvlHistory()
          .slice(this.defillamaService.chainsTvlHistory().length - 1464);
      if (this.timeInterval() === '2y')
        this.tvlHistory = this.defillamaService
          .chainsTvlHistory()
          .slice(this.defillamaService.chainsTvlHistory().length - 722);
      if (this.timeInterval() === '1y')
        this.tvlHistory = this.defillamaService
          .chainsTvlHistory()
          .slice(this.defillamaService.chainsTvlHistory().length - 366);
      if (this.timeInterval() === '30d')
        this.tvlHistory = this.defillamaService
          .chainsTvlHistory()
          .slice(this.defillamaService.chainsTvlHistory().length - 31);
      // console.log('LOGGA', this.tvlHistory);
      untracked(() => this.createLineChart());
    }
  });

  constructor(private defillamaService: DefillamaService) {
    effect(() => {});
  }

  createLineChart() {
    console.log('creo chart');

    const generateColors = (data) => {
      return data.map((d, idx) => {
        let color = d > 0 ? '#22c55f' : '#ef4544';

        return {
          offset: (idx / (data.length - 1)) * 100,
          color,
          opacity: 1,
        };
      });
    };

    this.chartOptions.set({
      chart: {
        type: 'line',
        // background: 'rgb(10, 10, 10)',
        width: '100%',
        height: '300px',
        foreColor: '#fff',
        fontFamily: 'Roboto, Arial, sans-serif',
        zoom: { autoScaleYaxis: true },
        toolbar: {
          show: false,
          offsetX: 0,
          offsetY: 0,
          tools: {
            download: false,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: false,
            customIcons: [],
          },
          export: {
            svg: {
              filename: undefined,
            },
            png: {
              filename: undefined,
            },
          },
          autoSelected: 'zoom',
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          opacity: 0.2,
        },
      },

      stroke: {
        width: 1.5,
        curve: 'smooth',
      },
      fill: {
        // colors: ['', '', '#fff'],
        // type: 'gradient',
        // gradient: {
        //   type: 'vertical',
        //   shadeIntensity: 1,
        //   opacityFrom: 1,
        //   opacityTo: 1,
        //   // gradientToColors: ['', '', ''], // green
        //   stops: [
        //     generateColors(
        //       this.portfolioHistory.portfolioMonthly.map(
        //         (history) => history.profit
        //       )
        //     ),
        //   ],
        // },
      },

      markers: {
        // size: 3,
        // strokeWidth: 1,
        // hover: {
        //   size: 6,
        // },
      },

      grid: {
        show: true,
        borderColor: '#424754',
        padding: {
          bottom: 20,
          right: 15,
          left: 0,
        },
        xaxis: {
          lines: {
            show: false,
          },
          tooltip: {
            enabled: false,
          },
        },
      },

      // colors: [
      //   '#1e38fc',
      //   '#2196F3',
      //   function () {
      //     return portfolioHistory().portfolioMonthly[
      //       portfolioHistory().portfolioMonthly.length - 1
      //     ].profit > 0
      //       ? 'var(--green-profit)'
      //       : '#ed451f';
      //   },

      //   // console.log('VALUE', value);
      //   // // return portfolioHistory.portfolioMonthly[w.globals.seriesLog[2]]
      //   // //   .profit > 0
      //   // //   ? 'var(--green-profit)'
      //   // //   : '#ed451f';
      // ],
      series: [
        {
          name: 'Tvl',
          data: this.tvlHistory.map((item) => item.tvl),
        },
      ],
      legend: {
        show: false,
        offsetY: -10,
        markers: {
          fillColors: ['#1e38fc', '#2196F3'],
        },
      },
      xaxis: {
        type: 'datetime',
        categories: this.tvlHistory.map((item) => {
          let newDate = new Date(item.date * 1000);
          const year = newDate.getFullYear();
          const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
          const day = newDate.getDate().toString().padStart(2, '0');

          return `${year}/${month}/${day}`;
        }),
        showAlways: false,
        forceNiceScale: true,

        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Roboto, Arial, sans-serif',
          },
          offsetY: 7,
          rotateAlways: true,
          hideOverlappingLabels: true,
        },
      },
      yaxis: {
        decimalsInFloat: 0,
        // max: this.maxY,
        // min: this.minY,
        showAlways: false,
        forceNiceScale: true,
        labels: {
          offsetX: -15,
          style: {
            fontSize: '11px',
            fontFamily: 'Roboto, Arial, sans-serif',
          },
          formatter: (value) => {
            return '$ ' + value.toFixed(0) / 1000000000 + ' B';
          },
        },
      },
      tooltip: {
        // x: {
        //   format: 'dd/MM/yy',
        // },
        theme: 'dark',
      },
      annotations: {
        xaxis: [],
        points: [
          {
            x: null,
            y: this.defillamaService.chainsTvlHistory()[
              this.defillamaService.chainsTvlHistory().length - 1
            ].tvl,
            marker: {
              size: 4,
              fillColor: '#2196F3',
              strokeColor: '#fff',
              radius: 1,
              cssClass: 'apexcharts-custom-class',
            },
            label: {
              borderColor: 'transparent',
              offsetY: 0,
              offsetX: -5,
              style: {
                color: '#fff',
                background: 'transparent',
                fontSize: '12px',
              },
              text: `$ ${
                (
                  this.defillamaService.chainsTvlHistory()[
                    this.defillamaService.chainsTvlHistory().length - 1
                  ].tvl / 1000000000
                ).toFixed(0) + ' B'
              }`,
            },
          },

          // {
          //   x: new Date('08 Dec 2017').getTime(),
          //   y: 9340.85,
          //   marker: {
          //     size: 0,
          //   },
          //   image: {
          //     path: '../../assets/images/ico-instagram.png',
          //   },
          // },
        ],
      },
      dataLabels: {
        enabled: false,
        enabledOnSeries: undefined,
      },
    });
  }

  finalDate() {
    let finalDate =
      this.defillamaService.chainsTvlHistory()[
        this.defillamaService.chainsTvlHistory().length - 1
      ].date;

    let newDate = new Date(finalDate);
    const year = newDate.getFullYear();
    const month = (newDate.getMonth() + 1).toString().padStart(2, '0');
    const day = newDate.getDate().toString().padStart(2, '0');

    // return `${year}/${month}/${day}`;
    return newDate.getTime();
  }
}
