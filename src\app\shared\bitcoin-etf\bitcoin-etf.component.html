@if (etfBtcStats()?.date) {
  <div class="container">
    <span class="title"> ETF Stats</span>
    @if (etfBtcStats()?.date) {
      <div class="etf">
        <!-- @if (etfBtcStatsNewDate()) {
    <div class="notification">!</div>
  } -->

        <div class="bitcoinEtfTitle">
          <div class="logo">
            <img src="../../../assets/img/logo/btc.png" alt="" />
          </div>
          <div class="price">
            {{ btcPrice() / eurusd() | $depositsNoDecimal }}
            <span
              class="change"
              [ngStyle]="{ color: btc24hChange() > 0 ? '#04dc00' : 'red' }"
            >
              @if (btc24hChange() > 0) {
                <i class="fa-solid fa-caret-up"></i>
              } @else {
                <i class="fa-solid fa-caret-down"></i>
              }
              {{ btc24hChange() | profitsPerc }}
            </span>
          </div>
          <!-- <div class="date">({{ etfBtcStats()?.date | date }})</div> -->
        </div>
        <div class="bitcoinEtf-info">
          <div class="bitcoinEtf-info-totalBtc">
            <!-- <span class="title">Total:</span> -->
            <span class="value"
              ><span style="color: #f7931a">₿</span>
              {{ etfBtcStats()?.totalBtc | thousandsSeparator }}</span
            >
            <!-- <i class="fa-solid fa-circle"></i> -->
          </div>
          <div class="bitcoinEtf-info-totalInflow">
            <!-- <span class="title">Inflow:</span> -->
            <span
              class="value"
              [ngStyle]="{
                color: etfBtcStats()?.total$ > 0 ? 'var(--green-profit)' : 'red'
              }"
            >
              {{ etfBtcStats()?.total$ | shortNumberUsd }}</span
            >
            <!-- <i class="fa-solid fa-circle"></i> -->
          </div>
          <!-- <div class="bitcoinEtf-info-totalInflowDaily">
      <span class="title">24h:</span>
      <span
        class="value"
        [ngStyle]="{
          color: etfBtcStats()?.daily$ > 0 ? 'var(--green-profit)' : 'red'
        }"
        >{{ etfBtcStats()?.daily$ | shortNumberUsd }}</span
      >
    </div> -->
        </div>
        @if (selectedEtf() !== "btc" && selectedEtf() !== "btc$") {
          <div class="moreInfo" (click)="selectedEtf.set('btc')">
            <i class="fa-solid fa-chevron-down"></i>
          </div>
        } @else {
          <div class="moreInfo" (click)="selectedEtf.set(undefined)">
            <i class="fa-solid fa-chevron-up"></i>
          </div>
        }
        @if (selectedEtf() === "btc" || selectedEtf() === "btc$") {
          <!-- <div class="data"> 
      <div class="header weeklyTitle">Week</div>
      <div class="header netInflowTitle">Weekly Net Inflow</div>
      <div class="header totalInflowTitl">Total Net Inflow</div>
      <div class="header totalBtcTitle">Total BTC</div>

      @for(week of etfHistory(); track week){
      <div [class.week]="true" [class]="($index + 1).toString()">
        {{ week?.date }}
      </div>
      <div
        [class.weeklyInflow]="true"
        [class]="($index + 1).toString()"
        [ngStyle]="{
          color: week?.weeklyNetInflow > 0 ? 'var(--green-profit)' : 'red'
        }"
      >
        {{ week?.weeklyNetInflow | shortNumberUsd }}
      </div>
      <div
        [class.totalInflow]="true"
        [class]="($index + 1).toString()"
        [ngStyle]="{
          color: week?.totalInflow > 0 ? 'var(--green-profit)' : 'red'
        }"
      >
        {{ week?.totalInflow | shortNumberUsd }}
      </div>
      <div [class.totalBtc]="true" [class]="($index + 1).toString()">
        <span style="color: #f7931a; margin-right: 0.5rem">₿</span>
        {{ week?.totalBtc | thousandsSeparator }}
      </div>
      }
    </div> -->

          <!-- <div class="data-chart">
      @if (!!imageUrl()) {
        <img [src]="imageUrl()" alt="" width="100%" height="100%" />
      } @else {
        <app-loader-spinner></app-loader-spinner>
      }
    </div> -->
          <div class="data-chart">
            <div class="crypto-list-buttons">
              <div
                class="crypto-list-buttons-btc"
                (click)="selectedEtf.set('btc')"
                [ngClass]="{ selected: selectedEtf() === 'btc' }"
              >
                BTC
              </div>
              <div
                class="crypto-list-buttons-dollar"
                (click)="selectedEtf.set('btc$')"
                [ngClass]="{ selected: selectedEtf() === 'btc$' }"
              >
                USD
              </div>

              <div class="date">{{ etfBtcStats()?.date | date }}</div>
            </div>
            @if (selectedEtf() == "btc") {
              <app-bitcoin-etf-chart [series]="'Btc'"></app-bitcoin-etf-chart>
            }
            @if (selectedEtf() == "btc$") {
              <app-bitcoin-etf-chart [series]="'$'"></app-bitcoin-etf-chart>
            }
          </div>
        }
      </div>
    } @else {
      <div class="etf-loader">
        <app-loader-spinner
          [style]="{ transform: 'scale(0.62)' }"
        ></app-loader-spinner>
      </div>
    }

    @if (etfEthStats()?.date) {
      <div class="etf">
        <!-- @if (etfBtcStatsNewDate()) {
    <div class="notification">!</div>
  } -->

        <div class="bitcoinEtfTitle eth">
          <div class="logo">
            <img src="../../../assets/img/logo/eth.png" alt="" />
          </div>
          <div class="price">
            {{ ethPrice() / eurusd() | $depositsNoDecimal }}
            <span
              class="change"
              [ngStyle]="{ color: eth24hChange() > 0 ? '#04dc00' : 'red' }"
            >
              @if (eth24hChange() > 0) {
                <i class="fa-solid fa-caret-up"></i>
              } @else {
                <i class="fa-solid fa-caret-down"></i>
              }
              {{ eth24hChange() | profitsPerc }}
            </span>
          </div>
          <!-- <div class="date">({{ etfEthStats()?.date | date }})</div> -->
        </div>
        <div class="bitcoinEtf-info">
          <div class="bitcoinEtf-info-totalBtc">
            <!-- <span class="title">Total:</span> -->
            <span class="value"
              ><span style="color: #627eea">Ξ</span>
              {{ etfEthStats()?.totalEth | thousandsSeparator }}</span
            >
            <!-- <i class="fa-solid fa-circle"></i> -->
          </div>
          <div class="bitcoinEtf-info-totalInflow">
            <!-- <span class="title">Inflow:</span> -->
            <span
              class="value"
              [ngStyle]="{
                color: etfEthStats()?.total$ > 0 ? 'var(--green-profit)' : 'red'
              }"
            >
              {{ etfEthStats()?.total$ | shortNumberUsd }}</span
            >
            <!-- <i class="fa-solid fa-circle"></i> -->
          </div>
          <!-- <div class="bitcoinEtf-info-totalInflowDaily">
      <span class="title">24h:</span>
      <span
        class="value"
        [ngStyle]="{
          color: etfEthStats()?.daily$ > 0 ? 'var(--green-profit)' : 'red'
        }"
        >{{ etfEthStats()?.daily$ | shortNumberUsd }}</span
      >
    </div> -->
        </div>
        @if (selectedEtf() !== "eth" && selectedEtf() !== "eth$") {
          <div class="moreInfo" (click)="selectedEtf.set('eth')">
            <i class="fa-solid fa-chevron-down"></i>
          </div>
        } @else {
          <div class="moreInfo" (click)="selectedEtf.set(undefined)">
            <i class="fa-solid fa-chevron-up"></i>
          </div>
        }
        @if (selectedEtf() === "eth" || selectedEtf() === "eth$") {
          <!-- <div class="data">
      <div class="header weeklyTitle">Week</div>
      <div class="header netInflowTitle">Weekly Net Inflow</div>
      <div class="header totalInflowTitl">Total Net Inflow</div>
      <div class="header totalBtcTitle">Total BTC</div>

      @for(week of etfHistory(); track week){
      <div [class.week]="true" [class]="($index + 1).toString()">
        {{ week?.date }}
      </div>
      <div
        [class.weeklyInflow]="true"
        [class]="($index + 1).toString()"
        [ngStyle]="{
          color: week?.weeklyNetInflow > 0 ? 'var(--green-profit)' : 'red'
        }"
      >
        {{ week?.weeklyNetInflow | shortNumberUsd }}
      </div>
      <div
        [class.totalInflow]="true"
        [class]="($index + 1).toString()"
        [ngStyle]="{
          color: week?.totalInflow > 0 ? 'var(--green-profit)' : 'red'
        }"
      >
        {{ week?.totalInflow | shortNumberUsd }}
      </div>
      <div [class.totalBtc]="true" [class]="($index + 1).toString()">
        <span style="color: #f7931a; margin-right: 0.5rem">₿</span>
        {{ week?.totalBtc | thousandsSeparator }}
      </div>
      }
    </div> -->

          <!-- <div class="data-chart">
      @if (!!imageUrl()) {
        <img [src]="imageUrl()" alt="" width="100%" height="100%" />
      } @else {
        <app-loader-spinner></app-loader-spinner>
      }
    </div> -->
          <div class="data-chart">
            <div class="crypto-list-buttons">
              <div
                class="crypto-list-buttons-btc"
                (click)="selectedEtf.set('eth')"
                [ngClass]="{ selected: selectedEtf() === 'eth' }"
              >
                ETH
              </div>
              <div
                class="crypto-list-buttons-dollar"
                (click)="selectedEtf.set('eth$')"
                [ngClass]="{ selected: selectedEtf() === 'eth$' }"
              >
                USD
              </div>
              <div class="date">{{ etfEthStats()?.date | date }}</div>

              <div class="extra"></div>
            </div>
            @if (selectedEtf() == "eth") {
              <app-ethereum-etf-chart [series]="'Eth'"></app-ethereum-etf-chart>
            }
            @if (selectedEtf() == "eth$") {
              <app-ethereum-etf-chart [series]="'$'"></app-ethereum-etf-chart>
            }
          </div>
        }
      </div>
    } @else {
      <div class="etf-loader">
        <app-loader-spinner
          [style]="{ transform: 'scale(0.62)' }"
        ></app-loader-spinner>
      </div>
    }
  </div>
}
