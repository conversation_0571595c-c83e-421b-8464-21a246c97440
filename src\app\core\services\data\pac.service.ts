import { Injectable } from '@angular/core';
import { Coin, ICoin, IPortfolioStats, ITrade } from '../../interfaces/coins';
import { PortfolioBinanceAndrea } from './binance-andrea.service';
import { PortfolioCoinbaseFra } from './coinbase-fra.service';

@Injectable({
  providedIn: 'root',
})
export class PortfolioPAC extends Coin {
  constructor(
    private coinbaseFra: PortfolioCoinbaseFra,
    private binanceAndrea: PortfolioBinanceAndrea,
  ) {
    super();
  }

  public coinsTest: ICoin[] = [];

  // PAC
  // public coins: ICoin[] = [
  //   {
  //     name: 'Bitcoin',
  //     nameApi: 'bitcoin',
  //     ticker: 'BTC',
  //     logo: '../../../assets/img/logo/btc.png',
  //     category: 'Layer 1',
  //     quantity: 0.05051823,
  //     deposits: 1293,
  //     averagePrice: 25595,
  //     logoColor: '#F7931A',
  //     ecosystem: ['Bitcoin'],
  //   },
  //   {
  //     name: 'Ethereum',
  //     nameApi: 'ethereum',
  //     ticker: 'ETH',
  //     logo: '../../../assets/img/logo/eth.png',
  //     category: 'Layer 1',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'ETH')?.quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'ETH')?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'ETH')?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'ETH')?.deposits,
  //     logoColor: '#627eea',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Cardano',
  //     nameApi: 'cardano',
  //     ticker: 'ADA',
  //     logo: '../../../assets/img/logo/ada.png',
  //     category: 'Layer 1',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'ADA')?.quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'ADA')?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'ADA')?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'ADA')?.deposits,
  //     logoColor: '#0033ad',
  //     ecosystem: ['Cardano'],
  //   },
  //   {
  //     name: 'Polygon',
  //     nameApi: 'matic-network',
  //     ticker: 'MATIC',
  //     logo: '../../../assets/img/logo/matic.png',
  //     category: 'Layer 2',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'MATIC').quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'MATIC')
  //         ?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'MATIC')
  //         ?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'MATIC')
  //         ?.deposits,
  //     logoColor: '#8247e5',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Eos',
  //     nameApi: 'eos',
  //     ticker: 'EOS',
  //     logo: '../../../assets/img/logo/eos.png',
  //     category: 'Layer 1',
  //     quantity: 11.86,
  //     deposits: 36,
  //     averagePrice: 3.02,
  //     logoColor: '#000000',
  //     ecosystem: ['Eos'],
  //   },
  //   {
  //     name: 'Axie Infinity',
  //     nameApi: 'axie-infinity',
  //     ticker: 'AXS',
  //     logo: '../../../assets/img/logo/axie-infinity.png',
  //     category: 'Gaming',
  //     quantity: 4.47594231,
  //     deposits: 25,
  //     averagePrice: 5.59,
  //     logoColor: '#0055d5',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Alchemy Pay',
  //     nameApi: 'alchemy-pay',
  //     ticker: 'ACH',
  //     logo: '../../../assets/img/logo/alchemy-pay.png',
  //     category: 'De-Fi',
  //     quantity: 482.41,
  //     deposits: 24,
  //     averagePrice: 0.05,
  //     logoColor: '#2e3567',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Cosmos',
  //     nameApi: 'cosmos',
  //     ticker: 'ATOM',
  //     logo: '../../../assets/img/logo/atom.png',
  //     category: 'Layer 1',
  //     quantity: 4.9114,
  //     deposits: 60,
  //     averagePrice: 12.21,
  //     logoColor: '#2e3148',
  //     ecosystem: ['Cosmos'],
  //   },
  //   {
  //     name: 'Solana',
  //     nameApi: 'solana',
  //     ticker: 'SOL',
  //     logo: '../../../assets/img/logo/sol.png',
  //     category: 'Layer 1',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'SOL').quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'SOL')?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'SOL')?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'SOL')?.deposits,
  //     logoColor: '#0a0b0d',
  //     ecosystem: ['Solana'],
  //   },
  //   {
  //     name: 'Ripple',
  //     nameApi: 'ripple',
  //     ticker: 'XRP',
  //     logo: '../../../assets/img/logo/xrp.png',
  //     category: 'Layer 1',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'XRP')?.quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'XRP')?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'XRP')?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'XRP')?.deposits,
  //     averagePrice: 0.55,
  //     logoColor: '#23292F',
  //     ecosystem: ['Xrp'],
  //   },
  //   {
  //     name: 'Uniswap',
  //     nameApi: 'uniswap',
  //     ticker: 'UNI',
  //     logo: '../../../assets/img/logo/uniswap.png',
  //     category: 'De-Fi',
  //     quantity: 1.92030248,
  //     deposits: 10,
  //     averagePrice: 5.21,
  //     logoColor: '#ff007a',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Chainlink',
  //     nameApi: 'chainlink',
  //     ticker: 'LINK',
  //     logo: '../../../assets/img/logo/chainlink.png',
  //     category: 'De-Fi',
  //     quantity: 9.5032698,
  //     deposits: 70,
  //     averagePrice: 7.37,
  //     logoColor: '#2a5ada',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Aave',
  //     nameApi: 'aave',
  //     ticker: 'AAVE',
  //     logo: '../../../assets/img/logo/aave.png',
  //     category: 'De-Fi',
  //     quantity: 0.14833635,
  //     deposits: 10,
  //     averagePrice: 67.41,
  //     logoColor: '#b6509e',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Origin',
  //     nameApi: 'origin-protocol',
  //     ticker: 'OGN',
  //     logo: '../../../assets/img/logo/origin.png',
  //     category: 'De-Fi',
  //     quantity: 80.24,
  //     deposits: 55,
  //     averagePrice: 0.69,
  //     logoColor: '#2087fb',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Avalanche',
  //     nameApi: 'avalanche-2',
  //     ticker: 'AVAX',
  //     logo: '../../../assets/img/logo/avax.png',
  //     category: 'Layer 1',
  //     quantity: 0.539,
  //     deposits: 37,
  //     averagePrice: 68.59,
  //     logoColor: '#e84142',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Stellar',
  //     nameApi: 'stellar',
  //     ticker: 'XLM',
  //     logo: '../../../assets/img/logo/stellar.png',
  //     category: 'De-Fi',
  //     quantity: 86.67,
  //     deposits: 10,
  //     averagePrice: 0.12,
  //     logoColor: '#ffffff',
  //     ecosystem: ['Stellar'],
  //   },
  //   {
  //     name: 'Quant',
  //     nameApi: 'quant-network',
  //     ticker: 'QNT',
  //     logo: '../../../assets/img/logo/quant.png',
  //     category: 'Network',
  //     quantity: 0.058,
  //     deposits: 10,
  //     averagePrice: 172.24,
  //     logoColor: '#ef4f1f',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Decentraland',
  //     nameApi: 'decentraland',
  //     ticker: 'MANA',
  //     logo: '../../../assets/img/logo/decentraland.png',
  //     category: 'Gaming',
  //     quantity: 79.26723981,
  //     deposits: 51,
  //     averagePrice: 0.65,
  //     logoColor: '#ff4b56',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Loopring',
  //     nameApi: 'loopring',
  //     ticker: 'LRC',
  //     logo: '../../../assets/img/logo/loopring.png',
  //     category: 'Layer 2',
  //     quantity: 12.25,
  //     deposits: 11,
  //     averagePrice: 0.92,
  //     logoColor: '#1c42ff',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Polkadot',
  //     nameApi: 'polkadot',
  //     ticker: 'DOT',
  //     logo: '../../../assets/img/logo/dot.png',
  //     category: 'Layer 1',
  //     quantity: 7.6832,
  //     deposits: 56,
  //     averagePrice: 7.54,
  //     logoColor: '#e6007a',
  //     ecosystem: ['Polkadot'],
  //   },
  //   {
  //     name: 'Cronos',
  //     nameApi: 'crypto-com-chain',
  //     ticker: 'CRO',
  //     logo: '../../../assets/img/logo/cro.png',
  //     category: 'Centralized Exchange',
  //     quantity: 55.68,
  //     deposits: 24,
  //     averagePrice: 0.43,
  //     logoColor: '#103f68',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'JasmyCoin',
  //     nameApi: 'jasmycoin',
  //     ticker: 'JASMY',
  //     logo: '../../../assets/img/logo/jasmy.png',
  //     category: 'Internet of Things',
  //     quantity: 811.33,
  //     deposits: 15,
  //     averagePrice: 0.018,
  //     logoColor: '#f7941c',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'ApeCoin',
  //     nameApi: 'apecoin',
  //     ticker: 'APE',
  //     logo: '../../../assets/img/logo/ape-coin.png',
  //     category: 'Gaming',
  //     quantity: 0.81,
  //     deposits: 10,
  //     averagePrice: 12.32,
  //     logoColor: '#0746c8',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Compound',
  //     nameApi: 'compound-governance-token',
  //     ticker: 'COMP',
  //     logo: '../../../assets/img/logo/compound.png',
  //     category: 'De-Fi',
  //     quantity: 0.067,
  //     deposits: 5,
  //     averagePrice: 71.79,
  //     logoColor: '#00d395',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Kava',
  //     nameApi: 'kava',
  //     ticker: 'KAVA',
  //     logo: '../../../assets/img/logo/kava.png',
  //     category: 'Layer 1',
  //     quantity: 2.91,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#ff433e',
  //     ecosystem: ['Cosmos'],
  //   },
  //   {
  //     name: 'Galxe',
  //     nameApi: 'project-galaxy',
  //     ticker: 'GAL',
  //     logo: '../../../assets/img/logo/galxe.png',
  //     category: 'NFT',
  //     quantity: 1.23,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#1e5dff',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'The Sandbox',
  //     nameApi: 'the-sandbox',
  //     ticker: 'SAND',
  //     logo: '../../../assets/img/logo/the-sandbox.png',
  //     category: 'Gaming',
  //     quantity: 65.39986568,
  //     deposits: 25,
  //     averagePrice: 0.38,
  //     logoColor: '#00aeef',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'NEAR Protocol',
  //     nameApi: 'near',
  //     ticker: 'NEAR',
  //     logo: '../../../assets/img/logo/near.png',
  //     category: 'Layer 1',
  //     quantity: 0.633,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#24272a',
  //     ecosystem: ['Near'],
  //   },
  //   {
  //     name: 'Render',
  //     nameApi: 'render-token',
  //     ticker: 'RNDR',
  //     logo: '../../../assets/img/logo/render.png',
  //     category: 'AI',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'RNDR')?.quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'RNDR')
  //         ?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'RNDR')?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'RNDR')
  //         ?.deposits,
  //     averagePrice: 0,
  //     logoColor: '#cf1011',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: '00 Token',
  //     nameApi: 'zer0zer0',
  //     ticker: '00',
  //     logo: '../../../assets/img/logo/00.png',
  //     category: 'Governance',
  //     quantity: 8.32,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#0a0b0d',
  //   },
  //   {
  //     name: 'Aleph.im',
  //     nameApi: 'aleph',
  //     ticker: 'ALEPH',
  //     logo: '../../../assets/img/logo/aleph-im.png',
  //     category: 'AI',
  //     quantity: 11.48,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#0054ff',
  //   },
  //   {
  //     name: 'IoTeX',
  //     nameApi: 'iotex',
  //     ticker: 'IOTX',
  //     logo: '../../../assets/img/logo/iotex.png',
  //     category: 'Internet of Things',
  //     quantity: 19.94,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#38dfa3',
  //   },
  //   {
  //     name: 'Onyxcoin',
  //     nameApi: 'chain-2',
  //     ticker: 'XCN',
  //     logo: '../../../assets/img/logo/onyxcoin.png',
  //     category: 'De-Fi',
  //     quantity: 41.63,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#161a1f',
  //   },
  //   {
  //     name: 'Clover Finance',
  //     nameApi: 'clover-finance',
  //     ticker: 'CLV',
  //     logo: '../../../assets/img/logo/clover.png',
  //     category: 'De-Fi',
  //     quantity: 2.2,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#42c37b',
  //   },
  //   {
  //     name: 'Injective',
  //     nameApi: 'injective-protocol',
  //     ticker: 'INJ',
  //     logo: '../../../assets/img/logo/injective.webp',
  //     category: 'De-Fi',
  //     quantity: 0.9443,
  //     deposits: 15,
  //     averagePrice: 15.88,
  //     logoColor: '#00A8F5',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'The Graph',
  //     nameApi: 'the-graph',
  //     ticker: 'GRT',
  //     logo: '../../../assets/img/logo/graph.webp',
  //     category: 'AI',
  //     quantity:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'GRT')?.quantity +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'GRT')?.quantity,
  //     deposits:
  //       this.coinbaseFra.coins.find((coin) => coin.ticker == 'GRT')?.deposits +
  //       this.binanceAndrea.coins.find((coin) => coin.ticker == 'GRT')?.deposits,
  //     averagePrice: 14.2,
  //     logoColor: '#4827A9',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'VeChain',
  //     nameApi: 'vechain',
  //     ticker: 'VET',
  //     logo: '../../../assets/img/logo/vet.webp',
  //     category: 'Internet of Things',
  //     quantity: 552.996,
  //     deposits: 0,
  //     averagePrice: 0,
  //     logoColor: '#28008C',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'Vara Network',
  //     nameApi: 'vara-network',
  //     ticker: 'VARA',
  //     logo: '../../../assets/img/logo/vara.webp',
  //     category: 'Layer 1',
  //     quantity: this.coinbaseFra.coins.find((coin) => coin.ticker == 'VARA')
  //       ?.quantity,
  //     deposits: this.coinbaseFra.coins.find((coin) => coin.ticker == 'VARA')
  //       ?.deposits,
  //     averagePrice: 0,
  //     logoColor: '#28008C',
  //   },
  //   {
  //     name: 'Fetch.ai',
  //     nameApi: 'fetch-ai',
  //     ticker: 'FET',
  //     logo: '../../../assets/img/logo/fet.png',
  //     quantity: this.binanceAndrea.coins.find((coin) => coin.ticker == 'FET')
  //       ?.quantity,
  //     deposits: this.binanceAndrea.coins.find((coin) => coin.ticker == 'FET')
  //       ?.deposits,
  //     category: 'AI',
  //     averagePrice: 0.44,
  //     logoColor: '#1E2943',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'TRON',
  //     nameApi: 'tron',
  //     ticker: 'TRX',
  //     logo: '../../../assets/img/logo/tron.webp',
  //     quantity: 1003.56,
  //     deposits: 100,
  //     category: 'Layer 1',
  //     averagePrice: 0.1,
  //     logoColor: '#C02F26',
  //     ecosystem: ['Tron'],
  //   },
  //   {
  //     name: 'Immutable',
  //     nameApi: 'immutable-x',
  //     ticker: 'IMX',
  //     logo: '../../../assets/img/logo/immutable.webp',
  //     quantity: 25.696,
  //     deposits: 50,
  //     category: 'Gaming',
  //     averagePrice: 1.94,
  //     logoColor: '#000',
  //     ecosystem: ['Ethereum'],
  //   },
  //   {
  //     name: 'BNB',
  //     nameApi: 'binancecoin',
  //     ticker: 'BNB',
  //     logo: '../../../assets/img/logo/bnb.webp',
  //     quantity: this.binanceAndrea.coins.find((coin) => coin.ticker == 'BNB')
  //       ?.quantity,
  //     deposits: this.binanceAndrea.coins.find((coin) => coin.ticker == 'BNB')
  //       ?.deposits,
  //     averagePrice: 294.1,
  //     category: 'Layer 1',
  //     logoColor: '#F0B90B',
  //   },
  //   {
  //     name: 'Arbitrum',
  //     nameApi: 'arbitrum',
  //     ticker: 'ARB',
  //     logo: '../../../assets/img/logo/arb.png',
  //     category: 'Layer 2',
  //     quantity: this.binanceAndrea.coins.find((coin) => coin.ticker == 'ARB')
  //       ?.quantity,
  //     deposits: this.binanceAndrea.coins.find((coin) => coin.ticker == 'ARB')
  //       ?.deposits,
  //     averagePrice: 1.94,
  //     logoColor: '#2D6E8D',
  //   },
  //   {
  //     name: 'Sui',
  //     nameApi: 'sui',
  //     ticker: 'SUI',
  //     logo: '../../../assets/img/logo/sui.webp',
  //     category: 'Layer 1',
  //     quantity: this.binanceAndrea.coins.find((coin) => coin.ticker == 'SUI')
  //       ?.quantity,
  //     deposits: this.binanceAndrea.coins.find((coin) => coin.ticker == 'SUI')
  //       ?.deposits,
  //     averagePrice: 1.3,
  //     logoColor: '#64ADE5',
  //   },
  // ];

  public portfolioStats: IPortfolioStats | null = {
    gifts:
      this.coinbaseFra.portfolioStats.gifts +
      this.binanceAndrea.portfolioStats.gifts,
    totalFees:
      this.coinbaseFra.portfolioStats.totalFees +
      this.binanceAndrea.portfolioStats.totalFees,
    taxes: 24,
    realizedProfit: 0,
  };

  mergeAccounts() {
    const coinbaseFraCoins = this.coinbaseFra.coins;
    const binanceAndreaCoins = this.binanceAndrea.coins;

    // console.log('merge1', coinbaseFraCoins, binanceAndreaCoins);

    // Funzione di utilità per trovare un elemento con lo stesso ticker in
    const findCoinByTicker = (ticker: string) =>
      this.coinsTest.find((coin) => coin.ticker === ticker);

    // Itera sugli elementi di coinbaseFraCoins
    coinbaseFraCoins.forEach((coin) => {
      const matchingCoin = binanceAndreaCoins.find(
        (item) => item.ticker === coin.ticker,
      );
      if (matchingCoin) {
        // Se il ticker è già presente in binanceAndreaCoins, aggiorna quantity e deposits
        const mergedCoin = findCoinByTicker(coin.ticker);

        if (mergedCoin) {
          mergedCoin.quantity += coin.quantity + matchingCoin.quantity;
          mergedCoin.deposits += coin.deposits + matchingCoin.deposits;
        } else {
          let newDescription = coin.description
            ? coin.description
            : matchingCoin.description;

          let newEarnQuantity = matchingCoin.earnQuantity
            ? matchingCoin.earnQuantity
            : 0;

          let firstEarnQuantity = coin.earnQuantity ? coin.earnQuantity : 0;

          this.coinsTest.push({
            ...coin,
            quantity:
              coin.quantity +
              matchingCoin.quantity -
              (firstEarnQuantity + newEarnQuantity),
            deposits: coin.deposits + matchingCoin.deposits,
            description: newDescription,
            rating: coin.rating,
            earnQuantity: firstEarnQuantity + newEarnQuantity,
          });
        }
      } else {
        // Se il ticker non è presente in binanceAndreaCoins, aggiungi il nuovo coin
        this.coinsTest.push({ ...coin });
      }
    });

    // Itera sugli elementi di binanceAndreaCoins
    binanceAndreaCoins.forEach((coin) => {
      const matchingCoin = findCoinByTicker(coin.ticker);

      if (!matchingCoin) {
        // Se il ticker non è presente in coinbaseFraCoins, aggiungi il nuovo coin
        this.coinsTest.push({ ...coin });
      }
    });

    this.coins = this.coinsTest;

    this.calculateStats();

    // console.log('MERGE', this.coins);
  }
  public closedTrades: ITrade[] = [
    // {
    //   tradeNum: 1,
    //   date: '01/01/2024',
    //   name: 'Bitcoin',
    //   ticker: 'BTC',
    //   quantity: 0.001,
    //   buyPrice: 25000,
    //   purchase: 1000,
    //   sellPrice: 80000,
    //   revenue: 6500,
    //   profit: 5500,
    //   profitPerc: 220,
    // },
  ];
}
