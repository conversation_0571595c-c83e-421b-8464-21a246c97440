import { HttpClient } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { Observable, Subject, tap } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CryptopanicService {
  private botToken = '6832288708:AAEx-TzWyKNPpNI6FjVjkITaxsNlwmNMR08';
  public cryptopanicList = signal([]);
  public loading$ = new Subject();
  public prevNews2 = [];

  constructor(private http: HttpClient) {}

  getUpdates(): Observable<any> {
    const apiUrl = `https://api.telegram.org/bot${this.botToken}/getUpdates`;

    if (localStorage.getItem('cryptopanicNews')) {
      this.prevNews2 = JSON.parse(localStorage.getItem('cryptopanicNews'));
    }

    return this.http.get(apiUrl).pipe(
      tap((data) => {
        console.table('CRYPTOPANIC NEWS DATA', data.result);
        let news = [];

        data.result.forEach((element) => {
          if (element.channel_post) {
            const text = element.channel_post.text;
            const date = +element.channel_post.date * 1000;
            const url = element.channel_post.entities[0].url;
            const indexOfBracket = text.indexOf('[');

            if (indexOfBracket !== -1) {
              const modifiedText = text.substring(0, indexOfBracket);
              news.push({
                title: modifiedText,
                date: date,
                timestamp: date,
                url: url,
                image_url: 'assets/img/topnews.jpg',
                tags: [
                  {
                    name: 'Top News',
                  },
                ],
              });
            }
          }
        });

        let finalNews = JSON.parse(localStorage.getItem('cryptopanicNews'))
          ? JSON.parse(localStorage.getItem('cryptopanicNews'))
          : [];

        news.forEach((item) => {
          const title = item.title;
          if (!this.prevNews2.some((obj) => obj.title === title)) {
            finalNews.push(item);
          }
        });

        console.log('FINAL NEWS', finalNews);

        localStorage.setItem('cryptopanicNews', JSON.stringify(finalNews));

        this.cryptopanicList.set(finalNews);
        console.log('CRYPTOPANIC NEWS', news);
      }),
    );
  }

  // Method to load news on-demand (for NewsComponent)
  loadNewsOnDemand() {
    if (localStorage.getItem('cryptopanicNews')) {
      this.cryptopanicList.set(
        JSON.parse(localStorage.getItem('cryptopanicNews')),
      );
    }
    return this.getUpdates();
  }
}
