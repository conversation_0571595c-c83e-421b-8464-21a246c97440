import it from '@angular/common/locales/it';
import { APP_INITIALIZER, isDevMode, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  provideHttpClient,
  withInterceptorsFromDi,
} from '@angular/common/http';
import { NgApexchartsModule } from 'ng-apexcharts';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HomeModule } from './pages/home/<USER>';
import { NewsModule } from './pages/news/news.module';
import { AccountSelectorModule } from './shared/account-selector/account-selector.module';
import { LoaderSpinnerModule } from './shared/loader-spinner/loader-spinner.module';
import { PriceTabModule } from './shared/price-tab/price-tab.module';

import { AngularFireModule } from '@angular/fire/compat';
import { AngularFireAuthModule } from '@angular/fire/compat/auth';
import { AngularFirestoreModule } from '@angular/fire/compat/firestore';
import { environment } from 'src/environments/environment';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ServiceWorkerModule } from '@angular/service-worker';
import { AccountDepositsModule } from './pages/account-deposits/account-deposits.module';
import { LoginModule } from './pages/login/login.module';
// import function to register Swiper custom elements
import { register } from 'swiper/element/bundle';
import { RetryInterceptor } from './core/services/interceptors/retry-http-errors.interceptor';
import { PortfolioInitializerService } from './core/services/portfolio-initializer.service';
import { ModalModule } from './shared/modal/modal.module';
import { SideMenuDesktopModule } from './shared/side-menu-desktop/side-menu-desktop.module';
import { WidgetsModule } from './shared/widgets/widgets.module';
// register Swiper custom elements
register();

registerLocaleData(it);

@NgModule({
  declarations: [AppComponent],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HomeModule,
    NgApexchartsModule,
    LoaderSpinnerModule,
    PriceTabModule,
    AccountSelectorModule,
    WidgetsModule,
    AngularFireModule.initializeApp(environment.firebase),
    AngularFirestoreModule,
    AngularFireAuthModule,
    BrowserAnimationsModule,
    LoginModule,
    NewsModule,
    SideMenuDesktopModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerImmediately',
    }),
    // provideFirebaseApp(() =>
    //   initializeApp({
    //     projectId: 'crypto-a6806',
    //     appId: '1:***********:web:307dba25bfd89d130cb374',
    //     databaseURL:
    //       'https://crypto-a6806-default-rtdb.europe-west1.firebasedatabase.app',
    //     storageBucket: 'crypto-a6806.appspot.com',
    //     apiKey: 'AIzaSyB3vfiZbqoTHFQxEz_I4eCQec3PjTlR4yU',
    //     authDomain: 'crypto-a6806.firebaseapp.com',
    //     messagingSenderId: '***********',
    //     measurementId: 'G-H7MHK24RPD',
    //   })
    // ),
    AccountDepositsModule,
    WidgetsModule,
    ModalModule,
  ],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: (p: PortfolioInitializerService) => () => p.initialize(),
      deps: [PortfolioInitializerService],
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: RetryInterceptor,
      multi: true,
    },
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AppModule {}
