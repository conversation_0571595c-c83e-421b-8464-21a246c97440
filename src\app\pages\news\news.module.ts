import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { PipesModule } from 'src/app/core/utils/pipes.module';
import { LoaderSpinnerModule } from '../../shared/loader-spinner/loader-spinner.module';
import { NewsComponent } from './news.component';

@NgModule({
  declarations: [NewsComponent],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    LoaderSpinnerModule,
    PipesModule,
    RouterModule,
  ],
  exports: [NewsComponent],
})
export class NewsModule {}
