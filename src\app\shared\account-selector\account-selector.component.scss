.bg {
  display: none;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background-color: black;
  opacity: 0.75;
  z-index: 998;

  &.menu {
    display: block;
  }
}

.account-selector {
  padding: 0.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  & .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-items: center;
    font-size: 3rem;
    font-weight: 500;
    padding: 1rem 0;
  }

  & .selector {
    & .account-selector-title {
      grid-row: 1/2;
      grid-column: 1/-1;
      align-self: center;
      font-size: 1.8rem;
      font-weight: 500;
      color: #fff;
      margin-bottom: 0.5rem;
    }

    & .account-selector-buttons {
      display: inline-flex;
      cursor: pointer;
      margin-bottom: 0.5rem;
      position: relative;
      z-index: 1000;

      &.hidden {
        z-index: 997;
      }

      & .total {
        color: #c4c4c4;
        background-color: #282828;
        padding: 0.7rem 1.2rem;
        border-radius: 5px;
        margin-right: 0.5rem;
        font-size: 1.4rem;
        border: none;
      }

      & select {
        color: #c4c4c4;
        background-color: #282828;
        padding: 0.7rem 1.2rem;
        border-radius: 5px;
        margin-right: 0.5rem;
        font-size: 1.4rem;
        width: 11rem;
        border: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        -ms-appearance: none;
        appearance: none;
        cursor: pointer;
        // -webkit-appearance:button !important;
        // -moz-appearance:button !important;
        //     appearance:button !important;

        &.selected {
          background-color: #4b4b4b;
          color: white;
        }

        & option {
          font-size: 1.4rem;
        }
      }

      & .select {
        // margin: 1rem;
        position: relative;
        // display: flex;
        width: 12rem;
        // height: 3em;
        // line-height: 3;
        // background: #2c3e50;
        // overflow: hidden;
        // border-radius: 0.25em;

        &:hover::after {
          color: #ffffff;
        }
      }
    }

    .buttons {
      color: #fff;
      background-color: #012a6a;
      padding: 0.7rem 1.2rem;
      border-radius: 5px;
      margin-right: 0.5rem;
      font-size: 1.4rem;
      font-weight: 500;
      border: none;
    }

    .select-after {
      & form {
        position: relative;
        & i {
          color: #fff;
          position: absolute;
          top: 6px;
          right: 6px;
          font-size: 1.6rem;
          padding: 0 1em;
          cursor: pointer;
          pointer-events: none;
        }
      }
    }

    & .select2 form {
      position: relative;

      & i {
        color: #fff;
        position: absolute;
        top: 6px;
        right: 6px;
        font-size: 1.6rem;
        padding: 0 1em;
        cursor: pointer;
        pointer-events: none;
      }
    }
  }

  // DROPDOWN
  // .menu {
  //   display: flex;
  //   flex-direction: row;
  //   list-style-type: none;
  //   margin: 0;
  //   padding: 0;
  //   position: relative;

  //   & .notification {
  //     position: absolute;
  //     top: -10px;
  //     right: 193px;
  //     background-color: green;
  //     border-radius: 50%;
  //     font-size: 1.4rem;
  //     width: 22px;
  //     height: 22px;
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     z-index: 1000;
  //   }
  // }

  // .menu > li {
  //   margin: 0 1rem;
  //   overflow: hidden;
  //   background: #4b4b4b;
  //   height: 42%;
  //   padding: 1.3rem;
  //   font-size: 1.6rem;
  //   border-radius: 5px;
  //   text-align: center;
  //   display: flex;
  //   align-items: center;
  //   cursor: pointer;

  //   &.news {
  //     position: relative;

  //     & .notification {
  //       position: absolute;
  //       top: 10px;
  //       left: 72px;
  //       background-color: green;
  //       border-radius: 50%;
  //       font-size: 1.4rem;
  //       width: 22px;
  //       height: 22px;
  //       display: flex;
  //       justify-content: center;
  //       align-items: center;
  //       z-index: 1000;
  //     }
  //   }
  // }

  // .menu > ul {
  //   margin-top: 0.2rem;
  // }

  // .menu-button-container {
  //   display: none;
  //   height: 100%;
  //   width: 30px;
  //   cursor: pointer;
  //   flex-direction: column;
  //   justify-content: center;
  //   align-items: center;
  // }

  // #menu-toggle {
  //   display: none;
  // }

  // .menu-button,
  // .menu-button::before,
  // .menu-button::after {
  //   display: block;
  //   background-color: #fff;
  //   position: absolute;
  //   height: 2px;
  //   width: 30px;
  //   transition: transform 1s cubic-bezier(0.23, 1, 0.32, 1);
  //   border-radius: 2px;
  // }

  // .menu-button::before {
  //   content: "";
  //   margin-top: -8px;
  // }

  // .menu-button::after {
  //   content: "";
  //   margin-top: 8px;
  // }

  // #menu-toggle:checked + .menu-button-container .menu-button::before {
  //   margin-top: 0px;
  //   transform: rotate(405deg);
  // }

  // #menu-toggle:checked + .menu-button-container .menu-button {
  //   background: rgba(255, 255, 255, 0);
  // }

  // #menu-toggle:checked + .menu-button-container .menu-button::after {
  //   margin-top: 0px;
  //   transform: rotate(-405deg);
  // }

  // @media (max-width: 700px) {
  //   .menu-button-container {
  //     display: flex;
  //     height: 21px;
  //     width: 35px;
  //   }
  //   .menu {
  //     position: absolute;
  //     top: 12px;
  //     right: 1rem;
  //     flex-direction: column;
  //     justify-content: center;
  //     align-items: end;
  //     width: 220px;
  //     margin-top: 2rem;
  //     z-index: 999;

  //     & .notification {
  //       right: -7px;
  //     }

  //     & :first-child {
  //       border-top-left-radius: 10px;
  //       border-top-right-radius: 10px;
  //     }

  //     & :last-child {
  //       border-bottom-left-radius: 10px;
  //       border-bottom-right-radius: 10px;
  //     }
  //   }

  //   .menu > ul.menu {
  //     margin-top: 3rem;
  //     top: 0;
  //   }
  //   .menu > li {
  //     font-size: 1.8rem;
  //   }

  //   .menu ul.menu li .icon {
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //     width: 25px;
  //   }
  //   #menu-toggle ~ .menu li {
  //     height: 0;
  //     margin: 0;
  //     padding: 0;
  //     border: 0;
  //     transition: height 800ms cubic-bezier(0.23, 1, 0.32, 1);
  //   }
  //   #menu-toggle:checked ~ .menu li {
  //     border: 1px solid #333;
  //     height: 2.5em;
  //     padding: 1.5rem;
  //     transition: height 800ms cubic-bezier(0.23, 1, 0.32, 1);
  //   }
  //   .menu > li {
  //     display: flex;
  //     justify-content: space-between;
  //     margin: 0;
  //     padding: 0.5em 0;
  //     width: 100%;
  //     color: white;
  //     background-color: #222;
  //     border-radius: 0;
  //   }
  //   .menu > li:not(:last-child) {
  //     border-bottom: 1px solid #444;
  //   }
  // }
}

// TEST MENU

.menu-container {
  // min-height: 100vh;
  background-size: cover;
  background-position: center;
  position: relative;

  // height: 50px;
  // width: 59px;
  & .notification {
    position: absolute;
    top: -2px;
    right: -6px;
    background-color: green;
    border-radius: 50%;
    font-size: 1.4rem;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  & .menu-header {
    display: flex;
    justify-content: start;
    align-items: center;
    color: #f7931a;
    border-bottom: 1px solid gray;
    font-size: 1.8rem;

    img {
      width: 30px;
      margin: 12px;
    }
  }
}

.side-bar {
  background: #1c1c1c;
  backdrop-filter: blur(15px);
  width: 65vw;
  height: 100dvh;
  position: fixed;
  top: env(safe-area-inset-top, 20px);
  right: -103%;
  overflow-y: auto;
  transition: 0.4s ease-in-out;
  transition-property: right;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}
.side-bar::-webkit-scrollbar {
  width: 0px;
}

.side-bar.active {
  right: 0;
}
h1 {
  text-align: center;
  font-weight: 500;
  font-size: 25px;
  padding-bottom: 13px;
  font-family: sans-serif;
  letter-spacing: 2px;
}

.side-bar .menu {
  width: 100%;
  margin-top: 10px;
}

.side-bar .menu .item {
  position: relative;
  cursor: pointer;

  &.stock-tracker {
    background: #3c3c3c;
    margin: 0.5rem;
    border-radius: 12px;
    a {
      padding: 5px 15px;
      .icon {
        margin-right: 5px;
        margin-left: -2px;
        img {
          margin: 0;
          width: 25px;
        }
      }

      i {
        margin-left: 7px;
      }
    }
  }
}

.side-bar .menu .item a {
  color: #fff;
  font-size: 1.6rem;
  text-decoration: none;
  display: block;
  padding: 5px 25px;
  line-height: 40px;
  display: flex;
  align-items: center;

  & .notificationNews {
    margin-left: 1rem;
    background-color: green;
    border-radius: 50%;
    font-size: 1.4rem;
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  & .icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 35px;
  }
}

.side-bar .menu .item:not(:first-child) a:hover {
  background: #33363a;
  transition: 0.3s ease;
}

.side-bar .menu .item i {
  margin-right: 15px;
}

.side-bar .menu .item a .dropdown {
  position: absolute;
  right: 0;
  margin: 20px;
  transition: 0.3s ease;
}

.side-bar .menu .item .sub-menu {
  background: #262627;
  display: none;
}

.side-bar .menu .item .sub-menu a {
  padding-left: 80px;
}

.rotate {
  transform: rotate(90deg);
}

.close-btn {
  position: absolute;
  color: #8d8d8d;
  font-size: 23px;
  right: 0px;
  margin: 15px;
  cursor: pointer;
}

.menu-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 25px;
  cursor: pointer;
}

.main {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50px;
}

.main h1 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 60px;
  text-align: center;
  line-height: 80px;
}

@media screen and (max-width: 899px) {
  .main h1 {
    font-size: 40px;
    line-height: 60px;
  }
}

@media screen and (min-width: 900px) {
  .account-selector {
    padding: 0;

    &.title-other-pages {
      justify-content: center;
      background: #343434;
      border-radius: 10px;
      // margin: 0 11%;
    }
  }
  .menu-container {
    display: none;

    & .side-bar {
      width: 35vw;
      max-width: 400px;
    }
  }
}

img {
  width: 40px;
  margin: 15px;
  border-radius: 50%;
  margin-left: 2rem;
  // border: 1px solid #d8d9d9;
}
header {
  background: #33363a;
}
