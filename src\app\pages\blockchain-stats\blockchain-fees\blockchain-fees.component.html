<div class="crypto-list-header">
  <div class="crypto-list-header-name">Blockchain</div>
  <div class="crypto-list-header-tvl">
    Fees
    @if (currentSorting == 'deposits') {
      <i
        class="fa-solid fa-chevron-down"
      ></i>
    }
  </div>
  <div class="crypto-list-header-price">Fees %</div>
</div>

@if(date == '1y'){
  @for (chain of chainsFees; track chain) {
    <div class="crypto">
      @if(!!chain.total1y && chain.total1y > 499999 && !!chain.tokenSymbol){
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="chain?.img" />
          </div>
          <div class="crypto-list-table-name">
            {{ chain?.name }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ chain?.tokenSymbol }}
          </div>
          <div class="crypto-list-table-tvl">
            {{ chain?.total1y | shortNumber }}
          </div>
          <div class="crypto-list-table-price">
            <div>
              {{ (chain?.total1y / chainsTotalFees) * 100 | profitsPerc }}
            </div>
          </div>
        </div>
      }
    </div>
  }
  } @if(date == '30d') {
  @for (chain of chainsFees; track chain) {
    <div class="crypto">
      @if(!!chain.total30d && chain.total30d > 99999 && !!chain.tokenSymbol){
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="chain?.img" />
          </div>
          <div class="crypto-list-table-name">
            {{ chain?.name }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ chain?.tokenSymbol }}
          </div>
          <div class="crypto-list-table-tvl">
            {{ chain?.total30d | shortNumber }}
          </div>
          <div class="crypto-list-table-price">
            <div>
              {{ (chain?.total30d / chainsTotalFees) * 100 | profitsPerc }}
            </div>
          </div>
        </div>
      }
    </div>
  }
  } @if(date == '7d') {
  @for (chain of chainsFees; track chain) {
    <div class="crypto">
      @if(!!chain.total7d && chain.total7d > 99999 && !!chain.tokenSymbol){
        <div class="crypto-list-table">
          <div class="crypto-list-table-logo">
            <img [src]="chain?.img" />
          </div>
          <div class="crypto-list-table-name">
            {{ chain?.name }}
          </div>
          <div class="crypto-list-table-ticker">
            {{ chain?.tokenSymbol }}
          </div>
          <div class="crypto-list-table-tvl">
            {{ chain?.total7d | shortNumber }}
          </div>
          <div class="crypto-list-table-price">
            <div>
              {{ (chain?.total7d / chainsTotalFees) * 100 | profitsPerc }}
            </div>
          </div>
        </div>
      }
    </div>
  }
}
