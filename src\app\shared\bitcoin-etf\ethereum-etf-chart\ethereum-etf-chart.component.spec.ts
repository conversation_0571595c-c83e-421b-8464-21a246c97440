import { ComponentFixture, TestBed } from '@angular/core/testing';

import { EthereumEtfChartComponent } from './ethereum-etf-chart.component';

describe('EthereumEtfChartComponent', () => {
  let component: EthereumEtfChartComponent;
  let fixture: ComponentFixture<EthereumEtfChartComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [EthereumEtfChartComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(EthereumEtfChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
