import { HttpClient } from '@angular/common/http';
import { Component, signal } from '@angular/core';
import { concatMap, tap } from 'rxjs';
import { DefillamaService } from 'src/app/core/services/http/defillama.service';

@Component({
  selector: 'app-blockchain-stats',
  templateUrl: './blockchain-stats.component.html',
  styleUrl: './blockchain-stats.component.scss',
})
export class BlockchainStatsComponent {
  protected currentSorting: string;
  protected chainsTvl = this.defillamaService.chainsTvl;
  protected chainsTotalTvl: number;

  protected chainsFees1y = [];
  protected chainsFees30d = [];
  protected chainsFees7d = [];
  protected chainsTotalFees1y: number;
  protected chainsTotalFees30d: number;
  protected chainsTotalFees7d: number;

  protected chainsVolume7d = [];
  protected chainsVolume30d = [];
  protected chainsTotalVolume30d: number;
  protected chainsTotalVolume7d: number;

  protected chainsProtocols = [];
  protected chainsTotalProtocols: number;

  protected chainsTvlHistory = signal<any | undefined>(undefined);

  protected currentFilter = 'tvl';
  protected loading = signal<boolean>(true);

  protected chartFilter = signal('1y');

  constructor(
    private defillamaService: DefillamaService,
    private http: HttpClient,
  ) {
    console.log('FechData Defillama', this.defillamaService.shouldFetchData());

    // console.log(
    //   'CALENDAR',
    //   coinCalendar
    //     .filter((item) => item.rank < 201 && !!item.significant_index)
    //     .sort((a, b) => {
    //       if (!a.significant_index) return 1;
    //       if (!b.significant_index) return -1;
    //       return b.significant_index - a.significant_index;
    //     })
    // );

    // console.log(
    //   'CALENDAR 2',
    //   coinCalendar
    //     .filter((item) => item.rank < 201 && !!item.upcoming)
    //     .sort((a, b) => {
    //       return b.upcoming - a.upcoming;
    //     })
    // );

    if (this.defillamaService.shouldFetchData()) {
      this.loading.set(true);

      this.defillamaService.getChainsTvlHistory().subscribe(() => {
        this.chainsTvlHistory.set(this.defillamaService.chainsTvlHistory);
        // console.log('TVLHISTORY!!!', this.chainsTvlHistory());
      });

      this.defillamaService.getChainsTvl().subscribe((data) => {
        // this.chainsTotalTvl = this.defillamaService
        //   .chainsTvl()
        //   .slice()
        //   .filter((item) => !!item.tokenSymbol)
        //   .reduce((acc, curr) => acc + curr.tvl, 0);
      });

      this.defillamaService
        .getChainsFees()
        .pipe(
          tap((data) => {
            this.chainsFees1y = this.defillamaService
              .chainsFees()
              .slice()
              .sort((a, b) => b.total1y - a.total1y);

            this.chainsTotalFees1y = this.defillamaService
              .chainsFees()
              .slice()
              .reduce((acc, curr) => acc + curr.total1y, 0);

            this.chainsFees30d = this.defillamaService
              .chainsFees()
              .slice()
              .sort((a, b) => b.total30d - a.total30d);

            this.chainsTotalFees30d = this.defillamaService
              .chainsFees()
              .slice()
              .reduce((acc, curr) => acc + curr.total30d, 0);

            this.chainsFees7d = this.defillamaService
              .chainsFees()
              .slice()
              .sort((a, b) => b.total7d - a.total7d);

            this.chainsTotalFees7d = this.defillamaService
              .chainsFees()
              .slice()
              .reduce((acc, curr) => acc + curr.total7d, 0);

            this.chainsVolume7d = this.defillamaService
              .chainsFees()
              .slice()
              .sort((a, b) => b.totalVolume7d - a.totalVolume7d);

            this.chainsTotalVolume7d = this.defillamaService
              .chainsFees()
              .slice()
              .reduce(
                (acc, curr) =>
                  acc + !!curr.totalVolume7d ? curr.totalVolume7d : acc,
                0,
              );

            this.chainsVolume30d = this.defillamaService
              .chainsFees()
              .slice()
              .sort((a, b) => b.totalVolume30d - a.totalVolume30d);

            this.chainsTotalVolume30d = this.defillamaService
              .chainsFees()
              .slice()
              .reduce(
                (acc, curr) =>
                  !!curr.totalVolume30d ? acc + curr.totalVolume30d : acc,
                0,
              );
          }),
          concatMap(() =>
            this.defillamaService.getProtocolsTvl().pipe(
              tap((data) => {
                this.chainsTotalTvl =
                  this.defillamaService.chainsTvlHistory()[
                    this.defillamaService.chainsTvlHistory().length - 1
                  ]?.tvl;
                // console.log('QUI', this.defillamaService.chainsTvl());
                this.chainsProtocols = this.defillamaService
                  .chainsTvl()
                  .slice()
                  .sort((a, b) => {
                    if (!a.protocols) return 1;
                    if (!b.protocols) return -1;
                    return b.protocols - a.protocols;
                  });

                this.chainsTotalProtocols = this.defillamaService
                  .chainsTvl()
                  .slice()
                  .reduce((acc, curr) => acc + curr.protocols, 0);

                this.chainsProtocols.forEach(
                  (item) =>
                    (item.img = this.defillamaService.chainsImg.find(
                      (img) => img.ticker == item.tokenSymbol,
                    )?.img),
                );
              }),
            ),
          ),
        )
        .subscribe(() => {
          this.loading.set(false);
          // console.log(
          //   'FINALE',
          //   this.chainsProtocols,
          //   this.chainsTotalProtocols
          // );
        });
    }

    if (!this.defillamaService.shouldFetchData()) {
      this.chainsTvlHistory.set(this.defillamaService.chainsTvlHistory);

      this.chainsTotalTvl =
        this.defillamaService.chainsTvlHistory()[
          this.defillamaService.chainsTvlHistory().length - 1
        ].tvl;
      // this.chainsTotalTvl = this.defillamaService
      //   .chainsTvl()
      //   .slice()
      //   .filter((item) => !!item.tokenSymbol)
      //   .reduce((acc, curr) => acc + curr.tvl, 0);

      this.chainsFees1y = this.defillamaService
        .chainsFees()
        .slice()
        .sort((a, b) => b.total1y - a.total1y);

      this.chainsTotalFees1y = this.defillamaService
        .chainsFees()
        .slice()
        .reduce((acc, curr) => acc + curr.total1y, 0);

      this.chainsFees30d = this.defillamaService
        .chainsFees()
        .slice()
        .sort((a, b) => b.total30d - a.total30d);

      this.chainsTotalFees30d = this.defillamaService
        .chainsFees()
        .slice()
        .reduce((acc, curr) => acc + curr.total30d, 0);

      this.chainsFees7d = this.defillamaService
        .chainsFees()
        .slice()
        .sort((a, b) => b.total7d - a.total7d);

      this.chainsTotalFees7d = this.defillamaService
        .chainsFees()
        .slice()
        .reduce((acc, curr) => acc + curr.total7d, 0);

      this.chainsVolume7d = this.defillamaService
        .chainsFees()
        .slice()
        .sort((a, b) => b.totalVolume7d - a.totalVolume7d);

      this.chainsTotalVolume7d = this.defillamaService
        .chainsFees()
        .slice()
        .reduce(
          (acc, curr) =>
            acc + !!curr.totalVolume7d ? curr.totalVolume7d : acc,
          0,
        );

      this.chainsVolume30d = this.defillamaService
        .chainsFees()
        .slice()
        .sort((a, b) => b.totalVolume30d - a.totalVolume30d);

      this.chainsTotalVolume30d = this.defillamaService
        .chainsFees()
        .slice()
        .reduce(
          (acc, curr) =>
            !!curr.totalVolume30d ? acc + curr.totalVolume30d : acc,
          0,
        );

      this.chainsProtocols = this.defillamaService
        .chainsTvl()
        .slice()
        .sort((a, b) => {
          if (!a.protocols) return 1;
          if (!b.protocols) return -1;
          return b.protocols - a.protocols;
        });

      this.chainsTotalProtocols = this.defillamaService
        .chainsTvl()
        .slice()
        .reduce((acc, curr) => acc + curr.protocols, 0);

      this.chainsProtocols.forEach(
        (item) =>
          (item.img = this.defillamaService.chainsImg.find(
            (img) => img.ticker == item.tokenSymbol,
          )?.img),
      );

      this.defillamaService.getProtocolsTvl().subscribe();
      this.loading.set(false);
    }
  }

  onChartFilterChange(interval: string) {
    this.chartFilter.set(interval);
  }
}
