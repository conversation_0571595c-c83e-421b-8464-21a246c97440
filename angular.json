{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "cache": {"enabled": false}}, "newProjectRoot": "projects", "projects": {"cripto-tracker": {"architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "configurations": {"development": {"buildOptimizer": false, "extractLicenses": false, "namedChunks": true, "optimization": false, "sourceMap": true, "vendorChunk": true}, "production": {"budgets": [{"maximumError": "5mb", "maximumWarning": "3mb", "type": "initial"}, {"maximumError": "15kb", "maximumWarning": "6kb", "type": "anyComponentStyle"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}}, "defaultConfiguration": "production", "options": {"assets": ["src/favicon.ico", "src/assets"], "index": "src/index.html", "inlineStyleLanguage": "scss", "main": "src/main.ts", "ngswConfigPath": "ngsw-config.json", "outputPath": "dist/cripto-tracker", "polyfills": ["zone.js"], "scripts": ["node_modules/apexcharts/dist/apexcharts.min.js"], "serviceWorker": true, "styles": ["src/theme/variables.scss", "src/global.scss"], "tsConfig": "tsconfig.app.json"}}, "deploy": {"builder": "@angular/fire:deploy", "configurations": {"development": {"buildTarget": "cripto-tracker:build:development", "proxyConfig": "./src/proxy.conf.json", "serveTarget": "cripto-tracker:serve:development"}, "production": {"buildTarget": "cripto-tracker:build:production", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "serveTarget": "cripto-tracker:serve:production"}}, "defaultConfiguration": "production", "options": {"version": 2}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "cripto-tracker:build"}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"development": {"buildTarget": "cripto-tracker:build:development"}, "production": {"buildTarget": "cripto-tracker:build:production"}}, "defaultConfiguration": "development"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"assets": ["src/favicon.ico", "src/assets"], "inlineStyleLanguage": "scss", "polyfills": ["zone.js", "zone.js/testing"], "scripts": [], "styles": ["src/theme/variables.scss", "src/global.scss"], "tsConfig": "tsconfig.spec.json"}}}, "prefix": "app", "projectType": "application", "root": "", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "sourceRoot": "src"}}, "version": 1}